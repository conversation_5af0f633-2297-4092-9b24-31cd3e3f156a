#!/bin/bash

# Function to generate a random string of specified length
generate_random_string() {
  local length=$1
  cat /dev/urandom | LC_ALL=C tr -dc 'a-zA-Z0-9' | fold -w $length | head -n 1
}

# Set end time (current time + 60 seconds)
end_time=$(($(date +%s) + 60))

echo "Starting to send messages for 1 minute..."
count=0

# Loop until we reach the end time
while [ $(date +%s) -lt $end_time ]; do
  # Generate random values
  random_device_id=$(generate_random_string 10)
  random_user_id=$(generate_random_string 8)
  random_session_id=$(generate_random_string 12)
  random_user_agent=$(generate_random_string 15)
  random_location=$((RANDOM % 100 + 1))
  
  # Create JSON message with random values
  json_message=$(cat << EOF
{
  "header": {
    "timestamp": {
      "seconds": $(date +%s)
    },
    "ingestionTime": {
      "seconds": $(date +%s)
    },
    "deviceId": {
      "value": "${random_device_id}"
    },
    "userId": {
      "value": "${random_user_id}"
    },
    "sessionId": {
      "value": "${random_session_id}"
    },
    "userAgent": {
      "value": "${random_user_agent}"
    },
    "location": {
      "value": ${random_location}
    }
  },
  "url": "test-url-${count}",
  "table": "karma_tracking",
  "payload": {
    "eventName": "ZOMATO_COD_${count}"
  }
}
EOF
)

  # Send the message to Kafka
  echo "Sending message #$((count+1))..."
  echo "$json_message" | tr -d '\n' | docker exec -i kafka kafka-console-producer --topic test.jumbov2events --bootstrap-server localhost:9092 > /dev/null
  
  # Increment counter
  count=$((count+1))
  
  # Sleep for a short time to avoid flooding
  sleep 0.5
done

echo "Completed sending $count messages over 1 minute"
