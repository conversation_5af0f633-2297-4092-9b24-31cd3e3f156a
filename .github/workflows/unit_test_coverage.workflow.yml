name: Patch Test Coverage
run-name: Generating test coverage 🚀
on: 
  pull_request:
    types:
      - opened
      - reopened
      - synchronize
      - closed
    branches: 
      - master
jobs:
  coverage_report:
    if: github.event.pull_request.merged == true
    uses: Zomato/github-actions/.github/workflows/go_unit_test_coverage.yml@master
    with:
      enabled: true
      upload: true
      threshold: 80
  coverage:
    if: github.event.pull_request.state == 'open'
    uses: Zomato/github-actions/.github/workflows/go_unit_test_coverage.yml@master
    with:
      enabled: true
      upload: false
      threshold: 80
