version: "2.3"

services:
  flash-gateway:
    build:
      context: .
    container_name: flash-gateway
    command: ./flash-gateway-linux-amd64 -jobtype=jumbo-v2-consumer
    restart: unless-stopped
    environment:
      - WEB_APP=host.docker.internal
      - CONFIG_SOURCE=local
    networks:
      - flash-gateway-net
    depends_on:
      kafka:
        condition: service_healthy

  kafka:
    hostname: kafka
    image: confluentinc/cp-kafka:6.2.1
    container_name: kafka
    depends_on:
      - zookeeper
    networks:
      - flash-gateway-net
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "nc", "-vz", "localhost", "9092"]
      interval: 10s
      timeout: 10s
      retries: 5
      start_period: 10s
    ports:
      - "29092:29092"
      - "9092:9092"
      - "9101:9101"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: 'zookeeper:2181'
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_NUM_PARTITIONS: 3
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: "true"
      KAFKA_JMX_PORT: 9101
      KAFKA_JMX_HOSTNAME: localhost

  zookeeper:
    image: confluentinc/cp-zookeeper:6.2.1
    hostname: zookeeper
    container_name: zookeeper
    restart: unless-stopped
    ports:
      - "2181:2181"
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - flash-gateway-net

  statsd-exporter:
    hostname: statsd-exporter
    container_name: statsd-exporter
    image: prom/statsd-exporter:v0.16.0
    volumes:
      - ./configs/prometheus/statsd-mapping.yml:/etc/prometheus/statsd-mapping.yml
    command:
      - --statsd.mapping-config=/etc/prometheus/statsd-mapping.yml
    ports:
      - 9102:9102
      - 9125:9125/udp
    restart: unless-stopped
    networks:
      - flash-gateway-net

  prometheus:
    hostname: prometheus
    image: prom/prometheus:v2.26.0
    container_name: prometheus
    volumes:
      - ./configs/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - "--config.file=/etc/prometheus/prometheus.yml"
      - "--storage.tsdb.path=/prometheus"
      - "--web.console.libraries=/etc/prometheus/console_libraries"
      - "--web.console.templates=/etc/prometheus/consoles"
      - "--storage.tsdb.retention.time=168h"
      - "--web.enable-lifecycle"
      - "--web.enable-admin-api"
    ports:
      - 9090
    restart: unless-stopped
    networks:
      - flash-gateway-net

networks:
  flash-gateway-net:
    name: flash-gateway-net

volumes:
  prometheus_data: {}
