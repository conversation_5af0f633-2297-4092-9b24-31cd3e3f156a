UNAME := $(shell uname)
SERVICE_NAME := flash-gateway
PROTOC_DOCKER_IMAGE := public.ecr.aws/zomato/zomato/protoc-gen:v3.2

# It is not necessary that PWD env variable will be present,
# it depends on how make command is being called
# hence to be on the safe side get it using shell command
# https://jerome-wang.github.io/2015/08/13/pwd-in-sudo-make/
PWD := $(shell pwd)

PROTO_PATH := proto/gateway
GO_PROTO_PATH := $(PWD)/client/golang/flash-gateway
PHP_PROTO_PATH := $(PWD)/client/php/lib

ifeq ($(UNAME), Darwin)
	MD5 = "md5 -q"
else
	MD5 = "md5sum"
endif

MD5COMMAND:="tar --exclude *.pb.go --exclude *.md5 -cf build/cache/proto.tar proto; ${MD5} build/cache/proto.tar"
OLD_PROTO_HASH:=$(shell mkdir -p build/cache;touch build/cache/proto.md5;cat build/cache/proto.md5)
NEW_PROTO_HASH:=$(shell eval ${MD5COMMAND})

.PHONY: compile_protos compile_go_protos compile_php_protos dev build clean setup gofmt

compile_protos:
# Caching the hash of proto lets us skip recompilation of proto files
# this helps in avoiding clearing of docker cache on each build
ifneq (${OLD_PROTO_HASH},${NEW_PROTO_HASH})
	@echo "Regenerating proto stubs"
	make compile_go_protos
	make compile_php_protos
	echo ${NEW_PROTO_HASH} > build/cache/proto.md5
endif

compile_go_protos:
	mkdir -p $(GO_PROTO_PATH)
	docker run --rm \
		-v $(PWD):$(PWD) \
		-w $(PWD) \
		$(PROTOC_DOCKER_IMAGE) \
		--go_out=$(GO_PROTO_PATH) \
		--go-grpc_out=$(GO_PROTO_PATH) \
		--go_opt=paths=source_relative \
		--go-grpc_opt=require_unimplemented_servers=false,paths=source_relative \
		-Iproto \
		$(shell find $(PROTO_PATH) -name '*.proto')

compile_php_protos:
	mkdir -p $(PHP_PROTO_PATH)
	docker run --rm \
		-v $(PWD):$(PWD) \
		-w $(PWD) \
		$(PROTOC_DOCKER_IMAGE) \
		--php_out=$(PHP_PROTO_PATH) \
		--plugin=protoc-gen-grpc=/usr/bin/grpc_php_plugin \
		-Iproto \
		$(shell find $(PROTO_PATH) -name '*.proto')

# start the local dev setup using docker-compose
dev:
	docker-compose -f docker-compose.yml up -d --build

build: 
	make compile_protos
	docker build -t latest-dev .

clean:
	docker ps -q --filter "name=${SERVICE_NAME}" | xargs docker container stop
	rm -f ${SERVICE_NAME}
	rm -rf build/cache/*

setup:
	make compile_protos
	make composer

gofmt:
	find . -type f -name "*.go" -exec go fmt {} \;

