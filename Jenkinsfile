pipeline {
    agent { label "production" }

    environment {
        GIT_BRANCH_NAME = ""
        GIT_COMMIT_MESSAGE = ""
        GIT_COMMIT_AUTHOR = "ZTravisBot <<EMAIL>>"
        GIT_CREDENTIAL_ID = "321f1026-8745-4ad2-85b9-5ffd997a56e6"
        IS_TAG = "false"

        GO_CLIENT = "/tmp/go-client"
        PHP_CLIENT = "/tmp/php-client"
    }

    stages {
        stage("Setup env") {
            steps {
                script {
                    GIT_BRANCH_NAME = env.GIT_BRANCH.replaceAll("(.*)/", "")

                    GIT_COMMIT_MESSAGE = sh (
                        script: "git log --format=format:%s -1",
                        returnStdout: true
                    ).trim()

                    if (GIT_BRANCH_NAME =~ /v\d+\.\d+\..*/) {
                        IS_TAG = "true"
                    }
                }
            }
        }

        stage("Client generation") {
            steps {
                withAWS(profile:"JUMBO-ACCOUNT") {
                    script {
                        // setup directory for php protos
                        sh "mkdir -p client/php/lib"

                        // generate protos
                        sh "\$(aws ecr get-login --no-include-email --region ap-southeast-1)"
                        sh "make compile_protos"
                    }
                }
            }
        }

        stage("Clone clients") {
            steps {
                script {
                    // if tag released then switch to master and push tags from there
                    def branch = GIT_BRANCH_NAME
                    if (IS_TAG == "true") {
                        branch = "master"
                    }

                    dir(GO_CLIENT) {
                        git branch: branch,
                            changelog: false,
                            poll: false,
                            credentialsId: GIT_CREDENTIAL_ID,
                            url: "**************:Zomato/event-gateway-service-client-golang.git"
                    }

                    dir(PHP_CLIENT) {
                        git branch: branch,
                            changelog: false,
                            poll: false,
                            credentialsId: GIT_CREDENTIAL_ID,
                            url: "**************:Zomato/event-gateway-service-client-php.git"
                    }
                }
            }
        }

        // Push only when we are pushing to branch (master/dev) not when releasing tag
        // In case of releasing tag, we take the latest branch and release a tag
        stage("Push clients") {
            when {
                expression { return !IS_TAG.toBoolean() }
            }

            steps {
                script {
                    def commit_url = "https://github.com/Zomato/flash-gateway/commit/${env.GIT_COMMIT}"

                    sh "cp proto/gateway/gateway.pb.go ${GO_CLIENT}/client/client.pb.go"
                    dir(GO_CLIENT) {
                        sshagent([GIT_CREDENTIAL_ID]) {
                            sh """
                                git add -A
                                git diff-index --quiet HEAD || git commit -m \"[AUTO GENERATED] Updated client to ${commit_url}\" -m \"Commit Message: ${GIT_COMMIT_MESSAGE}\" -m \"Pushed by ${GIT_COMMIT_AUTHOR}\"
                                git push origin ${GIT_BRANCH_NAME}
                            """
                        }
                    }

                    sh "cp -r client/php ${PHP_CLIENT}/"
                    dir(PHP_CLIENT) {
                        sshagent([GIT_CREDENTIAL_ID]) {
                            sh """
                                git add -A
                                git diff-index --quiet HEAD || git commit -m \"[AUTO GENERATED] updated client to ${commit_url}\" -m \"Commit: ${GIT_COMMIT_MESSAGE}\" -m \"Pushed by ${GIT_COMMIT_AUTHOR}\"
                                git push origin ${GIT_BRANCH_NAME}
                            """
                        }
                    }
                }
            }
        }

        // // Only trigger when tag detected e.g. v0.0.1-post, v0.2.0, v0.0.1-stag
        // // push tag should be same as gateway, maintains consistency
        stage("Release clients") {
            when {
                expression { return IS_TAG.toBoolean() }
            }

            steps {
                script {
                    dir(GO_CLIENT) {
                        sshagent([GIT_CREDENTIAL_ID]) {
                            sh """
                            if git tag ${GIT_BRANCH_NAME} -a -m \"[AUTO GENERATED TAG] ${GIT_BRANCH_NAME}\"; then
                                git push origin ${GIT_BRANCH_NAME}
                            fi
                            """
                        }
                    }

                    dir(PHP_CLIENT) {
                        sshagent([GIT_CREDENTIAL_ID]) {
                            sh """
                            if git tag ${GIT_BRANCH_NAME} -a -m \"[AUTO GENERATED TAG] ${GIT_BRANCH_NAME}\"; then
                                git push origin ${GIT_BRANCH_NAME}
                            fi
                            """
                        }
                    }
                }
            }
        }

        stage("Deploy") {
            steps {
                script {
                    def image_prefix = GIT_BRANCH_NAME

                    if (IS_TAG.toBoolean() && !GIT_BRANCH_NAME.contains("dev")) {
                        image_prefix = "production"
                    }

                    // write .netrc file
                    withCredentials([usernamePassword(credentialsId: '76bb1b7f-38cd-4b28-93c6-0c6cbf32c1d8', passwordVariable: 'pass', usernameVariable: 'user')]) {
                        sh "echo 'machine github.com login ${user} password ${pass}' > .netrc"
                    }

                    sh "echo '[{\"name\": \"${image_prefix}-flash-gateway\",\"imageUri\": \"************.dkr.ecr.ap-southeast-1.amazonaws.com/zomato/flash-gateway:${GIT_BRANCH_NAME}\"}]' > imagedefinitions.json"

                    // remove if the zip is already existing
                    def exists = fileExists "flash-gateway-${image_prefix}.zip"
                    if (exists) {
                        sh "rm flash-gateway-${image_prefix}.zip"
                    }

                    // create zip file to be uploaded
                    zip zipFile: "flash-gateway-${image_prefix}.zip", archive: false, dir: '', glob: 'imagedefinitions.json'

                    // upload zip to s3
                    // push image to ecr
                    withAWS(profile:'JUMBO-ACCOUNT') {
                        s3Upload(file: "flash-gateway-${image_prefix}.zip", bucket:'zanalytics', path:"codebuild-service-definitions/flash-gateway-${image_prefix}.zip")

                        sh "git config --global url.ssh://**************/.insteadOf https://github.com/"
                        sh "\$(aws ecr get-login --no-include-email --region ap-southeast-1)"
                        sh "docker build -t zomato/flash-gateway:${GIT_BRANCH_NAME} ."
                        sh "docker tag zomato/flash-gateway:${GIT_BRANCH_NAME} ************.dkr.ecr.ap-southeast-1.amazonaws.com/zomato/flash-gateway:${GIT_BRANCH_NAME}"
                        sh "docker push ************.dkr.ecr.ap-southeast-1.amazonaws.com/zomato/flash-gateway:${GIT_BRANCH_NAME}"
                    }

                    // clean up
                    sh "rm flash-gateway-${image_prefix}.zip"
                    sh "rm .netrc"
                }
            }
        }

        stage("Cleanup") {
            steps {
                script {
                    sh """
                        rm -rf ${GO_CLIENT}
                        rm -rf ${PHP_CLIENT}
                        sudo rm -rf client
                    """
                }
            }
        }
    }
}