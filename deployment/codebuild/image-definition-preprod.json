{"services": [{"ecsClusterName": "stag-worker-cluster", "ecsServiceName": "dev-flash-gateway-flash-consumer", "ssmParameterPathPrefix": "/services/flash-gateway/stag/", "containers": [{"image": "<container-image>", "name": "dev-flash-gateway-flash-consumer-container", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/stag/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/stag/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/stag/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/stag/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/stag/SRE_GO_RUNTIME_ENABLED"}}]}, {"ecsClusterName": "stag-worker-cluster", "ecsServiceName": "dev-flash-gateway-jumbo-consumer-backend", "ssmParameterPathPrefix": "/services/flash-gateway/stag/", "containers": [{"image": "<container-image>", "name": "dev-flash-gateway-jumbo-consumer-container", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/stag/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/stag/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/stag/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/stag/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/stag/SRE_GO_RUNTIME_ENABLED"}}]}, {"ecsClusterName": "stag-worker-cluster", "ecsServiceName": "dev-flash-gateway-jumbo-consumer-frontend", "ssmParameterPathPrefix": "/services/flash-gateway/stag/", "containers": [{"image": "<container-image>", "name": "dev-flash-gateway-jumbo-consumer-container", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/stag/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/stag/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/stag/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/stag/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/stag/SRE_GO_RUNTIME_ENABLED"}}]}, {"ecsClusterName": "stag-worker-cluster", "ecsServiceName": "dev-flash-gateway-jumbo-backend-consumer", "ssmParameterPathPrefix": "/services/flash-gateway/stag/", "containers": [{"image": "<container-image>", "name": "dev-flash-gateway-jumbo-consumer-container", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/stag/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/stag/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/stag/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/stag/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/stag/SRE_GO_RUNTIME_ENABLED"}}]}, {"ecsClusterName": "stag-worker-cluster", "ecsServiceName": "dev-flash-gateway-proto-conv-backend", "ssmParameterPathPrefix": "/services/flash-gateway/stag/", "containers": [{"image": "<container-image>", "name": "dev-flash-gateway-web-worker-container", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/stag/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/stag/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/stag/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/stag/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/stag/SRE_GO_RUNTIME_ENABLED"}}]}, {"ecsClusterName": "stag-worker-cluster", "ecsServiceName": "dev-flash-gateway-proto-conv-frontend", "ssmParameterPathPrefix": "/services/flash-gateway/stag/", "containers": [{"image": "<container-image>", "name": "dev-flash-gateway-web-worker-container", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/stag/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/stag/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/stag/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/stag/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/stag/SRE_GO_RUNTIME_ENABLED"}}]}, {"ecsClusterName": "stag-worker-cluster", "ecsServiceName": "dev-flash-gateway-blinkit-proto-conv-backend", "ssmParameterPathPrefix": "/services/flash-gateway/stag/", "containers": [{"image": "<container-image>", "name": "dev-flash-gateway-gateway-web-proto-conv-container", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/stag/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/stag/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/stag/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/stag/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/stag/SRE_GO_RUNTIME_ENABLED"}}]}, {"ecsClusterName": "stag-worker-cluster", "ecsServiceName": "dev-flash-gateway-blinkit-proto-conv-frontend", "ssmParameterPathPrefix": "/services/flash-gateway/stag/", "containers": [{"image": "<container-image>", "name": "dev-flash-gateway-gateway-web-proto-conv-container", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/stag/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/stag/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/stag/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/stag/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/stag/SRE_GO_RUNTIME_ENABLED"}}]}, {"ecsClusterName": "stag-worker-cluster", "ecsServiceName": "dev-composite-order-consumer", "ssmParameterPathPrefix": "/services/flash-gateway/stag/", "containers": [{"image": "<container-image>", "name": "dev-flash-gateway-composite-order-container", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/stag/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/stag/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/stag/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/stag/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/stag/SRE_GO_RUNTIME_ENABLED"}}]}, {"ecsClusterName": "stag-worker-cluster", "ecsServiceName": "dev-flash-gateway-blinkit-jumbo-consumer-frontend", "ssmParameterPathPrefix": "/services/flash-gateway/stag/", "containers": [{"image": "<container-image>", "name": "dev-flash-gateway-blinkit-jumbo-consumer-container", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/stag/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/stag/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/stag/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/stag/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/stag/SRE_GO_RUNTIME_ENABLED"}}]}, {"ecsClusterName": "stag-worker-cluster", "ecsServiceName": "dev-flash-gateway-blinkit-jumbo-consumer-backend", "ssmParameterPathPrefix": "/services/flash-gateway/stag/", "containers": [{"image": "<container-image>", "name": "dev-flash-gateway-blinkit-jumbo-consumer-container", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/stag/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/stag/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/stag/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/stag/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/stag/SRE_GO_RUNTIME_ENABLED"}}]}, {"ecsClusterName": "stag-worker-cluster", "ecsServiceName": "dev-flash-gateway-enrichment-ingestor", "ssmParameterPathPrefix": "/services/flash-gateway/stag/", "containers": [{"image": "<container-image>", "name": "dev-flash-gateway-enrichment-ingestor-container", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/stag/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/stag/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/stag/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/stag/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/stag/SRE_GO_RUNTIME_ENABLED"}}]}, {"ecsClusterName": "stag-worker-cluster", "ecsServiceName": "dev-flash-gateway-blinkit-clevertap-consumer", "ssmParameterPathPrefix": "/services/flash-gateway/stag/", "containers": [{"image": "<container-image>", "name": "dev-flash-gateway-clevertap-blinkit-consumer-container", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/stag/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/stag/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/stag/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/stag/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/stag/SRE_GO_RUNTIME_ENABLED"}}]}, {"ecsClusterName": "stag-worker-cluster", "ecsServiceName": "dev-flash-gateway-bistro-clevertap-consumer", "ssmParameterPathPrefix": "/services/flash-gateway/stag/", "containers": [{"image": "<container-image>", "name": "dev-flash-gateway-clevertap-bistro-consumer-container", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/stag/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/stag/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/stag/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/stag/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/stag/SRE_GO_RUNTIME_ENABLED"}}]}, {"ecsClusterName": "stag-worker-cluster", "ecsServiceName": "dev-jumbov2eventconsumer", "ssmParameterPathPrefix": "/services/flash-gateway/stag/", "containers": [{"image": "<container-image>", "name": "dev-jumbov2eventconsumer-container", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/stag/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/stag/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/stag/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/stag/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/stag/SRE_GO_RUNTIME_ENABLED"}}]}, {"ecsClusterName": "stag-worker-cluster", "ecsServiceName": "dev-blinkit-jumbo-v2-event-consumer", "ssmParameterPathPrefix": "/services/flash-gateway/stag/", "containers": [{"image": "<container-image>", "name": "dev-blinkitjumbov2eventconsumer-container", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/stag/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/stag/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/stag/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/stag/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/stag/SRE_GO_RUNTIME_ENABLED"}}]}, {"ecsClusterName": "stag-worker-cluster", "ecsServiceName": "dev-flash-gateway-district-proto-conv", "ssmParameterPathPrefix": "/services/flash-gateway/stag/", "containers": [{"image": "<container-image>", "name": "dev-flash-gateway-gateway-district-proto-conv-container", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/stag/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/stag/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/stag/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/stag/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/stag/SRE_GO_RUNTIME_ENABLED"}}]}, {"ecsClusterName": "stag-worker-cluster", "ecsServiceName": "district-dev-flash-gateway-jumbo-consumer", "ssmParameterPathPrefix": "/services/flash-gateway/stag/", "containers": [{"image": "<container-image>", "name": "district-dev-flash-gateway-jumbo-consumer-container", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/stag/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/stag/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/stag/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/stag/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/stag/SRE_GO_RUNTIME_ENABLED"}}]}, {"ecsClusterName": "stag-worker-cluster", "ecsServiceName": "district-dev-jumbo-v2-event-consumer", "ssmParameterPathPrefix": "/services/flash-gateway/stag/", "containers": [{"image": "<container-image>", "name": "district-dev-jumbo-v2-event-consumer", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/stag/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/stag/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/stag/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/stag/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/stag/SRE_GO_RUNTIME_ENABLED"}}]}, {"ecsClusterName": "stag-worker-cluster", "ecsServiceName": "dev-flash-gateway-feedingindia-proto-conv", "ssmParameterPathPrefix": "/services/flash-gateway/stag/", "containers": [{"image": "<container-image>", "name": "dev-flash-gateway-feedingindia-proto-conv-container", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/stag/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/stag/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/stag/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/stag/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/stag/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/stag/SRE_GO_RUNTIME_ENABLED"}}]}], "serviceName": "flash-gateway", "appconfigEnvironment": "preprod", "disableConfigInjection": true}