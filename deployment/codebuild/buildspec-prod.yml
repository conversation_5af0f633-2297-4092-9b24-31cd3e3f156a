version: 0.2

env:
  variables:
    GITHUB_REPO_NAME: "flash-gateway"
    GITHUB_API_BASE: "https://api.github.com/repos/Zomato/flash-gateway"
    PHP_CLIENT_REPO_URI: "github.com/Zomato/event-gateway-service-client-php.git"
    ECR_REPOSITORY_URI: "125719378300.dkr.ecr.ap-southeast-1.amazonaws.com/zomato/flash-gateway"
    DOCKER_BUILDKIT: 1

phases:
  install:
    runtime-versions:
      docker: 18
    commands:
      - echo "Starting"
      - echo "machine github.com login $GITHUB_BOT_NAME password $GITHUB_BOT_PAC" > .netrc

  pre_build:
    commands:
      - # setting git config
      - git config --global user.name $GITHUB_BOT_NAME
      - git config --global user.email $GITHUB_BOT_EMAIL

      - IMAGE_TAG=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)
      - "export RELEASE_TAG=$(curl -H 'Authorization: token '\"$GITHUB_BOT_PAC\"'' \"$GITHUB_API_BASE\"/releases/latest | jq -r  '.tag_name')"
      - "export ARTIFACT_VERSION=$(echo $RELEASE_TAG | sed 's/v//g')"

  build:
    commands:
      - $(aws ecr get-login --region $AWS_DEFAULT_REGION --no-include-email)
      - make build
      - docker build -t zomato/flash-gateway:$IMAGE_TAG .
      - docker tag zomato/flash-gateway:$IMAGE_TAG $ECR_REPOSITORY_URI:$IMAGE_TAG
      - docker push $ECR_REPOSITORY_URI:$IMAGE_TAG
      - MANIFEST=$(aws ecr batch-get-image --repository-name $ECR_REPOSITORY_NAME_DEFAULT --image-ids imageTag=$IMAGE_TAG --query 'images[].imageManifest' --output text)
      - aws ecr put-image --repository-name $ECR_REPOSITORY_NAME_DEFAULT --image-tag $RELEASE_TAG --image-manifest "$MANIFEST"

  post_build:
    commands:
      - cd $CODEBUILD_SRC_DIR
      - PHP_CLIENT_REPO_NAME=$(basename $PHP_CLIENT_REPO_URI .git)

      - # cloning php client repository
      - git clone --single-branch --branch master https://$GITHUB_BOT_NAME:$GITHUB_BOT_PAC@$PHP_CLIENT_REPO_URI

      - # remove everything from src and adding newly generated files
      - mkdir -p $PHP_CLIENT_REPO_NAME/php/lib
      - rm -rf $PHP_CLIENT_REPO_NAME/php/lib/*
      - rm -rf $PHP_CLIENT_REPO_NAME/lib/*
      - cp -r client/php/lib/* $PHP_CLIENT_REPO_NAME/php/lib/
      - cp client/php/composer.json $PHP_CLIENT_REPO_NAME/composer.json

      - # pushing latest changes
      - cd $PHP_CLIENT_REPO_NAME && git checkout -B master && git add . && git commit --allow-empty -m "[AUTO GENERATED] updated client to https://github.com/Zomato/$GITHUB_REPO_NAME/commit/$IMAGE_TAG"
      - git push -f origin master
      - git tag -a $RELEASE_TAG $(git rev-parse :/$IMAGE_TAG) -m "[AUTO GENERATED TAG] $RELEASE_TAG" && git push --tags

      ### updating image definition ####################################################
      - cd $CODEBUILD_SRC_DIR
      - sed -i "s#<container-image>#$ECR_REPOSITORY_URI_DEFAULT:$RELEASE_TAG#" $CODEBUILD_SRC_DIR/deployment/codebuild/image-definition-prod.json

artifacts:
  secondary-artifacts:
    ConfigArtifacts:
      discard-paths: yes
      files:
        - "configs/config.yaml"
    ImageDefinitionArtifacts:
      discard-paths: yes
      files:
        - "deployment/codebuild/image-definition-prod.json"
