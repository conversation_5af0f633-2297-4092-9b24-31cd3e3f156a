{"services": [{"ecsClusterName": "prod-worker-cluster", "ecsServiceName": "prod-flash-gateway-flash-consumer", "ssmParameterPathPrefix": "/services/flash-gateway/prod/", "containers": [{"image": "<container-image>", "name": "prod-flash-gateway-flash-consumer-container", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/prod/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/prod/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/prod/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/prod/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/prod/SRE_GO_RUNTIME_ENABLED"}}]}, {"ecsClusterName": "prod-worker-cluster", "ecsServiceName": "prod-flash-gateway-proto-conv-frontend", "ssmParameterPathPrefix": "/services/flash-gateway/prod/", "containers": [{"image": "<container-image>", "name": "prod-flash-gateway-web-proto-conv-container", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/prod/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/prod/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/prod/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/prod/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/prod/SRE_GO_RUNTIME_ENABLED"}}]}, {"ecsClusterName": "prod-worker-cluster", "ecsServiceName": "prod-flash-gateway-proto-conv-backend", "ssmParameterPathPrefix": "/services/flash-gateway/prod/", "containers": [{"image": "<container-image>", "name": "prod-flash-gateway-web-proto-conv-container", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/prod/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/prod/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/prod/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/prod/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/prod/SRE_GO_RUNTIME_ENABLED"}}]}, {"ecsClusterName": "prod-worker-cluster", "ecsServiceName": "prod-flash-gateway-blinkit-proto-conv-frontend", "ssmParameterPathPrefix": "/services/flash-gateway/prod/", "containers": [{"image": "<container-image>", "name": "prod-flash-gateway-web-proto-conv-container", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/prod/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/prod/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/prod/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/prod/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/prod/SRE_GO_RUNTIME_ENABLED"}}]}, {"ecsClusterName": "prod-worker-cluster", "ecsServiceName": "prod-flash-gateway-blinkit-proto-conv-backend", "ssmParameterPathPrefix": "/services/flash-gateway/prod/", "containers": [{"image": "<container-image>", "name": "prod-flash-gateway-web-proto-conv-container", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/prod/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/prod/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/prod/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/prod/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/prod/SRE_GO_RUNTIME_ENABLED"}}]}, {"ecsClusterName": "prod-worker-cluster", "ecsServiceName": "prod-flash-gateway-jumbo-v2-consumer", "ssmParameterPathPrefix": "/services/flash-gateway/prod/", "containers": [{"image": "<container-image>", "name": "prod-flash-gateway-jumbov2-consumer-container", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/prod/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/prod/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/prod/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/prod/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/prod/SRE_GO_RUNTIME_ENABLED"}}]}, {"ecsClusterName": "prod-worker-cluster", "ecsServiceName": "prod-composite-event-consumer", "ssmParameterPathPrefix": "/services/flash-gateway/prod/", "containers": [{"image": "<container-image>", "name": "prod-flash-gateway-composite-consumer-container", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/prod/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/prod/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/prod/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/prod/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/prod/SRE_GO_RUNTIME_ENABLED"}}]}, {"ecsClusterName": "prod-worker-cluster", "ecsServiceName": "prod-flash-gateway-jumbo-consumer-frontend", "ssmParameterPathPrefix": "/services/flash-gateway/prod/", "containers": [{"image": "<container-image>", "name": "prod-flash-gateway-jumbo-consumer-container", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/prod/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/prod/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/prod/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/prod/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/prod/SRE_GO_RUNTIME_ENABLED"}}]}, {"ecsClusterName": "prod-worker-cluster", "ecsServiceName": "prod-flash-gateway-jumbo-consumer-backend", "ssmParameterPathPrefix": "/services/flash-gateway/prod/", "containers": [{"image": "<container-image>", "name": "prod-flash-gateway-jumbo-consumer-container", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/prod/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/prod/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/prod/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/prod/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/prod/SRE_GO_RUNTIME_ENABLED"}}]}, {"ecsClusterName": "prod-worker-cluster", "ecsServiceName": "prod-flash-gateway-enrichment-ingestor", "ssmParameterPathPrefix": "/services/flash-gateway/prod/", "containers": [{"image": "<container-image>", "name": "prod-flash-gateway-enrichment-ingestor-container", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/prod/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/prod/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/prod/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/prod/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/prod/SRE_GO_RUNTIME_ENABLED"}}]}, {"ecsClusterName": "prod-worker-cluster", "ecsServiceName": "prod-flash-gateway-notify-alerting", "ssmParameterPathPrefix": "/services/flash-gateway/prod/", "containers": [{"image": "<container-image>", "name": "prod-flash-gateway-notify-alerting-consumer-container", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/prod/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/prod/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/prod/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/prod/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/prod/SRE_GO_RUNTIME_ENABLED"}}]}, {"ecsClusterName": "prod-worker-cluster", "ecsServiceName": "prod-flash-gateway-blinkit-jumbo-consumer-frontend", "ssmParameterPathPrefix": "/services/flash-gateway/prod/", "containers": [{"image": "<container-image>", "name": "prod-flash-gateway-blinkit-jumbo-consumer-container", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/prod/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/prod/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/prod/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/prod/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/prod/SRE_GO_RUNTIME_ENABLED"}}]}, {"ecsClusterName": "prod-worker-cluster", "ecsServiceName": "prod-flash-gateway-blinkit-jumbo-consumer-backend", "ssmParameterPathPrefix": "/services/flash-gateway/prod/", "containers": [{"image": "<container-image>", "name": "prod-flash-gateway-blinkit-jumbo-consumer-container", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/prod/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/prod/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/prod/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/prod/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/prod/SRE_GO_RUNTIME_ENABLED"}}]}, {"ecsClusterName": "prod-worker-cluster", "ecsServiceName": "prod-flash-gateway-blinkit-clevertap-consumer", "ssmParameterPathPrefix": "/services/flash-gateway/prod/", "containers": [{"image": "<container-image>", "name": "prod-flash-gateway-blinkit-clevertap-consumer-container", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/prod/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/prod/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/prod/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/prod/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/prod/SRE_GO_RUNTIME_ENABLED"}}]}, {"ecsClusterName": "prod-worker-cluster", "ecsServiceName": "prod-flash-gateway-bistro-clevertap-consumer", "ssmParameterPathPrefix": "/services/flash-gateway/prod/", "containers": [{"image": "<container-image>", "name": "prod-flash-gateway-bistro-clevertap-consumer-container", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/prod/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/prod/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/prod/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/prod/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/prod/SRE_GO_RUNTIME_ENABLED"}}]}, {"ecsClusterName": "prod-worker-cluster", "ecsServiceName": "prod-jumbov2eventconsumer", "ssmParameterPathPrefix": "/services/flash-gateway/prod/", "containers": [{"image": "<container-image>", "name": "prod-jumbov2eventconsumer-container", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/prod/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/prod/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/prod/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/prod/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/prod/SRE_GO_RUNTIME_ENABLED"}}]}, {"ecsClusterName": "prod-worker-cluster", "ecsServiceName": "prod-blinkitjumbov2eventconsumer", "ssmParameterPathPrefix": "/services/flash-gateway/prod/", "containers": [{"image": "<container-image>", "name": "prod-blinkitjumbov2eventconsumer-container", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/prod/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/prod/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/prod/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/prod/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/prod/SRE_GO_RUNTIME_ENABLED"}}]}, {"ecsClusterName": "prod-worker-cluster", "ecsServiceName": "prod-flash-gateway-district-proto-conv", "ssmParameterPathPrefix": "/services/flash-gateway/prod/", "containers": [{"image": "<container-image>", "name": "prod-flash-gateway-district-proto-conv-container", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/prod/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/prod/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/prod/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/prod/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/prod/SRE_GO_RUNTIME_ENABLED"}}]}, {"ecsClusterName": "prod-worker-cluster", "ecsServiceName": "district-prod-flash-gateway-jumbo-consumer", "ssmParameterPathPrefix": "/services/flash-gateway/prod/", "containers": [{"image": "<container-image>", "name": "district-prod-flash-gateway-jumbo-consumer-container", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/prod/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/prod/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/prod/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/prod/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/prod/SRE_GO_RUNTIME_ENABLED"}}]}, {"ecsClusterName": "prod-worker-cluster", "ecsServiceName": "district-prod-jumbo-v2-event-consumer", "ssmParameterPathPrefix": "/services/flash-gateway/prod/", "containers": [{"image": "<container-image>", "name": "district-prod-jumbo-v2-event-consumer", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/prod/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/prod/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/prod/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/prod/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/prod/SRE_GO_RUNTIME_ENABLED"}}]}, {"ecsClusterName": "prod-worker-cluster", "ecsServiceName": "prod-flash-gateway-feedingindia-proto-conv", "ssmParameterPathPrefix": "/services/flash-gateway/prod/", "containers": [{"image": "<container-image>", "name": "prod-flash-gateway-feedingindia-proto-conv-container", "ssmParameterNamePrefix": "FLASH_GATEWAY_", "ssmParameterSubstitutionMethod": "MERGE", "ssmParameters": {"SRE_GO_MEMLIMIT_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_ENABLED", "SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_TASK_MEMORY_PERCENTAGE", "SRE_GO_MEMLIMIT_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEMLIMIT_UPDATE_INTERVAL", "SRE_GO_METRICS_PAUSE_DURATION": "/sre/go/runtime/prod/SRE_GO_METRICS_PAUSE_DURATION", "SRE_GO_MEM_PROFILE_ENABLED": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_ENABLED", "SRE_GO_MEM_PROFILE_UPDATE_INTERVAL": "/sre/go/runtime/prod/SRE_GO_MEM_PROFILE_UPDATE_INTERVAL", "SRE_GOMAXPROCS_ENABLED": "/sre/go/runtime/prod/SRE_GOMAXPROCS_ENABLED", "SRE_GO_RUNTIME_GOGC": "/sre/go/runtime/prod/SRE_GO_RUNTIME_GOGC", "SRE_GO_METRICS_ENABLED": "/sre/go/runtime/prod/SRE_GO_METRICS_ENABLED", "SRE_GO_RUNTIME_ENABLED": "/sre/go/runtime/prod/SRE_GO_RUNTIME_ENABLED"}}]}], "serviceName": "flash-gateway", "appconfigEnvironment": "prod", "disableConfigInjection": true}