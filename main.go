package main

import (
	"context"
	"flag"
	"net/http"
	_ "net/http/pprof"
	"os"
	"time"

	"github.com/Zomato/flash-gateway/internal/flashgateway"
	fconfig "github.com/Zomato/flash-gateway/pkg/config"
	heart "github.com/Zomato/flash-gateway/pkg/heartbeat"
	"github.com/Zomato/flash-gateway/pkg/services/statsd"
	"github.com/Zomato/flash-gateway/pkg/signals"
	"github.com/Zomato/flash-gateway/pkg/signals/channels"
	"github.com/Zomato/flash-gateway/pkg/tracer"
	"github.com/Zomato/go/config"
	log "github.com/Zomato/go/logger"
	"github.com/Zomato/go/newrelic"
	"github.com/Zomato/go/profiler"
	zruntime "github.com/Zomato/go/runtime"
	"go.uber.org/zap"
)

var (
	isWorker = true
	jobtype  = ""
	ctx      context.Context
	err      error
)

func init() {
	setupFlags()

	// setup config
	fconfig.InitializeConfig("flash_gateway")

	config.Init()
	ctx, err = config.TODOContext()

	if err != nil {
		log.WithError(err).Error("error while getting context from appconfig")
	}
	// initialize stastd
	statsd.Init(ctx)

	// start sending heartbeat
	heart.StartBeating(ctx)

	// handle interrrupt signals
	go signals.Handler(ctx) // nolint: ignore-recover

	// enable profiling
	if config.GetBool(ctx, "profiling.enabled") {
		go func() { //nolint: ignore-recover
			err := http.ListenAndServe(":"+config.GetString(ctx, "profiling.port"), nil)
			if err != nil {

				log.Error("Couldn't start server", zap.Any("reason", err))
			}
		}()
	}

	// if config.GetBool(ctx, "profiler.enabled") {
	// 	serviceName := fmt.Sprintf(`flash-gateway-%s`, jobtype)
	// 	profiler.Initialize(
	// 		profiler.WithProfilerType(profiler.ProfilerTypePyroscope),
	// 		profiler.WithPyroscopeAuthToken(config.GetString(ctx, "profiler.credentials")),
	// 		profiler.WithFallbackAppName(serviceName),
	// 		profiler.WithGoroutineProfiling(config.GetBool(ctx, "profiler.goroutine_profile.enabled")),
	// 		profiler.WithBlockProfiling(config.GetBool(ctx, "profiler.block_profile.enabled"), 1000), // profile 1 blocking event per 1000ns spent blocked on average
	// 		profiler.WithMutexProfiling(config.GetBool(ctx, "profiler.mutex_profile.enabled"), 100),  // 100 == 1% (1/100) of mutex contention events are profiled.,
	// 		profiler.WithDisableGCRuns(config.GetBool(ctx, "profiler.gc_runs.disabled")),
	// 	)
	// }

	if config.GetBool(ctx, "datadog.profiling.enabled") {
		profiler.Initialize(
			ctx,
			profiler.WithProfilerType(profiler.ProfilerTypeDatadog), // datadog profiler
			profiler.WithGoroutineProfiling(config.GetBool(ctx, "datadog.profiling.goroutine_profile.enabled")),
		)
	}

	statsdPath := config.GetString(ctx, "statsd.url")
	zruntime.Init(
		zruntime.WithGoMemLimitEnabled(config.GetBool(ctx, "go_runtime.mem_limit.enabled")),
		zruntime.WithGCMetricsCollectorEnabled(config.GetBool(ctx, "go_runtime.metrics.enabled"), statsdPath),
		zruntime.WithGOGC(config.GetInt(ctx, "go_runtime.mem_limit.go_gc_percentage")),
	)

	newrelic.Initialize(
		&newrelic.Options{
			Name:    config.GetString(ctx, "newrelic.application"),
			License: config.GetString(ctx, "newrelic.license"),
			Enabled: config.GetBool(ctx, "newrelic.enabled"),
		},
	)
}

func main() {
	//handlers.ProtoConvertorClaimFunc(nil, nil)
	if isWorker {
		tracer.InitialiseTracer(ctx)
		flashgateway.Start(ctx, jobtype)
	}

	// Wait for everything to gracefully exit
	// if timeout, then force exit
	go func() { // nolint: ignore-recover
		select {
		case <-time.After(20 * time.Second):
			log.Info("Timeout on exiting")
			os.Exit(1)
		}
	}()

	<-channels.CompleteShutdown
	log.Debug("Successfull shutdown")
}

func setupFlags() {
	flag.StringVar(&jobtype, "jobtype", "", "Job type to start")

	var mode string
	flag.StringVar(&mode, "mode", "worker", "Mode to start: worker/grpc")
	flag.Parse()

	isWorker = (mode == "worker")
}
