package heart

import (
	"context"
	"time"

	"github.com/Zomato/flash-gateway/pkg/services/statsd"
	log "github.com/Zomato/go/logger"
)

// StartBeating pushes service heartbeat 1
func StartBeating(ctx context.Context) {
	ticker := time.NewTicker(15 * time.Second)

	go func() { //nolint: ignore-recover
		for {
			select {
			case <-ticker.C:
				log.Debug("Pushed heartbeat")
				statsd.PushHeartBeat(1, ctx)
			}
		}
	}()
}

// StopBeating pushes service heartbeat 0
func StopBeating(ctx context.Context) {
	log.Info("Stopping heartbeat")
	statsd.PushHeartBeat(0, ctx)
}
