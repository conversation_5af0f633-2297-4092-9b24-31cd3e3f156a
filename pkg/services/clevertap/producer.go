package clevertap

import (
	"context"
	"fmt"
	"io"

	ztracer "github.com/Zomato/flash-gateway/pkg/tracer"
	log "github.com/Zomato/go/logger"
	newrelic "github.com/Zomato/go/newrelic"
	"github.com/Zomato/go/tracer"
	jsoniter "github.com/json-iterator/go"
	"go.uber.org/zap"
)

var json = jsoniter.ConfigCompatibleWithStandardLibrary

const (
	fieldResponse = "Response"
	fieldPayload  = "Payload"
)

func (client *ClevertapClient) CreateUploadRequest(ctx context.Context, payload Payload) (int, error) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "CleverTap/CreateUploadRequest", tracer.WithSpanKind(tracer.SpanKindInternal))
		defer span.End()
		ctx = tctx
	}
	defer newrelic.StartSegmentWithContext(ctx, client.ConsumerName).End()

	// this should never happen due to workerpool, adding here as a generalisation
	if len(payload.Data) == 0 {
		log.
			WithField(fieldPayload, payload).
			Warn("No data to upload")
		return 0, nil
	}

	var eventsCount int = len(payload.Data)

	marshalledPayload, err := json.Marshal(payload)
	if err != nil {
		log.
			WithError(err).
			Errorf("Error in marshalling payload")
		newrelic.NoticeError(ctx, err)
		return eventsCount, err
	}
	log.Debug("Payload", zap.Any("payload", string(marshalledPayload)))

	uploadEndpoint := fmt.Sprintf("%s/1/upload", client.Config.Endpoint)
	res, err := client.NewRequest(ctx, "POST", uploadEndpoint, marshalledPayload)

	defer func() {
		if res != nil && res.Body != nil {
			res.Body.Close()
		}
	}()

	if err != nil {
		log.
			WithError(err).
			Errorf("Unable to make api call to Clevertap")
		newrelic.NoticeError(ctx, err)
		return eventsCount, err
	}

	body, err := io.ReadAll(res.Body)
	if err != nil {
		log.
			WithField(fieldResponse, res).
			WithError(err).
			Errorf("Error in reading response body from request")
		newrelic.NoticeError(ctx, err)
		return eventsCount, err
	}

	var response Response
	resErr := json.Unmarshal(body, &response)

	if resErr != nil {
		log.
			WithField(fieldResponse, string(body)).
			WithError(resErr).Errorf("Unable to parse CT response")
		newrelic.NoticeError(ctx, resErr)
		return eventsCount, resErr
	}

	if response.Status == "success" && len(response.Unprocessed) == 0 {
		return 0, nil
	} else {
		log.
			WithFields(map[string]interface{}{
				fieldResponse: string(body),
				fieldPayload:  string(marshalledPayload),
			}).Errorf("Failed to upload data to clevertap [status: %v]", response.Status)

		return len(response.Unprocessed), fmt.Errorf("error- %+v", response.Unprocessed)
	}

}

// UploadEvents accepts a slice of ClevertapEventData and uploads it to Clevertap
func (client *ClevertapClient) UploadEvents(ctx context.Context, ctEventData []ClevertapEventData) (int, error) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "CleverTap/UploadEvents", tracer.WithSpanKind(tracer.SpanKindInternal))
		defer span.End()
		ctx = tctx
	}

	Data := make([]ClevertapEventData, len(ctEventData))
	copy(Data, ctEventData)

	return client.CreateUploadRequest(ctx, Payload{Data: Data})
}
