package clevertap

import (
	"context"
	"net/http"
	"time"

	log "github.com/Zomato/go/logger"
	newrelic "github.com/Zomato/go/newrelic"
	retryablehttp "github.com/hashicorp/go-retryablehttp"
	"go.uber.org/zap"
)

type Config struct {
	AccountID string
	Passcode  string
	Endpoint  string
}

type ClevertapClient struct {
	Http         *retryablehttp.Client
	Config       *Config
	ConsumerName string
}

func NewClient(cfg *Config) *ClevertapClient {
	log.Info("Initialized new clevertap client", zap.Any("AccountID", cfg.AccountID))

	return &ClevertapClient{
		Config: cfg,
	}
}

func (client *ClevertapClient) getHeaders() http.Header {
	headers := http.Header{}

	headers.Add("Content-Type", "application/json; charset=utf-8")
	headers.Add("X-CleverTap-Account-Id", client.Config.AccountID)
	headers.Add("X-CleverTap-Passcode", client.Config.Passcode)

	return headers
}

func (client *ClevertapClient) NewRequest(ctx context.Context, method string, url string, payload interface{}) (*http.Response, error) {
	if client.Http == nil {
		client.Http = retryablehttp.NewClient()
		client.Http.HTTPClient.Timeout = time.Millisecond * 5000
		client.Http.RetryWaitMin = 500 * time.Millisecond
		client.Http.RetryWaitMax = 5 * time.Second
		client.Http.RetryMax = 3
	}

	request, err := retryablehttp.NewRequest(method, url, payload)
	if err != nil {
		log.
			WithError(err).
			Errorf("Error in creating http request")
		newrelic.NoticeError(ctx, err)
		return nil, err
	}

	request.Header = client.getHeaders()
	return client.Http.Do(request)
}
