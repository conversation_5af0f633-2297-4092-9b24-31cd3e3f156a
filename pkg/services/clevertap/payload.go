package clevertap

type DefaultClevertapEvent struct {
	Identity string `json:"identity"`
	Type     string `json:"type"`
}

type ClevertapEventData struct {
	DefaultClevertapEvent
	EventName   string                 `json:"evtName,omitempty"`
	EventData   map[string]interface{} `json:"evtData,omitempty"`
	Timestamp   int64                  `json:"ts,omitempty"`
	ProfileData map[string]interface{} `json:"profileData,omitempty"`
}

// Payload is the final payload sent to clevertap
type Payload struct {
	Data []ClevertapEventData `json:"d"`
}

// Response is the clevertap response recieved
type Response struct {
	Status      string        `json:"status"`
	Processed   int           `json:"processed"`
	Unprocessed []interface{} `json:"unprocessed"`
}
