package statsd

import (
	"context"
	"time"

	"github.com/Zomato/flash-gateway/internal/constants"

	"github.com/Zomato/go/config"
	"github.com/smira/go-statsd"

	log "github.com/Zomato/go/logger"
)

var client *statsd.Client

// Init starts the statsd client on statsd.url
func Init(ctx context.Context) {
	client = statsd.NewClient(
		config.GetString(ctx, "statsd.url"),
		statsd.MetricPrefix(config.GetString(ctx, "statsd.application")+"."),
	)
	log.Info("Initalized statsd")
}

// PushHeartBeat pushes heartbeat value
func PushHeartBeat(value int64, ctx context.Context) {
	client.Gauge("heartbeat", value, statsd.StringTag("job", config.GetString(ctx, "statsd.job")))
}

// PushPanicCount pushes heartbeat value
func PushPanicCount(ctx context.Context) {
	jobType := ctx.Value(constants.JobNameCtxKey).(string)

	client.Incr("panic_total", 1, statsd.StringTag("job", jobType))
}

// PushEvictionCount pushes heartbeat value
func PushEvictionCount(ctx context.Context) {
	client.Incr("l2_eviction_total", 1, statsd.StringTag("job", config.GetString(ctx, "statsd.job")))
}

// PushFailedMessagesCount pushes falied message count
func PushFailedMessagesCount(ctx context.Context) {
	jobType := ctx.Value(constants.JobNameCtxKey).(string)
	client.Incr("failed_messages_total", 1, statsd.StringTag("job", jobType))
}

// PushFailedMessagesCount pushes falied message count
func PushFailedMessagesCountTableLevel(ctx context.Context, table string, errorType string) {
	jobType := ctx.Value(constants.JobNameCtxKey).(string)
	client.Incr("failed_messages_total", 1, statsd.StringTag("job", jobType), statsd.StringTag("table_name", table), statsd.StringTag("error_type", errorType))
}

// PushEventLag pushes falied message count
func PushEventLag(delta time.Duration, ctx context.Context) {
	client.PrecisionTiming("event_lag", delta, statsd.StringTag("job", config.GetString(ctx, "statsd.job")))
}

// PushEventProcessingTime pushes falied message count
func PushEventProcessingTime(ctx context.Context, delta time.Duration) {
	jobType := ctx.Value(constants.JobNameCtxKey).(string)
	client.PrecisionTiming("event_processing_time", delta, statsd.StringTag("job", jobType))
}

// PushFailedEnqueueMessagesTotal pushes falied message count
func PushFailedEnqueueMessagesTotal(ctx context.Context) {
	client.Incr("failed_enqueue_messages_total", 1, statsd.StringTag("job", config.GetString(ctx, "statsd.job")))
}

// Close closes the client
func Close() {
	log.Info("Closing statsd connection")
	client.Close()
}

func RecordGaugeMetric(ctx context.Context, name string, value int64) {
	jobType := ctx.Value(constants.JobNameCtxKey).(string)
	client.Gauge(name, value, statsd.StringTag("job", jobType))
}

func RecordFGaugeMetric(ctx context.Context, name string, value float64) {
	jobType := ctx.Value(constants.JobNameCtxKey).(string)
	client.FGauge(name, value, statsd.StringTag("job", jobType))
}

func RecordIncrMetric(ctx context.Context, name string, count int64) {
	jobType := ctx.Value(constants.JobNameCtxKey).(string)
	client.Incr(name, count, statsd.StringTag("job", jobType))
}

func RecordIncrMetricWithTags(ctx context.Context, name string, count int64, tags map[string]string) {
	jobType := ctx.Value(constants.JobNameCtxKey).(string)
	statsdTags := []statsd.Tag{statsd.StringTag("job", jobType)}

	for key, value := range tags {
		statsdTags = append(statsdTags, statsd.StringTag(key, value))
	}

	client.Incr(name, count, statsdTags...)
}
