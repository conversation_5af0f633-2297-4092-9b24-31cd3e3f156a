package cache

import (
	"context"

	"github.com/Zomato/flash-gateway/internal/helpers"

	"github.com/Zomato/flash-gateway/pkg/services/cache/inmemory"
	"github.com/Zomato/flash-gateway/pkg/services/cache/redis"
	ztracer "github.com/Zomato/flash-gateway/pkg/tracer"
	"github.com/Zomato/go/config"
	log "github.com/Zomato/go/logger"
	"github.com/Zomato/go/tracer"

	"go.uber.org/zap"
)

// Cache represents cache struct
type Cache struct {
	L2             *inmemory.Client
	L1             *redis.Client
	Metrics        bool
	L2CacheKeyFunc func(keys ...string) string
}

var cache *Cache

// Initialize setups both l1 and l2 cache
func Initialize(ctx context.Context) {
	cache = &Cache{
		Metrics: config.GetBool(ctx, "cache.metrics"),
	}

	cache.initializeL1Cache(ctx)
	cache.initializeL2Cache(ctx)
}

func (c *Cache) initializeL1Cache(ctx context.Context) {
	c.L1 = redis.Initialize(ctx, c.Metrics)
}

func (c *Cache) initializeL2Cache(ctx context.Context) {
	c.L2 = inmemory.Initialize(ctx, c.Metrics)
}

// SetL2CacheKeyFunc sets dunction to compute l2 cache key
func SetL2CacheKeyFunc(f func(keys ...string) string) {
	cache.L2CacheKeyFunc = f
}

// Shutdown initiates safe shutdown for all cache levels
func (c *Cache) Shutdown() {
	if c == nil {
		return
	}

	if c.L1 != nil {
		c.L1.Cache.Close()
	}

	if c.L2 != nil {
		c.L2.Cache.Close()
	}
}

// Shutdown initiates safe shutdown for all cache levels
func Shutdown() {
	log.Info("Shutting down cache")
	cache.Shutdown()
}

// Get returns cache value
// 1. Checks in L2 cache
// 2. If found returns
// 3. If not, looks in L1 cache
// 4. If found, sets in L2 cache and returns, else return empty string
func Get(ctx context.Context, cacheStore *map[string]map[string]interface{}) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "Cache/Get", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}
	// Look in l2 cache
	for key, fields := range *cacheStore {
		for field := range fields {
			l2value, found := cache.L2.Get(ctx, cache.L2CacheKeyFunc(key, field))
			if found {
				(*cacheStore)[key][field] = l2value
			}
		}
	}

	keyFields := make(map[string][]string)
	for key, fieldMap := range *cacheStore {
		for field, value := range fieldMap {
			if value != nil {
				continue
			}

			if v, ok := keyFields[key]; ok {
				keyFields[key] = append(v, field)
			} else {
				keyFields[key] = []string{field}
			}
		}
	}

	// If everything was found in l2 return
	if len(keyFields) == 0 {
		return
	}

	for key, fields := range keyFields {
		cacheValues, err := cache.L1.HMGet(ctx, key, fields)
		if err != nil {
			log.Error("Unable to HMGET from L1 cache", zap.String("key", key), zap.Any("fields", fields))
			continue
		}

		result := make(map[string]interface{})
		i := 0
		for _, field := range fields {
			if cacheValues[i] != nil {
				result[field] = cacheValues[i]
			} else {
				result[field] = ""
			}

			cache.L2.Set(ctx, cache.L2CacheKeyFunc(key, field), result[field])

			i++
		}

		value := (*cacheStore)[key]

		(*cacheStore)[key] = *helpers.MergeMapStringInterface(&result, &value)
	}
}

// HMSet performs batched HMSet for ingesting data to L1 cache
func HMSet(ctx context.Context, pairs ...redis.HMSETPair) error {
	return cache.L1.PipedMSet(ctx, pairs)
}
