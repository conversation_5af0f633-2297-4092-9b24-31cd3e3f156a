package redis

import (
	"context"

	"github.com/Zomato/go/redis"

	goredis "github.com/redis/go-redis/v9"
)

type Pipeline struct {
	pipeliner redis.Pipeliner
}

func (p *Pipeline) Exec(ctx context.Context) error {
	_, err := p.pipeliner.ExecContext(ctx)
	return err
}

func (p *Pipeline) HMSet(context context.Context, pair HMSETPair) *goredis.BoolCmd {
	return p.pipeliner.Pipeline().HMSet(context, pair.Key, pair.Fields)
}
