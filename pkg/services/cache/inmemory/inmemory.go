package inmemory

import (
	"context"

	"github.com/Zomato/flash-gateway/pkg/services/statsd"
	ztracer "github.com/Zomato/flash-gateway/pkg/tracer"
	"github.com/Zomato/go/config"
	log "github.com/Zomato/go/logger"
	"github.com/Zomato/go/tracer"
	"github.com/dgraph-io/ristretto"

	"go.uber.org/zap"
)

type Client struct {
	*ristretto.Cache
}

// Initialize returns a new in memory cache instance
func Initialize(ctx context.Context, metrics bool) *Client {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "Cache/Initialize", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}
	cache, err := ristretto.NewCache(&ristretto.Config{
		NumCounters: config.GetInt64(ctx, "cache.l2.numcounters"),
		MaxCost:     config.GetInt64(ctx, "cache.l2.maxcost"),
		BufferItems: 64, // number of keys per Get buffer.
		Metrics:     metrics,
		OnEvict: func(key, conflict uint64, value interface{}, cost int64) {
			statsd.PushEvictionCount(ctx)
		},
	})
	if err != nil {
		log.Error("Unable to setup L2 cache", zap.Any("error", err.Error()))
	}

	log.Info("L2 cache connected")

	return &Client{cache}
}

// Get returns value from cache
func (c *Client) Get(ctx context.Context, key string) (interface{}, bool) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "Cache/Get", tracer.WithSpanKind(tracer.SpanKindInternal))
		defer span.End()
		ctx = tctx
	}
	defer func() {
		if c.Cache.Metrics != nil {
			statsd.RecordGaugeMetric(ctx, "l2_cache_hits", int64(c.Metrics.Hits()))
			statsd.RecordGaugeMetric(ctx, "l2_cache_misses", int64(c.Metrics.Misses()))
			statsd.RecordIncrMetric(ctx, "l2_cache_gets_dropped_total", int64(c.Metrics.GetsDropped()))
			statsd.RecordIncrMetric(ctx, "l2_cache_gets_kept_total", int64(c.Metrics.GetsKept()))
			statsd.RecordFGaugeMetric(ctx, "l2_cache_hit_ratio", c.Metrics.Ratio())

			c.Cache.Metrics.Clear()
		}
	}()

	return c.Cache.Get(key)
}

// Set sets value to cache
func (c *Client) Set(ctx context.Context, key string, value interface{}) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "Cache/Set", tracer.WithSpanKind(tracer.SpanKindInternal))
		defer span.End()
		ctx = tctx
	}
	defer func() {
		if c.Cache.Metrics != nil {
			statsd.RecordGaugeMetric(ctx, "l2_cache_keys_added", int64(c.Metrics.KeysAdded()))
			statsd.RecordGaugeMetric(ctx, "l2_cache_keys_updated", int64(c.Metrics.KeysUpdated()))
			statsd.RecordGaugeMetric(ctx, "l2_cache_sets_dropped", int64(c.Metrics.SetsDropped()))
			statsd.RecordGaugeMetric(ctx, "l2_cache_sets_rejected", int64(c.Metrics.SetsRejected()))
			statsd.RecordGaugeMetric(ctx, "l2_cache_cost_added", int64(c.Metrics.CostAdded()))

			c.Cache.Metrics.Clear()
		}
	}()

	c.Cache.SetWithTTL(key, value, 1, config.GetDuration(ctx, "cache.l2.ttl"))
}
