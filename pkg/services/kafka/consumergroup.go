package kafka

import (
	"github.com/Shopify/sarama"
)

// ConsumerClaimFuncType defines the claim function type
type ConsumerClaimFuncType func(session sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error

// ConsumerGroup represents a Sarama consumer group consumer
type ConsumerGroup struct {
	ready             chan bool
	Consumer          sarama.ConsumerGroup
	ConsumerClaimFunc ConsumerClaimFuncType
}

// Setup is run at the beginning of a new session, before ConsumeClaim
func (consumer *ConsumerGroup) Setup(sarama.ConsumerGroupSession) error {
	// Mark the consumer as ready
	close(consumer.ready)
	return nil
}

// Cleanup is run at the end of a session, once all ConsumeClaim goroutines have exited
func (consumer *ConsumerGroup) Cleanup(sarama.ConsumerGroupSession) error {
	return nil
}

// ConsumeClaim must start a consumer loop of ConsumerGroupClaim's Messages().
func (consumer *ConsumerGroup) ConsumeClaim(session sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
	return consumer.ConsumerClaimFunc(session, claim)
}
