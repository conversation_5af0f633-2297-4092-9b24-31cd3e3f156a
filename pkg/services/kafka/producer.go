package kafka

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/Shopify/sarama"
	"github.com/Zomato/flash-gateway/internal/helpers"
	ztracer "github.com/Zomato/flash-gateway/pkg/tracer"
	"github.com/Zomato/go/config"
	"github.com/Zomato/go/tracer"

	log "github.com/Zomato/go/logger"
	"github.com/golang/protobuf/proto"

	"go.uber.org/zap"
)

// DEFAULT_PRODUCER_NAME defines default producer label
const DEFAULT_PRODUCER_NAME = "default"

// Producer represents producer object
type Producer struct {
	Name    string
	Brokers []string

	producer sarama.AsyncProducer
	config   *sarama.Config
}

// RegisterProducers setups all brokers
func RegisterProducers(ctx context.Context, labels []string) map[string]Producer {
	producers := make(map[string]Producer)
	for _, label := range labels {
		producers[label] = NewProducer(ctx, label)
	}

	return producers
}

// NewProducer returns kafka producer object
func NewProducer(ctx context.Context, name string) Producer {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "Kafka/NewProducer", tracer.WithSpanKind(tracer.SpanKindInternal))
		defer span.End()
		ctx = tctx
	}
	producer := Producer{
		Name: name,
	}

	producer.setup(ctx)

	log.Info("Created kafka producer", zap.String("name", name), zap.Any("brokers", producer.Brokers))

	return producer
}

func (p *Producer) init(ctx context.Context) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "Kafka/Producer/Init", tracer.WithSpanKind(tracer.SpanKindInternal))
		defer span.End()
		ctx = tctx
	}
	saramaConfig := sarama.NewConfig()

	if config.GetBool(ctx, fmt.Sprintf("kafka.producers.%s.is_warpstream", p.Name)) {
		log.Info("Configuring a Warpstream producer")
		saramaConfig.Net.ReadTimeout = 60 * time.Second
		saramaConfig.Producer.Compression = sarama.CompressionLZ4
		saramaConfig.Metadata.RefreshFrequency = 60 * time.Second
		saramaConfig.Producer.MaxMessageBytes = 64_000_000
		saramaConfig.Producer.Flush.Bytes = 64_000_000
		saramaConfig.Producer.Flush.Frequency = 25 * time.Millisecond
		saramaConfig.Producer.Flush.MaxMessages = 1_000_000
	} else {
		saramaConfig.Producer.Compression = sarama.CompressionSnappy
		saramaConfig.Producer.Flush.Bytes = 5000000
		saramaConfig.Producer.Flush.Frequency = config.GetDuration(ctx, "kafka.config.producer.flushfrequency")
	}
	saramaConfig.Producer.RequiredAcks = sarama.WaitForLocal
	saramaConfig.Producer.Return.Errors = true
	saramaConfig.Producer.Return.Successes = true

	p.config = saramaConfig

	p.Brokers = strings.Split(config.GetString(ctx, "kafka.producers."+p.Name+".brokers"), ",")

	p.validate()
}

func (p *Producer) validate() {
	if len(p.Brokers) == 0 {
		log.Error("No brokers to connect to for kafka producer")

	}
}

func (p *Producer) setup(ctx context.Context) {
	p.init(ctx)

	producer, err := sarama.NewAsyncProducer(p.Brokers, p.config)
	if err != nil {
		log.Error("Error occured while creating kafka producer client", zap.Any("brokers", p.Brokers), zap.String("name", p.Name), zap.String("error", err.Error()))
	}

	p.producer = producer

	go func() { //nolint: ignore-recover
		for err := range p.producer.Errors() {
			log.Error(
				"Failed to write data to kafka topic",
				zap.Any("brokers", p.Brokers),
				zap.String("label", p.Name),
				zap.String("topic", err.Msg.Topic),
				zap.Any("value", err.Msg.Value),
				zap.Int32("partition", err.Msg.Partition),
				zap.Int64("offset", err.Msg.Offset),
				zap.Any("metadata", err.Msg.Metadata),
				zap.String("error", err.Error()),
			)
		}
	}()

	go func() { //nolint: ignore-recover
		for msg := range p.producer.Successes() {
			log.Debug(
				"Message Published successfully to kafka",
				zap.String("Topic", msg.Topic),
				zap.Int32("Partition ", msg.Partition),
				zap.Int64("Offset ", msg.Offset),
			)
		}
	}()
}

// PushStringWithSegment pushes message to given topic and records a newrelic segment
// [MIGRATION] This will replace the producer.PushString func in future
func (p *Producer) PushStringWithSegment(ctx context.Context, topic string, message string, key []byte) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "Kafka/PushStringWithSegment", tracer.WithSpanKind(tracer.SpanKindInternal))
		defer span.End()
		ctx = tctx
	}
	if topic == "" || message == "" {
		return
	}

	segment := addNewRelicKafkaSegment(ctx, "STRING_PUSH")
	defer segment.End()

	p.PushString(topic, message, key)
}

// PushString pushes string message to producer on given topic
// [MIGRATION] This func will soon be deprecated in favour of producer.PushStringWithSegment
func (p *Producer) PushString(topic string, message string, key []byte) {
	if topic == "" || message == "" {
		return
	}

	producerMessage := sarama.ProducerMessage{
		Topic: topic,
		Value: sarama.StringEncoder(message),
	}

	if key != nil {
		producerMessage.Key = sarama.ByteEncoder(key)
	}

	p.producer.Input() <- &producerMessage
}

// PublishProto will marshal the proto message and emit it to the Kafka topic.
func (p *Producer) PublishProto(ctx context.Context, topic string, message proto.Message, key []byte) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "Kafka/PublishProto", tracer.WithSpanKind(tracer.SpanKindInternal))
		defer span.End()
		ctx = tctx
	}
	segment := addNewRelicKafkaSegment(ctx, "PROTO_PUSH")
	defer segment.End()

	mb, err := proto.Marshal(message)
	if err != nil {
		log.Error("Failed to push proto message to kafka", zap.String("error", err.Error()))
		return
	}

	producerMessage := sarama.ProducerMessage{
		Topic: topic,
		Value: sarama.ByteEncoder(mb),
	}

	if key != nil {
		producerMessage.Key = sarama.ByteEncoder(key)
	}

	p.producer.Input() <- &producerMessage
}

// Shutdown closes producer
func (p *Producer) Shutdown() {
	if p.producer != nil {
		if err := p.producer.Close(); err != nil {
			log.Warn("Producer shutdown unsuccessful", zap.String("error", err.Error()))
		}
	}
}

// Publish produces message to specified topic
func Publish(
	ctx context.Context,
	topicProducers []helpers.TopicProducer,
	stringMessage string,
	protoMessage proto.Message,
	key []byte,
) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "Kafka/Publish", tracer.WithSpanKind(tracer.SpanKindInternal))
		defer span.End()
		ctx = tctx
	}
	for _, topicProducer := range topicProducers {
		if topicProducer.Topics == nil || len(topicProducer.Topics) == 0 {
			log.Error("Could not find topics to write to.", zap.String("producer", topicProducer.Producer))
			continue
		}

		kafkaProducer := getProducerFromLabel(topicProducer.Producer)

		if topicProducer.Format == "string" && stringMessage != "" {
			for _, topic := range topicProducer.Topics {
				kafkaProducer.PushStringWithSegment(ctx, topic, stringMessage, key)
			}
		}

		if topicProducer.Format == "proto" && protoMessage != nil {
			for _, topic := range topicProducer.Topics {
				kafkaProducer.PublishProto(ctx, topic, protoMessage, key)
			}
		}
	}
}

func getProducerFromLabel(label string) Producer {
	if kafkaProducer, ok := Producers[label]; ok {
		return kafkaProducer
	}

	return DefaultProducer
}
