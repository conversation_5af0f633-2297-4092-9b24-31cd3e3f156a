package config

import (
	"strings"

	"github.com/spf13/viper"
)

// InitializeConfig inits the global config for the application
func InitializeConfig(serviceName string) {
	//read our app config
	//viper.SetConfigType("toml")
	viper.AddConfigPath("./configs/")
	//viper.SetConfigName("config")

	err := viper.ReadInConfig()
	if err != nil {
		panic(err)
	}

	// read tables json
	viper.SetConfigType("json")
	viper.SetConfigName("jobs")

	// merge both configs
	viper.MergeInConfig()

	viper.SetConfigType("json")
	viper.SetConfigName("enrichment-service")
	viper.MergeInConfig()

	// Check and load env variables
	viper.AutomaticEnv()

	// Define Prefix
	viper.SetEnvPrefix(serviceName)

	// // Define Replacer
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
}
