package workerpool

import (
	"time"

	log "github.com/Zomato/go/logger"
	"go.uber.org/zap"
)

type JobFuncType func(args []interface{}) (int, error)

type Worker struct {
	Identifier    int32
	BatchSize     uint32
	FlushInterval time.Duration

	Shutdown            chan bool
	SuccessfullShutdown chan bool

	Queue   chan interface{}
	Handler JobFuncType

	Buffer []interface{}

	Ticker <-chan time.Time
	Timer  *time.Ticker

	IsActive bool
}

func (w *Worker) process(payload interface{}) {
	w.Buffer = append(w.Buffer, payload)
	if len(w.<PERSON>uff<PERSON>) >= int(w.BatchSize) {
		w.Flush()
	}
}

func (w *Worker) Start() {
	log.Info("Starting worker", zap.Int32("worker", w.Identifier))

	if w.FlushInterval > 0 {
		w.Timer = time.NewTicker(w.FlushInterval)
		w.Ticker = w.Timer.C
	}

	w.IsActive = true

	for {
		select {
		case <-w.Shutdown:
			log.Info("Worker shutdown hook called", zap.Int32("worker", w.Identifier))
			w.IsActive = false

			// Flush the queue
			for len(w.Queue) > 0 {
				log.Info("Flushing queue", zap.Int32("worker", w.Identifier))
				payload, open := <-w.Queue
				if !open {
					w.SuccessfullShutdown <- true
					return
				}

				w.process(payload)
			}

			w.SuccessfullShutdown <- true
			return
		case payload, open := <-w.Queue:
			// If queue is closed and client is still trying to push messages
			// we ignore this message and flush the queue
			if !open {
				// this is just fail safe
				w.Flush()
				return
			}

			w.process(payload)
		case <-w.Ticker:
			w.Flush()
		}
	}
}

// Stop stops the given worker
// flushes the buffer
// closes both queue and shutdown channels
func (w *Worker) Stop() {
	w.IsActive = false
	w.Flush()
	w.Shutdown <- true

	// Allow workers to drain their queues
	// clean up the buffer
	<-w.SuccessfullShutdown

	w.Flush()

	close(w.Shutdown)
	close(w.Queue)
}

// Flush flushes the buffer.
func (w *Worker) Flush() {
	if len(w.Buffer) == 0 {
		return
	}

	// process the buffer
	unprocessedMessages, err := w.Handler(w.Buffer)
	if err != nil {
		log.Error("Worker unable to process messages",
			zap.Int32("worker", w.Identifier),
			zap.Any("messages", int64(unprocessedMessages)),
			zap.Any("error", err.Error()))
	}

	// empty the buffer
	w.Buffer = w.Buffer[:0]
}

func (w *Worker) isQueueFull() bool {
	if len(w.Queue) >= cap(w.Queue)-1 {
		log.Info("Queue full", zap.Int32("worker", w.Identifier))

		return true
	}

	return false
}
