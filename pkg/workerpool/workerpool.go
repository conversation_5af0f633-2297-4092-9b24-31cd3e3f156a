package workerpool

import (
	"errors"
	"reflect"
	"sync"
	"time"

	log "github.com/Zomato/go/logger"
	"go.uber.org/zap"
)

type WorkerPool struct {
	Identifier        string
	Workers           map[int32]*Worker
	NumWorkers        int32
	MessageAssertType reflect.Type
}

var pool map[string]*WorkerPool

var onceInit sync.Once

func New(
	identifier string,
	numWorkers int32,
	batchSize uint32, // after how many messages to flush the queue
	flushInterval time.Duration, // after how much time to flush the quueue
	queueLength uint32, // used to define the buffer size, controls backpressure over the consumer
	handler JobFuncType,
	messageAssertType reflect.Type,
) (*WorkerPool, error) {
	onceInit.Do(func() {
		pool = make(map[string]*WorkerPool)
	})

	if _, ok := pool[identifier]; ok {
		return nil, errors.New("Worker pool with the given identifier already exists")
	}

	wp := &WorkerPool{
		Identifier:        identifier,
		Workers:           make(map[int32]*Worker),
		NumWorkers:        numWorkers,
		MessageAssertType: messageAssertType,
	}

	for i := int32(0); i < wp.NumWorkers; i++ {
		wp.Workers[i] = &Worker{
			Identifier:          i,
			BatchSize:           batchSize,
			FlushInterval:       flushInterval,
			Handler:             handler,
			Shutdown:            make(chan bool, 1),
			SuccessfullShutdown: make(chan bool, 1),
			Queue:               make(chan interface{}, queueLength),
			Buffer:              make([]interface{}, 0, batchSize),
		}
	}

	pool[identifier] = wp

	return wp, nil
}

// Enqueue pushes into the queue
func (wp *WorkerPool) Enqueue(message interface{}, workerKey int32) bool {
	if !(reflect.TypeOf(message) == wp.MessageAssertType) {
		return false
	}

	// [Optimization] Better algorithm to select worker queue to push to
	worker, ok := wp.Workers[workerKey]
	if !ok {
		// This case should never happen, if it does this means we have to tweak the number of workers
		// according to partitions
		log.Error("Worker is not registered", zap.Int32("worker", workerKey), zap.Any("message", message))
	}

	if !worker.IsActive {
		return false
	}
	log.Info("Pushing to worker", zap.Int32("worker", workerKey))
	worker.Queue <- message
	return true
}

// Stop stops all workers in the pool
func (wp *WorkerPool) Stop() {
	log.Info("Stopping worker pool", zap.String("identifier", wp.Identifier))

	for _, worker := range wp.Workers {
		worker.Stop()
	}
}

// Start boots up the workers
func (wp *WorkerPool) Start() {
	log.Info("Booting up worker pool")

	for _, worker := range wp.Workers {
		go worker.Start() //nolint: ignore-recover
	}
}

func Stop() {
	log.Info("Stopping all worker pools")

	for _, wp := range pool {
		wp.Stop()
	}
}
