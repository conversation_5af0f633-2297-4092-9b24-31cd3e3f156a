logger:
  level: 0
  timestamp: ""

profiling:
  enabled: false
  port: 6060
  
datadog:
  profiling:
    enabled: true
    goroutine_profile:
      enabled: true
      
tracer:
  enabled: true
  debug: false
  env: dev
  service:
    name: flash-gateway
  agent:
    addr: "localhost:8126"

kafka:
  notdefinedtopic: dataplatform.undefined_events_stag
  compositeeventsoutputtopic: arroyo.composite_order_events
  consumers:
    jumbo:
      brokers: prod-jumbo-v2-kafka-nlb-87382a3e698e4217.elb.ap-southeast-1.amazonaws.com:9092
      # brokers: stag-jumbo-kafka-1.internal.zomans.com:9092,stag-jumbo-kafka-2.internal.zomans.com:9092,stag-jumbo-kafka-3.internal.zomans.com:9092
      topics: search_topic_v2
      group: subscriptions-events-test-20250131
      version: 2.4.1
    aggr:
      brokers: prod-aggregator-kafka-nlb-9aab8be6f5ef0580.elb.ap-southeast-1.amazonaws.com:9092
      topics: logistics.enveloped_events,zomato.flash-pipeline.realtime-events,logistics.flash-pipeline.realtime-events
      group: worker-backfill-prod-20240321
      version: 2.5.0
    jumbov2:
      brokers: stag-jumbo-kafka-1.internal.zomans.com:9092,stag-jumbo-kafka-2.internal.zomans.com:9092,stag-jumbo-kafka-3.internal.zomans.com:9092
      topics: logistics.jumbo-pipeline.realtime-events-v2,zomato.jumbo-pipeline.realtime-events-v2,zomato.web.realtime-events-v2
      group: any-worker-stag_20240321
      version: 2.5.0
    flash:
      brokers: stag-jumbo-kafka-1.internal.zomans.com:9092,stag-jumbo-kafka-2.internal.zomans.com:9092,stag-jumbo-kafka-3.internal.zomans.com:9092
      topics: online_ordering.jumbo_app_events
      group: jumbo_flash_router_test_20250131
      version: 2.2.0
    jumbov2eventconsumer:
      brokers: prod-jumbo-kafka-nlb-1e1da6a25dcf88d2.elb.ap-southeast-1.amazonaws.com:9092
      topics: jumbo_streaming.zomato.jumbo2.karma_tracking,jumbo_streaming.zomato.jumbo2.app_performance_metrics,jumbo_streaming.zomato.jumbo2.search_serviceability
      topics_regex: ""
      # topics: jumbo_streaming.zomato.jumbo2.karma_tracking,jumbo_streaming.zomato.jumbo2.merchant_relay_events,jumbo_streaming.zomato.jumbo2.live_order_service_events,jumbo_streaming.zomato.jumbo2.app_performance_metrics,jumbo_streaming.zomato.jumbo2.search_serviceability,jumbo_streaming.zomato.jumbo2.video_message_events,jumbo_streaming.zomato.jumbo2.xtreme_orders,jumbo_streaming.zomato.jumbo2.weather_station_events,jumbo_streaming.zomato.jumbo2.cell_logs_events,jumbo_streaming.zomato.jumbo2.order_rush_events,jumbo_streaming.zomato.jumbo2.res_serviceability_events,jumbo_streaming.zomato.jumbo2.serviceability_locality_stats,jumbo_streaming.zomato.jumbo2.zfe_limit_transaction_events,jumbo_streaming.zomato.jumbo2.ads_central_web_tracking,jumbo_streaming.zomato.jumbo2.webviews_tracking,jumbo_streaming.zomato.jumbo2.ads_studio_tracking,jumbo_streaming.hyperpure.jumbo2.hyperpure_order_events
      group: stag_flash_gateway_jumbov2eventconsumer_test_20250505_test
      version: "2.2.0"
      
    blinkitjumbov2eventconsumer:
      # brokers: prod-jumbo-kafka-nlb-1e1da6a25dcf88d2.elb.ap-southeast-1.amazonaws.com:9092
      brokers: stag-jumbo-kafka-1.internal.zomans.com:9092,stag-jumbo-kafka-2.internal.zomans.com:9092,stag-jumbo-kafka-3.internal.zomans.com:9092
      topics: jumbo_streaming.blinkit.jumbo2.click_events
      group: stag_flash_gateway_jumbov2eventconsumer_test_20250523_test
      version: "2.2.0"
    districtprotoconvertorconsumer:
      brokers: prod-jumbo-kafka-nlb-1e1da6a25dcf88d2.elb.ap-southeast-1.amazonaws.com:9092
      topics: district_search_topic_v2
      group: flash_district_protoconvertor_test
      version: 2.2.0
    enricher:
      brokers: prod-fronting-gateway-kafka-1.zdp.internal.zomans.com:9092,prod-fronting-gateway-kafka-2.zdp.internal.zomans.com:9092,prod-fronting-gateway-kafka-3.zdp.internal.zomans.com:9092
      topics: flash.res_serviceability_events
      group: enricher-test-flash-gateway_20240321
      version: 2.2.0
    enrichmentingestor:
      brokers: prod-fronting-gateway-kafka-1.zdp.internal.zomans.com:9092,prod-fronting-gateway-kafka-2.zdp.internal.zomans.com:9092,prod-fronting-gateway-kafka-3.zdp.internal.zomans.com:9092
      topics: flash.enrichment_events
      group: enrichment-ingestor-stag_20240321
      version: 2.2.0
    alertconsumer:
      brokers: kafka:29092
      topics: noob
      group: alerts-consumer_stag_20240321
      version: 2.2.0
    protoconvertorconsumerfrontend:
      brokers: prod-jumbo-v2-kafka-nlb-87382a3e698e4217.elb.ap-southeast-1.amazonaws.com:9092
      topics: search_topic_v2
      group: proto_conv_test
      version: 2.2.0
    protoconvertorconsumerbackend:
      brokers: prod-aggregator-kafka-nlb-9aab8be6f5ef0580.elb.ap-southeast-1.amazonaws.com:9092
      topics: zomato.web.offline-events-v2,logistics.jumbo-pipeline.realtime-events-v2,zomato.jumbo-pipeline.realtime-events-v2,logistics.jumbo-pipeline.offline-events-v2,zomato.jumbo-pipeline.offline-events-v2
      group: proto_conv_test
      version: 2.2.0
    blinkitprotoconvertorconsumer:
      # brokers: prod-jumbo-kafka-nlb-1e1da6a25dcf88d2.elb.ap-southeast-1.amazonaws.com:9092
      # topics: blinkit_search_topic_v2,blinkit_search_topic_web_v2
      brokers: prod-blinkit-kafka-1.internal.zomans.com:9092,prod-blinkit-kafka-2.internal.zomans.com:9092,prod-blinkit-kafka-3.internal.zomans.com:9092
      topics: blinkit_search_topic_test
      group: proto_blinkit_conv_test_20240502_test
      version: 2.2.0
    blinkitprotoconvertorconsumerbackend:
      brokers: prod-blinkit-kafka-1.internal.zomans.com:9092,prod-blinkit-kafka-2.internal.zomans.com:9092,prod-blinkit-kafka-3.internal.zomans.com:9092
      topics: blinkit.jumbo-pipeline.offline-events-v2
      group: blinkitprotoconvertorconsumerbackend_stag
      version: 2.2.0
    blinkitprotoconvertorconsumerfrontend:
      # brokers: prod-jumbo-kafka-nlb-1e1da6a25dcf88d2.elb.ap-southeast-1.amazonaws.com:9092
      # topics: blinkit_search_topic_v2
      brokers: prod-blinkit-kafka-1.internal.zomans.com:9092,prod-blinkit-kafka-2.internal.zomans.com:9092,prod-blinkit-kafka-3.internal.zomans.com:9092
      topics: blinkit_search_topic_test
      group: proto_blinkit_conv_test_20250502_test
      version: 2.4.1
    compositeconsumer:
      brokers: prod-aggregator-kafka-nlb-9aab8be6f5ef0580.elb.ap-southeast-1.amazonaws.com:9092
      topics: zomato.delivery-composite-order-service.composite-order-events-v2
      group: composite_order_test_20240805
      version: "2.2.0"
    blinkit:
      brokers: prod-jumbo-kafka-nlb-1e1da6a25dcf88d2.elb.ap-southeast-1.amazonaws.com:9092
      # brokers: stag-jumbo-kafka-1.internal.zomans.com:9092,stag-jumbo-kafka-2.internal.zomans.com:9092,stag-jumbo-kafka-3.internal.zomans.com:9092
      topics: blinkit_search_topic_v2
      group: blinkit-jumbo-consumer-stag_20240321
      version: 2.4.1
    blinkitorderlifecycle:
      brokers: prod-blinkit-kafka-1.internal.zomans.com:9092,prod-blinkit-kafka-2.internal.zomans.com:9092,prod-blinkit-kafka-3.internal.zomans.com:9092
      topics: blinkit.dev.ols.flash_gateway
      group: blinkit-clevertap-consumer-stag_20240321
      version: "2.2.0"
      destinations:
        clevertap: blinkit
    bistroclevertapconsumer:
      brokers: prod-blinkit-kafka-1.internal.zomans.com:9092,prod-blinkit-kafka-2.internal.zomans.com:9092,prod-blinkit-kafka-3.internal.zomans.com:9092
      topics: bistro.dev.ct.flash_gateway
      group: bistro-clevertap-consumer-stag_20241220
      version: "2.2.0"
      destinations:
        clevertap: bistro
    district:
      brokers: prod-jumbo-kafka-nlb-1e1da6a25dcf88d2.elb.ap-southeast-1.amazonaws.com:9092
      topics: district_search_topic_v2
      group: district_jumbo_consumer-test
      version: 2.4.1
    zomato-jumbo-consumer-frontend:
      brokers: prod-jumbo-kafka-nlb-1e1da6a25dcf88d2.elb.ap-southeast-1.amazonaws.com:9092
      topics: search_topic_v2
      group: jumbo_flash_router_stag_test_dep_pusher_frontend
      version: 2.4.1     
    zomato-jumbo-consumer-backend:
      brokers: prod-aggregator-kafka-nlb-9aab8be6f5ef0580.elb.ap-southeast-1.amazonaws.com:9092
      topics: logistics.jumbo-pipeline.realtime-events-v2
      group: jumbo_flash_router_stag_dep_pusher
      version: 2.4.1 
    blinkit-jumbo-consumer-frontend:
      brokers: kafka:29092
      topics: searchTopic
      group: jumbo_flash_router_stag
      version: 2.4.1  
    blinkit-jumbo-consumer-backend:
      brokers: kafka:29092
      topics: searchTopic
      group: jumbo_flash_router_stag
      version: 2.4.1    
  producers:
    default:
      brokers: stag-sdp-kafka-nlb-485a40d16c0b54c4.elb.ap-southeast-1.amazonaws.com:9092
      useJVMCompatiblePartitioner: false
    offlinev2:
      brokers: staging-kafka-1.internal.zomans.com:9092,staging-kafka-2.internal.zomans.com:9092,staging-kafka-3.internal.zomans.com:9092
      useJVMCompatiblePartitioner: false
    sdp:
      brokers: stag-sdp-kafka-nlb-485a40d16c0b54c4.elb.ap-southeast-1.amazonaws.com:9092
      useJVMCompatiblePartitioner: false
    staging:
      brokers: stag-jumbo-kafka-1.internal.zomans.com:9092,stag-jumbo-kafka-2.internal.zomans.com:9092,stag-jumbo-kafka-3.internal.zomans.com:9092
      useJVMCompatiblePartitioner: false
    composite:
      brokers: stag-jumbo-kafka-1.internal.zomans.com:9092,stag-jumbo-kafka-2.internal.zomans.com:9092,stag-jumbo-kafka-3.internal.zomans.com:9092
      useJVMCompatiblePartitioner: true
    blinkit:
      brokers: stag-jumbo-kafka-1.internal.zomans.com:9092,stag-jumbo-kafka-2.internal.zomans.com:9092,stag-jumbo-kafka-3.internal.zomans.com:9092
      useJVMCompatiblePartitioner: true
    offlineMsk:
      brokers: preprodofflinekaf.guyv8x.c4.kafka.ap-southeast-1.amazonaws.com:9092
      useJVMCompatiblePartitioner: false
    jumbo:
      brokers: stag-jumbo-kafka-1.internal.zomans.com:9092,stag-jumbo-kafka-2.internal.zomans.com:9092,stag-jumbo-kafka-3.internal.zomans.com:9092
    district:
      brokers: stag-jumbo-kafka-1.internal.zomans.com:9092,stag-jumbo-kafka-2.internal.zomans.com:9092,stag-jumbo-kafka-3.internal.zomans.com:9092

  config:
    producer:
      flushfrequency: 500ms

statsd:
  url: 127.0.0.1:9125
  application: flash_gateway
  job: jumbo

notify:
  url: localhost

cache:
  metrics: true
  l2:
    ttl: 24h
    numcounters: 100000000
    maxcost: 10000000
  l1:
    hosts: prod-enrichment-service-test.xxvr2h.clustercfg.apse1.cache.amazonaws.com:6379
    clustermode: true
    poolsize: 50
    servereadsfromslaves: true

enricher:
  kafka: default

proto-convertor:
  kafka: jumbo
  blinkit-kafka: jumbo

composite:
  kafka: composite

newrelic:
  application: flash_gateway
  license: ""
  enabled: false

workerpool:
  size: 10
  batchSize: 10
  flushinterval: 5s
  queuelength: 8

clevertap:
  producers:
    default:
      workerpool:
        size: 10
        batchSize: 10
        flushinterval: 5s
        queuelength: 8
    blinkit:
      accountid: TEST-867-464-665Z
      passcode: 8838f8ce49a947fa8207de7637969e0e
      endpoint: https://api.clevertap.com
      workerpool:
        size: 3
        batchSize: 1000
        flushinterval: 60s
        queuelength: 1000
    bistro:
      accountid: 8W6-4RK-4R7Z
      passcode: SCC-ASB-AWEL
      endpoint: https://api.clevertap.com
      workerpool:
        size: 3
        batchSize: 1000
        flushinterval: 60s
        queuelength: 1000
        
profiler:
  port: 6060
  enabled: false
  credentials: ""
  goroutine_profile:
    enabled: true
  block_profile:
    enabled: true
  mutex_profile:
    enabled: true
  gc_runs:
    disabled: true

go_runtime:
  mem_limit:
    enabled: true
    go_gc_percentage: -1
  metrics:
    enabled: true