## flash-gateway
 
### Infrastructure doc

https://zomato.atlassian.net/wiki/spaces/DE/blog/2020/06/10/1279557792/Worker+Cluster

### Entrypoint parameters

#### -mode

- [worker, grpc]
  - worker: starts in worker mode
  - server: starts in grpc mode

#### -jobtype

- [flash-consumer, jumbo-v2-consumer, jumbo-consumer]
  - flash-consumer
    - kafka: aggr-v2
    - topics: logistics.enveloped_events,zomato.flash-pipeline.realtime-events,logistics.flash-pipeline.realtime-events
    - payload format: proto
  - jumbo-v2-consumer
    - kafka: aggr-v2
    - topics: logistics.jumbo-pipeline.realtime-events-v2,zomato.jumbo-pipeline.realtime-events-v2
    - payload format: jumbo (v2, header and payload)
  - jumbo-consumer
    - kafka: jumbo-kafka
    - topics: jumbo_app_events
    - payload format: jumbo (v1, $% separator)
