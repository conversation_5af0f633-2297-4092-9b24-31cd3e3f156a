# Testing jumbov2-events-consumer with Datadog Integration

This guide provides instructions for testing the jumbov2-events-consumer service with Datadog integration locally.

## Prerequisites

- Docker and Docker Compose installed
- A Datadog API key (you can get a free trial from Datadog)

## Setup

1. Set your Datadog API key as an environment variable:

```bash
export DD_API_KEY=your_datadog_api_key
```

2. Create the Kafka topic:

```bash
./setup-kafka.sh
```

3. Start the services:

```bash
make dev
```

This will start the following services:
- Kafka and Zookeeper
- Statsd exporter
- Datadog agent
- Flash Gateway with jumbov2-events-consumer job type

## Testing

1. Produce a test message to the Kafka topic:

```bash
./produce-test-message.sh
```

2. Check the logs to see if the message was processed:

```bash
docker logs -f flash-gateway
```

3. Check Datadog for traces:

Open the Datadog APM UI in your browser to see the traces from the jumbov2-events-consumer service.

## Configuration

The service is configured with the following:

- Job type: `jumbov2-events-consumer`
- Kafka topic: `test.jumbov2events`
- Datadog tracing enabled
- Statsd metrics sent to Datadog agent

## Troubleshooting

If you encounter issues:

1. Check if the Kafka topic was created:

```bash
docker exec -it kafka kafka-topics --list --bootstrap-server localhost:9092
```

2. Check if the Datadog agent is running:

```bash
docker ps | grep datadog-agent
```

3. Check the logs for any errors:

```bash
docker logs -f flash-gateway
docker logs -f datadog-agent
```
