syntax = "proto3";

package zomato.jumbo.gateway;

import "google/protobuf/any.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/Zomato/flash-gateway-client-golang/gateway";

message MessageEnvelope {
    string UUID = 1; 
    google.protobuf.Any Payload = 4;  
}

message OrderEvents {
    string UserId = 1;
    google.protobuf.Timestamp EventTime = 2;
    string Timezone = 3;
    string DeviceId = 4;
    string SessionId = 5;
    string AppType = 6;
    string AppVersion = 7;
    string Platform = 8;
    string PhoneModel = 9;
    string OSName = 10;
    string OSVersion = 11;
    string UserAgent = 12;
    string Namespace = 13;
    string SchemaName = 14;
    string SchemaType = 15;
    string EventName = 16;
    int64 OrderID = 17;
    int64 OrderStatus = 18; //1/2/3/4/5
    int64 ResID = 19;
    int64 ChainID = 20;
    int32 DszID = 21;
    int32 ZoneID = 22;
    int64 CellID = 23;
    int32 SubzoneID = 24;
    int32 LocalityID = 25;
    int32 CityID = 26;
    int32 CountryID = 27;
    int32 VendorID = 28;
    float Latitude = 29;
    float Longitude = 30;
    float RestaurantLatitude = 31;
    float RestaurantLongitude = 32;
    int32 PromoCodeID = 33;
    int32 LogisticsPartnerID = 34;
    string CurrencyCode = 35;
    float SubTotal = 36;
    float GrandTotal = 37;
    float DeliveryCharge = 38;
    float PackagingCharge = 39;
    float SaltDiscountAmount = 40;
    float PromoDiscountAmount = 41;
    float TotalDiscountAmount = 42;
    float PepperDiscountAmount = 43;
    float PiggybankDiscountAmount = 44;
    string KarmaScore = 45;
    string DeliveryStatus = 46;
    string OrderType = 47;
    int32 RejectedMessageId = 48;
    int32 ZomatoDeliveryChargeTag = 49;
    int32 IsNewUser = 50;
    string VoucherName = 51;
    int32 PaymentStatus = 52;
    int64 EpochTimestamp = 53;
    int32 NumItems = 54;
    int32 DistinctNumItems = 55;
    string PaymentMethod = 56;
    google.protobuf.Timestamp OrderAcceptedAt = 57;
    google.protobuf.Timestamp FEArrivedAt = 58;
    string RestaurantCellID = 59;
    float PromisedKPT = 60;
    float PromisedDeliveryTime = 61;
    float PromisedETA = 62;
    google.protobuf.Timestamp OrderCreatedAt = 63;
    google.protobuf.Timestamp OrderPickedAt = 64;
    float PromisedOTOFDeliveryTime = 65;
    bool IsOTOF = 66;
    float GoldDiscount = 67;
    float TotalDishCost = 68;
    float ActualOTOFDeliveryTime = 69;
    int32 IsSingleServe = 70;
    int32 IsReservedRestaurant = 71;
    int32 IsBatched = 72;
    int32 IsDeferred = 73;
    string AssignmentSource = 74;
    string RiderCarrierType = 75;
    float ActualAssignmentTimeInMinutes = 76;
    float RiderLatitude = 77;
    float RiderLongitude = 78;
    bool IsOtofBreached = 79;
    float MerchantTotal = 80;
    string PromoCode = 81;
    int64 TripId = 82;
    google.protobuf.Timestamp FoodOrderReadyAt = 83;
    string UserSegment = 84;
    float ActualKPT = 85;
    int32 SaltID = 86;
    float MerchantPromisedKPT = 87;
    int32 IsZomatoHealthy = 88;
    int32 IsMarket = 89;
    int32 IsAlcobev = 90;
    int64 DiningOrderId = 91;
    int32 ServingOption = 92;
    float ZomatoVoucherDiscount = 93;
    float MerchantVoucherDiscount = 94;
    float CommissionableAmount = 95;
    float DeliveryDiscountAmount = 96;
    int32 IsPremiumUser = 97;
    int32 RpfOrder = 98;
    float ProDiscount = 99;
    int32 IsAlcobevV2 = 100;
    int64 FEID = 101;
    int32 IsGourmet = 102;
    int32 UserPremiumIndex = 103;
    float MenuServiceabilityLevel = 104;
    string PromoCampaign = 105;
    google.protobuf.Timestamp ScheduledStartTime =106;
    google.protobuf.Timestamp ScheduledEndTime = 107;
    google.protobuf.Timestamp PreparationStartTime = 108;
    float EtaBuffer = 109;
    float MoveValue = 110;
    string MoveType = 111;
    string RuleApplied = 112;
    bool RelaxationHelped = 113;
    float Dc = 114;
    float BaseDc = 115;
    float DistanceDc = 116;
    float DcDiscount = 117;
    float SurgeCharge = 118;
    bool IsBypassOrder = 119;
    string BypassOrderReason = 120;
    int32 FoodXLDrop = 121;
    int32 DropLocalityID = 122;
    bool IsCovidOrder = 123;
    bool IsDefaultContactless = 124;
    float OxygenDonationAmount = 125;
    int32 PriorityOrder = 126;
    float ModelKPT = 127;
    float KPTBuffer = 128;
    int32 UserAddressEffort = 129;
    int32 ProPlanPurchased = 130;
    float MerchantLoyaltyDiscount = 131;
    string ProPlusDiscount = 132;
    float RiderKPT = 133;
    float KPTHandshakeTime = 134;
    bool IsPara = 135;
    bool IsBoltOrder = 136;
    bool IsRaining = 137;
    float RoadDistance = 138;
    float Ddt = 139;
    float DdtKpt = 140;
    string ServiceabilityReason = 141;
    string ServiceabilityType = 142;
    float MerchantReferralDiscount = 143;
    int32 PosID = 144;
    float FuelSurgeCharge = 145;
    int64 PoiID = 146;
    string RejectionSource = 147;
    int32 CountForAcceptance = 148;
    bool IsIntercityOrder = 149;
    string KarmaScoreV2 = 150;
    bool IsInstantRestaurant = 151;
    int32 DcPlanPrice = 152;
    int32 AddressCityID = 153;
    bool IsMealForOne = 154;
    float DcPackDiscount = 155;
    int32 SmallOrderFee = 156;
    float ZomatoStarchDiscount = 157;
    int32 ResRushLevel = 158;
    int32 ResLiveOrderCount = 159;
    float EdValue = 160;
    float EdBuffer = 161;
    bool IsNoDelayGuarantee = 162;
    float NoDelayGuaranteePromisedEdt = 163;
    bool IsGoldUser = 164;
    int32 DeliverySpeed = 165;
    float GoldDCDiscount = 166;
    float OtgCashback = 167;
    bool IsOrderV2 = 168;
    float KPTDelay = 169;
    float RiderDropTime = 170;
    float TimeToReach = 171;
    float PlatformCharge = 172;
    string DishName = 173;
    int64 CatalogueID = 174;
    int32 LandingSource = 175;
    bool IsEnterpriseMealWalletOrder = 176;
    string FulfilmentType = 177;
    string LogisticsFulfilmentType = 178;
    google.protobuf.Timestamp WalkerAssignedAt = 179;
    int64 AddressID = 180;
    int32 AddressConfidence = 181;
    string UserAddressType = 182;
    bool IsAddItemContext = 183;
    bool IsAddItemMultiStoreContext = 184;
    string SubscriptionType = 185;
    bool IsPremiumCheckoutOrder = 186;
    float SmallOrderFeeV2 = 187;
    float CustomerCODLimit = 188;
    bool IsMerchantOrderServiceCreatedOrder = 189;
    string PaymentPromoCode = 190;
    float PaymentVoucherDiscount = 191;
    float PaymentZomatoVoucherDiscount = 192;
    float PaymentMerchantVoucherDiscount = 193;
    string DestinationAddressTrainNumber = 194;
    float TimedOfferDiscountAmount = 195;
    float ZomatoGoldSubscriptionResDiscount = 196;
    float MerchantGoldSubscriptionResDiscount = 197;
    bool IsEnterpriseLimitOrder = 198;
    bool IsResPureVeg = 199;
    bool IsPureVegModeOn = 200;
    string UserVegCategory = 201;
    int64 StarchID = 202;
    int64 DriverServiceId = 203;
    string ServiceabilityFleetType = 204;
    string RateCardID = 205;
    float ZomatoGoldSubscriptionDeliveryDiscount = 206;
    float MerchantGoldSubscriptionDeliveryDiscount = 207;
    float ZomatoSaltDiscount = 208;
    float MerchantSaltDiscount = 209;
    float ZomatoDCPackDiscount = 210;
    float MerchantDCPackDiscount = 211;
    float MerchantBXGYDiscount = 212;
    float ZomatoBXGYDiscount = 213;
    float ZomatoDeliveryChargeDiscount = 214;
    float MerchantDeliveryChargeDiscount = 215;
    float MerchantStarchDiscount = 216;
    string StoreType = 217;
    bool IsO2ScheduledOrder = 218;
    int64 DiscountPackID = 219;
    float DiscountPackAmount = 220;
    float AmountToBeCollected = 221; // amount to be collected by the rider
    float AmountCollected = 222; // amount collected by the rider
    bool IsSpareCash = 223;  // if spare cash is enabled on the order or not
    float SettledAmount = 224; // adjusted amount collected by the rider
    bool IsOrderCreatedFromAPIGateway = 225;
    bool isExpressFoodOrder = 226;
    string logisticItemProperty = 227; // highest priority logistic item property
    bool IsMultiOrder = 228;
    bool IsFoodRescueOrder = 229;
    float CustomerKpt = 230;
}

message ServiceabilityEvents {
    google.protobuf.Timestamp EventTime = 1;
    string Timezone = 2;
    string EventName = 3;
    string EntityID = 4;                 
    string EntityType = 5;               
    int32 Serviceable = 6;                
    int64 PingTime = 7;                   
    int32 LocalityId = 8;                 
    int32 DriverCount =  9;               
    float PickupTime = 10;                 
    int32 QueueableDriverCount = 11;        
    string Channel = 12;                   
    float Pscore = 13;                     
    repeated int32 QueueableDriverIds = 14; 
    repeated int32 FreeDriverIds = 15;
    string Namespace = 16;
    string SchemaName = 17;
    string SchemaType = 18;
    int32 Shutdown = 19;
    int32 Reserved = 20;
    int32 HawkeyeShutdown = 21;
    int32 GmtShutdown = 22;
    int32 RestaurantSelfShutdown = 23;
    int32 CityShutdown = 24;
    int32 CycleServiceable = 25;
    int32 CityID = 26;
    int32 CyclistCount = 27;
    float RATLongDistance = 28;
    float RATShortDistance = 29;
    float RPT = 30;
    float KPT = 31;
    int32 ServiceableV2LongDistance = 32;
    int32 ServiceableV2ShortDistance = 33;
    int32 ServiceabilityLevel = 34;
    int32 CycleServiceabilityLevel = 35;
    float Variance = 36;
    float Threshold = 37;
    float WeightedAvg = 38;
    int32 LastMinutes = 39;
    float CycleVariance = 40;
    float CycleThreshold = 41;
    float CycleWeightedAvg = 42;
    int32 CycleLastMinutes = 43;
    int32 UnassignedOrderCount = 44;
    string Source = 45;
    int32 PingHandlerCount = 46;
    bool BikeServiceable = 47;
    int32 PingFreeBikerCount = 48;
    int32 PingQueueableBikerCount = 49;
    int32 PingFreeCyclistCount = 50;
    int32 PingQueueableCyclistCount = 51;
    string ConfigSource = 52;
    int32 PingUnassignedOrderCount = 53;
    bool GroceryServiceable = 54;
    bool GroceryShutdown = 55;
    bool GroceryEnabled = 56;
    int32 GroceryDriverCount = 57;
    int32 PingGroceryDriverCount = 58;
    int32 MenuUnavailability = 59;
    int32 MenuEmpty = 60;
    int32 RestaurantLogsTimingActiveStatus = 61;
    int32 MerchantStatus = 62;
    int32 Lp15Status = 63;
    int32 GroceryFreeCyclistCount = 64;
    bool LiquorServiceable = 65;
    int32 LiquorFreeBikersCount = 66;
    int32 LiquorFreeCyclistCount = 67;
    bool LiquorShutdown = 68;
    bool GroceryCycleServiceable = 69;
    bool LiquorCycleServiceable = 70;
    int32 GroceryUnassignedOrderCount = 71;
    int32 LiquorUnassignedOrderCount = 72;
    int32 QueueableCyclistCount = 73;
    int32 GroceryQueueableBikerCount = 74;
    int32 GroceryQueueableCyclistCount = 75;
    int32 LiquorQueueableBikerCount = 76;
    int32 LiquorQueueableCyclistCount = 77;
    int32 O2CityKilled101 = 78;
    int32 DszO2Killed103 = 79;
    int32 DeliveryStatusFlagOff710 = 80;
    int32 AdminActiveFlagOff709 = 81;
    int32 MerchantDeviceUnreachable140 = 82;
    int32 RestaurantTemporarilyClosed711 = 83;
    string TimeBuckets = 84;
    string PredictionData = 85;
    int32 ServiceabilityLevelPremium = 86;
    int32 CycleServiceabilityLevelPremium = 87;
    int32 ServiceabilityLevelNormal = 88;
    int32 CycleServiceabilityLevelNormal = 89;
    int32 ServiceabilityLevelLowValue = 90;
    int32 CycleServiceabilityLevelLowValue = 91;
    float EstimatedDemandPremium = 92;
    float EstimatedDemandNormal = 93;
    float EstimatedDemandLowValue = 94;
    int32 SmoothenedServiceabilityLevel = 95;
    int32 SmoothenedCycleServiceabilityLevel = 96;
    int32 UnassignedOrderPremium = 97;
    int32 UnassignedOrderNormal = 98;
    int32 UnassignedOrderLowValue = 99;
    int32 CourierDelay = 100;
    int32 SkippedEntity = 101;
    int32 ShortServiceabilityLevel = 102;
    int32 SmoothenedShortServiceabilityLevel = 103;
    int32 BikeServiceableAggressive = 104;
    int32 CycleServiceableAggressive = 105;
    float AverageAssignmentTime = 106;
    int32 FoodExpiredOrderCount = 107;
    int32 GroceryExpiredOrderCount = 108;
    int32 LiquorExpiredOrderCount = 109;
    string DistanceBuckets = 110;
    string SupplyPredictionData = 111;
    float RiderAssignmentTime = 112;
    bool FoodXLDropServiceable = 113;
    bool FoodXLDropShutdown = 114;
    int32 FoodXLDropFreeBikersCount = 115;
    int32 FoodXLDropQueueableBikerCount = 116;
    int32 FoodXLDropUnassignedOrderCount = 117;
    float LongRat = 118;
    string ReservationType = 119;
    float QueueableFraction = 120;
    float FreeRiderFraction = 121;
    string ConditionalOlBikeServiceableCells = 122 [deprecated = true];
    string ConditionalOlCycleServiceableCells = 123 [deprecated = true];
    repeated string ConditionalOlBikeServiceableCells2 = 124;
    repeated string ConditionalOlCycleServiceableCells2 = 125;
    int32 SaServiceable = 126;
    float LongRPT = 127;
    float ShortRPT = 128;

}

message DriverEvents {
    google.protobuf.Timestamp EventTime = 1;
    string Timezone = 2;
    string Namespace = 3; 
    string SchemaName = 4;
    string SchemaType = 5;
    string EventName = 6;
    string DriverId = 7;
    int32 LocalityId = 8;
    int32 CarrierId = 9;
    int32 SessionId = 10;
    google.protobuf.Timestamp CreatedAt = 11;
    string PrevLoginStatus = 12;
    float ExcessCash = 13;
    float CashLimit = 14;
    string EventType = 15;
    bool IsBlinkit = 16;
    int32 DriverServiceId = 17;
}

message DisputeEvents {
    google.protobuf.Timestamp EventTime = 1;
    string Timezone = 2;
    string Namespace = 3;
    string SchemaName = 4;
    string SchemaType = 5; 
    string EventName = 6;
    string DriverId = 7;
    int32 LocalityId = 8;
    int32 DisputeId = 9;
    string ReferenceType = 10;
    int32 ReferenceId = 11;
    string ResolutionState = 13;
}
message DriverCashDepositTransaction {
    google.protobuf.Timestamp EventTime = 1;
    string Timezone = 2;
    string Namespace = 3; 
    string SchemaName = 4;
    string SchemaType = 5;
    string EventName = 6;
    string DriverId = 7;
    int32 LocalityId = 8;
    int32 DriverCashDepositTransactionId = 9;
    string Type = 10;
    google.protobuf.Timestamp UpdatedAt = 11;
    float ExcessCash = 13;
    float CashLimit = 14;
    string EventType = 15;
}
message ChatEvents {
    google.protobuf.Timestamp EventTime = 1;
    string Timezone = 2;
    string Namespace = 3; 
    string SchemaName = 4;
    string SchemaType = 5;
    string EventName = 6;
    string UserId = 7;
    int64 AgentId = 8;
    string SessionId = 9;
    int32 ChannelId = 10;
    int32 CsatValue = 11;
    int32 ChatStatus = 12;
    string ResolutionTag = 13;
    string AgentName = 14;
    bool IsTl = 15;
    bool IsAdmin = 16;
    bool IsAgent = 17;
    bool IsTempTl = 18;
    int64 TeamLeadId = 19;
    string TeamLeadName = 20;
    int64 ChangedBy = 21;
    int32 GroupId = 22;
    string GroupName = 23;
    bool AgentActiveStatus = 24;
    bool AgentOnlineStatus = 25;
    int32 SkillGroupID = 26;
    string SkillGroupName = 27;
    int32 SiteId = 28;
    string SiteName = 29;
    int64 EventValue = 30;
    google.protobuf.Timestamp ChatStartTime = 31;
    google.protobuf.Timestamp BreakStartTime = 32;
    int32 BreakDuration = 33;
    string AgentStatus = 34;
}
message ReturnHubEvents {
    google.protobuf.Timestamp EventTime = 1;
    string Timezone = 2;
    string Namespace = 3;
    string SchemaName = 4; 
    string SchemaType = 5;
    string EventName = 6;
    string DriverId = 7;
    int32 LocalityId = 8;
    string Otp = 9;
    string OtpType = 10;
    string OrderId = 11;
}

message ShutdownEvents {
    google.protobuf.Timestamp EventTime = 1;
    string Timezone = 2;
    string Namespace = 3; 
    string SchemaName = 4; 
    string SchemaType = 5;
    string EventName = 6;
    int32 LocalityId = 7;
    int32 UnassignedOrderCount = 8;
    int32 RunningOrderCount = 9;
    int32 UnassignedOrderMaxAge = 10;
    string LocalityName = 11;
    int32 CityId = 12;
    string CityName = 13;
    google.protobuf.Timestamp ShutdownAt = 14;
}

message DriverTagMappingEvents {
    google.protobuf.Timestamp EventTime = 1;
    string Timezone = 2;
    string Namespace = 3; 
    string SchemaName = 4;
    string SchemaType = 5;
    string EventName = 6;
    string DriverId = 7;
    string OldTagName = 8;
    string NewTagName = 9;
    google.protobuf.Timestamp OldTagStartDate = 10;
    google.protobuf.Timestamp OldTagEndDate = 11;
    google.protobuf.Timestamp CreatedAt = 12;
    string EventType = 13;
    float FirstPreviousWeekEarning = 14;
    float SecondPreviousWeekEarning = 15;
    float MaxEarning = 16;
    float DriverExcessCash = 17;   
 }

 message B2bGroceryOrderEvents {
    google.protobuf.Timestamp EventTime = 1;
    string Timezone = 2;
    string Namespace = 3; //"logistics"
    string SchemaName = 4;
    string SchemaType = 5;
    string EventName = 6;
    string UserName = 7;
    string OrderId = 8;
    string DriverId = 9;
    string TripId = 10;
    int32 UserId = 11;
    int32 LocalityId = 12;
    string IsBatched = 13;
    string IsDelayed = 14;
    string AssignmentSource = 15;
    string CityName = 16;
    string LocalityName = 17;
    int32 SourceId = 18;
    int32 CityId = 19;
    int32 NumberItems = 20;
    int32 PreparationTime = 21;
    float SubTotal = 22;
}

message NoMaskDisableEvent {
    google.protobuf.Timestamp EventTime = 1;
    string Timezone = 2;
    string Namespace = 3; // "logistics"
    string SchemaName = 4; // "nomask_driver_disable"
    string SchemaType = 5;
    string EventName = 6;
    string DriverId = 7;
    int32 ModelInfractionCount = 8;
    int32 ConsumerInfractionCount = 9;
    int32 MerchantInfractionCount = 10;
    string Action = 11;
    string FeedbackBy = 12;
    string EventType = 13;
    google.protobuf.Timestamp CreatedAt = 14;
    string InfractionOrderId = 15;
    string DriverCity = 16;
    int32 TotalInfractionCount = 17;
    int32 TimeoutInfractionCount = 18;
}

message RiderIdleEventPayload {
    string DriverId = 1;
    int32 IdleLevel = 2;
    float Latitude = 3;
    float Longitude = 4;
    int32 ShipmentLegId = 5;
    int32 TripId = 6;
    string TripState = 7;
    float Speed = 8;
    google.protobuf.Timestamp EventTime = 9;
    repeated string ExternalOrderIds = 10;
    string DriverState = 11;
    int32 Repeat = 12;
    string ActivityState = 13;
    float ActivityConfidence = 14;
    string SourceCellToken = 15;
    string TargetCellToken = 16;
    int32 CityId = 17;
    int32 LocalityId = 18;
    int32 RiderCarrierId = 19;
    string TripClient = 20;
    int32 TripCategoryId = 21;
    int32 NoOfOrdersInCurrentTrip = 22;
    int32 NoOfOrdersInCurrentSession = 23;
    int32 NoOfTripsInCurrentSession = 24;
    int32 ActiveSessionAgeInMinutes = 25;
    int32 TripAgePostPickupSeconds = 26;
    int32 DriverAgeInDays = 27;
    string Namespace = 28;
    string SchemaName = 29;
    int32 IdlingTimeMs = 30;
    google.protobuf.Timestamp LastEventTime = 31;
    string Timezone = 32;
}

