package jumbo_v2_event_consumer

import (
	"context"
	"github.com/Zomato/flash-gateway/internal/constants"
	"github.com/Zomato/flash-gateway/internal/models"
	jumboEvent "github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/event"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/apigateway/o2carttracking"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/types/known/anypb"
	"google.golang.org/protobuf/types/known/timestamppb"
	"google.golang.org/protobuf/types/known/wrapperspb"
	"testing"
)

func Test_O2CartPageLoadedTrackingEvent(t *testing.T) {
	type args struct {
		envelope *jumboEvent.JumboEvent
		key      []byte
	}
	tests := []struct {
		name        string
		args        args
		want        []*models.JumboV2TransformedEvent
		wantErr     bool
		pb          *o2carttracking.O2CartTracking
		eventString string
	}{
		{
			name: "Test O2CartPageLoadedTrackingEvent",
			args: args{
				envelope: &jumboEvent.JumboEvent{
					EventId:  "123",
					Database: jumboEvent.Database_jumbo2,
					Header: &jumboEvent.Header{
						Source: jumboEvent.Source_android,
						DeviceId: &wrapperspb.StringValue{
							Value: "123456",
						},
						UserId: &wrapperspb.StringValue{
							Value: "1234567",
						},
						SessionId: &wrapperspb.StringValue{
							Value: "1234568",
						},
						UserAgent: &wrapperspb.StringValue{
							Value: "ios",
						},
						Timestamp: &timestamppb.Timestamp{
							Seconds: 1741175565,
						},
						Location: &wrapperspb.UInt32Value{
							Value: 1,
						},
						LocationInfo: &jumboEvent.LocationInfo{
							UserDefinedLatitude: &wrapperspb.DoubleValue{
								Value: 26.293832519647705,
							},
							UserDefinedLongitude: &wrapperspb.DoubleValue{
								Value: 73.04621815681458,
							},
							CurrentLatitude: &wrapperspb.DoubleValue{
								Value: 26.2937476,
							},
							CurrentLongitude: &wrapperspb.DoubleValue{
								Value: 73.0462254,
							},
						},
						IngestionTime: &timestamppb.Timestamp{
							Seconds: 1741175565,
						},
					},
				},
				key: []byte("123"),
			},
			want: []*models.JumboV2TransformedEvent{
				{
					Table:  "jumbo_app_events",
					Key:    []byte("123"),
					Topic:  "online_ordering.jumbo_app_events",
					Broker: constants.DEFAULT_BROKER,
				},
			},
			wantErr: false,
			pb: &o2carttracking.O2CartTracking{
				EventName: &wrapperspb.StringValue{
					Value: "O2CartPageLoaded",
				},
				CartId: &wrapperspb.StringValue{
					Value: "1234567",
				},
				CartSequenceId: &wrapperspb.StringValue{
					Value: "1234568",
				},
				ResIds: []*wrapperspb.StringValue{
					{
						Value: "302115",
					},
				},
				DeliveryAddress: &o2carttracking.AddressDetails{
					AddressId: &wrapperspb.StringValue{
						Value: "66666",
					},
					PlaceId: &wrapperspb.StringValue{
						Value: "1",
					},
					PlaceType: &wrapperspb.StringValue{
						Value: "4",
					},
				},
				PaymentDetails: &o2carttracking.PaymentDetails{
					PaymentMethodType: &wrapperspb.StringValue{
						Value: "upi",
					},
				},
			},
			eventString: "{\"device_id\":\"123456\",\"ename\":\"O2CartPageLoaded\",\"key\":\"jevent\",\"location\":\"1\",\"location_info\":{\"current_latitude\":26.2937476,\"current_longitude\":73.0462254,\"user_defined_latitude\":26.293832519647705,\"user_defined_longitude\":73.04621815681458},\"session_id\":\"1234568\",\"source\":\"android\",\"timestamp\":\"2025-03-05T11:52:45Z\",\"user_agent\":\"ios\",\"user_id\":\"1234567\",\"var1\":\"302115\",\"var2\":\"0\",\"var3\":\"upi\",\"var4\":\"66666\",\"var5\":\"1\",\"var6\":\"4\"}",
		},
	}
	for _, tt := range tests {
		pbAny, err := anypb.New(tt.pb)
		if err != nil {
			t.Errorf("Error in creating anypb: %+v", err)
		}
		tt.args.envelope.Payload = pbAny
		tt.want[0].Payload = []byte(tt.eventString)

		t.Run(tt.name, func(t *testing.T) {
			got, err := getO2CartTrackingEvents(context.Background(), tt.args.envelope, tt.args.key)
			if (err != nil) != tt.wantErr {
				t.Errorf("getO2CartTrackingEvents() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.Equal(t, tt.want, got)
		})
	}
}
