package jumbo_v2_event_consumer

import (
	"github.com/Zomato/flash-gateway/internal/models"
	"github.com/Zomato/go/logger"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/apigateway/o2carttracking"
)

var o2CartGenericTransformationEvents = map[string]func(event *o2carttracking.O2CartTracking) ([]*models.JEventsPayload, error){
	"O2CartCheckoutButtonTapped": getO2CartCheckoutButtonTappedJEventPayload,
	"O2OrderPlaced":              getO2OrderPlacedJEventPayload,
	"Backend_O2OrderPlaced":      getBackendO2OrderPlacedJEventPayload,
	"O2CartPageLoaded":           getO2CartPageLoadedJEventPayload,
	"Backend_O2CartPageLoaded":   getBackendO2CartPageLoadedJEventPayload,
	"O2CartPageLoadFailed":       getO2CartPageLoadFailedJEventPayload,
	"O2CheckoutStarted":          getO2CheckoutStartedJEventPayload,
	"O2CheckoutFailed":           getO2CheckoutFailedJEventPayload,
	"O2CheckoutSuccess":          getO2CheckoutSuccessJEventPayload,
	"O2OrderPaymentStarted":      getO2OrderPaymentStartedJEventPayload,
	"O2OrderPaymentFailed":       getO2OrderPaymentFailedJEventPayload,
	"O2OrderPaymentSuccess":      getO2OrderPaymentSuccessJEventPayload,
	"O2OrderPollingStarted":      getO2OrderPollingStartedJEventPayload,
	"O2OrderPollingFailed":       getO2OrderPollingFailedJEventPayload,
	"O2OrderPollingSuccess":      getO2OrderPollingSuccessJEventPayload,
	"O2OrderConfirmed":           getO2OrderConfirmedJEventPayload,
}

var errorIdToErrorCodeMap = map[string]string{
	"0":  "0",  // UndefinedErrorId -> DEFAULT_CODE
	"1":  "1",  // UnknownErrorId -> ORDER_VALIDATION_SUCCESS_CODE
	"2":  "2",  // UnserviceableErrorId -> RESTAURANT_UNSERVICEABLE_CODE
	"3":  "3",  // CatalogItemOutOfStockErrorId -> ITEMS_OUT_OF_STOCK_ERROR_CODE
	"4":  "4",  // TotalMismatchErrorId -> ACTUAL_TOTAL_MISMATCH_ERROR_CODE
	"6":  "6",  // SomethingWentWrongErrorId -> DEFAULT_MAKE_ORDER_ERROR_CODE
	"7":  "7",  // ConcurrentOrderErrorId -> CONCURRENT_ORDER_ERROR_CODE
	"10": "17", // CartLimitBreachedErrorId -> MAX_CART_LIMIT_NEW_CART_ERROR_CODE
	"11": "11", // UnserviceableSchedulingSlot -> UNSERVICEABLE_SCHEDULING_SLOT_ERROR_CODE
	"12": "28", // AddItemInvalidParentId -> ADD_ITEM_MULTI_STORE_FLOW_INVALID_PARENT_ERROR_CODE
	"13": "29", // CustomerNotAllowedDueToLoadSheddingErrorId -> USER_NOT_ALLOWED_DUE_TO_LOAD_SHEDDING
	"14": "9",  // InvalidCatalogConfigurationErrorId -> SOMETHING_WENT_WRONG_ERROR_CODE
	"16": "5",  // ActiveDuplicateError -> MAKE_DUPLICATE_ORDER_ERROR_CODE
}

var errorTypeToErrorCodeMap = map[string]string{
	"ItemsOutOfStock":                   "3",  // ITEMS_OUT_OF_STOCK_ERROR_CODE
	"ActiveDuplicateOrder":              "5",  // MAKE_DUPLICATE_ORDER_ERROR_CODE
	"CatalogUniqueItemMaxLimitBreached": "30", // MAX_UNIQUE_ITEMS_LIMIT_BREACHED_ERROR_CODE
	"LogisticsPartnerMismatch":          "33", // LOGISTICS_PARTNER_MISMATCH_CODE_CONVENTIONAL
	"OrderValueBreached":                "34", // ORDER_VALUE_BREACH_ERROR_CODE
}

func getErrorCodeFromErrorIdAndErrorType(errorId, errorType string) string {
	errorCode, found := errorIdToErrorCodeMap[errorId]
	if found {
		return errorCode
	}

	errorCode, found = errorTypeToErrorCodeMap[errorType]
	if found {
		return errorCode
	}

	return errorId
}

func getO2CartTrackingPayload(event *o2carttracking.O2CartTracking) ([]*models.JEventsPayload, error) {
	eventName := event.GetEventName().GetValue()
	dataFetcher, found := o2CartGenericTransformationEvents[eventName]
	if !found || dataFetcher == nil {
		logger.Debugf("[getO2CartTrackingPayload] Event %s is not required for transformation ", eventName)
		return nil, nil
	}

	return dataFetcher(event)
}

func getO2CartCheckoutButtonTappedJEventPayload(event *o2carttracking.O2CartTracking) ([]*models.JEventsPayload, error) {
	resSubtotalMap := make(map[string]string)
	for _, resOrderAmount := range event.GetBillSummaryDetails().GetResOrderAmountComponents() {
		resSubtotalMap[resOrderAmount.GetResId().GetValue()] = resOrderAmount.GetItemTotal().String()
	}

	eventsData := []*models.JEventsPayload{}
	for _, resId := range event.GetResIds() {
		subtotal := resSubtotalMap[resId.GetValue()]

		eventData := &models.JEventsPayload{
			EventName: "O2CartCheckoutButtonTapped",
			Var1:      resId.GetValue(),
			Var2:      event.GetCheckoutSectionDetails().GetButtonAction().GetValue(),
			Var3:      subtotal,
			Var4:      event.GetPaymentDetails().GetPaymentMethodType().GetValue(),
		}

		eventsData = append(eventsData, eventData)
	}

	return eventsData, nil
}

func getO2OrderPlacedJEventPayload(event *o2carttracking.O2CartTracking) ([]*models.JEventsPayload, error) {
	eventsData := []*models.JEventsPayload{}
	for _, orderDetails := range event.GetOrderDetails() {
		eventData := &models.JEventsPayload{
			EventName: "OrderPlaced",
			Var1:      orderDetails.GetOrderId().GetValue(),
			Var2:      orderDetails.GetResId().GetValue(),
			Var3:      "delivery",
			Var4:      event.GetPaymentDetails().GetPaymentMethodType().GetValue(),
		}

		eventsData = append(eventsData, eventData)
	}

	return eventsData, nil
}

func getO2OrderConfirmedJEventPayload(event *o2carttracking.O2CartTracking) ([]*models.JEventsPayload, error) {
	eventsData := []*models.JEventsPayload{}
	for _, orderDetails := range event.GetOrderDetails() {
		eventData := &models.JEventsPayload{
			EventName: "O2OrderConfirmed",
			Var1:      orderDetails.GetOrderId().GetValue(),
			Var2:      orderDetails.GetResId().GetValue(),
			Var3:      "delivery",
			Var4:      event.GetPaymentDetails().GetPaymentMethodType().GetValue(),
		}

		eventsData = append(eventsData, eventData)
	}

	return eventsData, nil
}

func getBackendO2OrderPlacedJEventPayload(event *o2carttracking.O2CartTracking) ([]*models.JEventsPayload, error) {
	resGrandTotalMap := make(map[string]string)
	for _, resAmountComponent := range event.GetBillSummaryDetails().GetResOrderAmountComponents() {
		resGrandTotalMap[resAmountComponent.GetResId().GetValue()] = resAmountComponent.GetGrandTotal().String()
	}

	eventsData := []*models.JEventsPayload{}
	for _, orderDetails := range event.GetOrderDetails() {
		grandTotal := resGrandTotalMap[orderDetails.GetResId().GetValue()]

		backendEventData := &models.JEventsPayload{
			EventName: "Backend_OrderPlaced",
			Var1:      orderDetails.GetResId().GetValue(),
			Var2:      orderDetails.GetOrderId().GetValue(),
			Var3:      grandTotal,
		}

		eventsData = append(eventsData, backendEventData)
	}

	return eventsData, nil
}

func getO2CartPageLoadedJEventPayload(event *o2carttracking.O2CartTracking) ([]*models.JEventsPayload, error) {
	isCustomerPhoneVerified := "0"
	if event.GetIsCustomerPhoneVerified().GetValue() {
		isCustomerPhoneVerified = "1"
	}

	eventsData := []*models.JEventsPayload{}
	for _, resId := range event.GetResIds() {
		eventData := &models.JEventsPayload{
			EventName: "O2CartPageLoaded",
			Var1:      resId.GetValue(),
			Var2:      isCustomerPhoneVerified,
			Var3:      event.GetPaymentDetails().GetPaymentMethodType().GetValue(),
			Var4:      event.GetDeliveryAddress().GetAddressId().GetValue(),
			Var5:      event.GetDeliveryAddress().GetPlaceId().GetValue(),
			Var6:      event.GetDeliveryAddress().GetPlaceType().GetValue(),
		}

		eventsData = append(eventsData, eventData)
	}

	return eventsData, nil
}

func getBackendO2CartPageLoadedJEventPayload(event *o2carttracking.O2CartTracking) ([]*models.JEventsPayload, error) {
	resSubtotalMap := make(map[string]string)
	for _, resOrderAmount := range event.GetBillSummaryDetails().GetResOrderAmountComponents() {
		resSubtotalMap[resOrderAmount.GetResId().GetValue()] = resOrderAmount.GetItemTotal().String()
	}

	resVoucherMap := make(map[string]string)
	for _, benefits := range event.GetBillSummaryDetails().GetResBenefits() {
		for _, voucher := range benefits.GetVouchers() {
			if voucher.GetVoucherType() != o2carttracking.VoucherType_VOUCHER_TYPE_RES {
				continue
			}
			resVoucherMap[voucher.GetResId().GetValue()] = voucher.GetVoucherCode().GetValue()
			break
		}
	}

	cellId := event.GetDeliveryAddress().GetCellId().GetValue()
	if cellId == "" {
		cellId = event.GetUserAddress().GetCellId().GetValue()
	}

	eventsData := []*models.JEventsPayload{}
	for _, resId := range event.GetResIds() {
		subtotal := resSubtotalMap[resId.GetValue()]
		voucherCode := resVoucherMap[resId.GetValue()]

		backendEventData := &models.JEventsPayload{
			EventName: "BACKEND_O2CartPageLoaded",
			Var1:      resId.GetValue(),
			Var2:      subtotal,
			Var3:      voucherCode,
			Var4:      cellId,
		}

		eventsData = append(eventsData, backendEventData)
	}

	return eventsData, nil
}

func getO2CartPageLoadFailedJEventPayload(event *o2carttracking.O2CartTracking) ([]*models.JEventsPayload, error) {
	errorId := event.GetCartResponse().GetErrorDetails().GetErrorId().String()
	errorType := event.GetCartResponse().GetErrorDetails().GetTenantResponseErrorType().GetValue()
	errorMessage := event.GetCartResponse().GetErrorDetails().GetErrorMessage().String()

	source := "cart"
	if event.GetCartPhase() == o2carttracking.CartPhase_CART_PHASE_CHECKOUT {
		source = "make_order"
	}

	eventData := &models.JEventsPayload{
		EventName: "CART_ERROR_DIALOG_SHOWN",
		Var1:      getErrorCodeFromErrorIdAndErrorType(errorId, errorType),
		Var2:      errorMessage,
		Var3:      source,
	}

	return []*models.JEventsPayload{eventData}, nil
}

func getO2CheckoutStartedJEventPayload(event *o2carttracking.O2CartTracking) ([]*models.JEventsPayload, error) {
	isPremiumCheckoutFlow := "0"
	if event.GetPaymentDetails().GetIsPremiumCheckoutFlow().GetValue() {
		isPremiumCheckoutFlow = "1"
	}

	eventsData := []*models.JEventsPayload{}
	for _, resId := range event.GetResIds() {
		eventData := &models.JEventsPayload{
			EventName: "MakeOrderCallStarted",
			Var1:      event.GetPaymentDetails().GetPaymentMethodId().GetValue(),
			Var2:      event.GetFinalBillAmount().String(),
			Var3:      isPremiumCheckoutFlow,
			Var4:      resId.GetValue(),
		}

		eventsData = append(eventsData, eventData)
	}

	return eventsData, nil
}

func getO2CheckoutFailedJEventPayload(event *o2carttracking.O2CartTracking) ([]*models.JEventsPayload, error) {
	isPremiumCheckoutFlow := "0"
	if event.GetPaymentDetails().GetIsPremiumCheckoutFlow().GetValue() {
		isPremiumCheckoutFlow = "1"
	}

	eventsData := []*models.JEventsPayload{}
	for _, resId := range event.GetResIds() {
		eventData := &models.JEventsPayload{
			EventName: "MakeOrderCallFailed",
			Var1:      event.GetPaymentDetails().GetPaymentMethodId().GetValue(),
			Var2:      event.GetFinalBillAmount().String(),
			Var3:      isPremiumCheckoutFlow,
			Var4:      resId.GetValue(),
		}

		eventsData = append(eventsData, eventData)
	}

	return eventsData, nil
}

func getO2CheckoutSuccessJEventPayload(event *o2carttracking.O2CartTracking) ([]*models.JEventsPayload, error) {
	isPremiumCheckoutFlow := "0"
	if event.GetPaymentDetails().GetIsPremiumCheckoutFlow().GetValue() {
		isPremiumCheckoutFlow = "1"
	}

	eventsData := []*models.JEventsPayload{}
	for _, orderDetails := range event.GetOrderDetails() {
		eventData := &models.JEventsPayload{
			EventName: "MakeOrderCallSuccess",
			Var1:      orderDetails.GetOrderId().GetValue(),
			Var2:      event.GetPaymentDetails().GetPaymentMethodId().GetValue(),
			Var3:      event.GetFinalBillAmount().String(),
			Var4:      orderDetails.GetResId().GetValue(),
			Var5:      isPremiumCheckoutFlow,
		}

		eventsData = append(eventsData, eventData)
	}

	return eventsData, nil
}

func getO2OrderPaymentStartedJEventPayload(event *o2carttracking.O2CartTracking) ([]*models.JEventsPayload, error) {
	eventsData := []*models.JEventsPayload{}
	for _, orderDetails := range event.GetOrderDetails() {
		eventData := &models.JEventsPayload{
			EventName: "SdkMakePaymentCallStarted",
			Var1:      event.GetPaymentDetails().GetPaymentMethodId().GetValue(),
			Var2:      event.GetFinalBillAmount().String(),
			Var3:      orderDetails.GetOrderId().GetValue(),
		}

		eventsData = append(eventsData, eventData)
	}

	return eventsData, nil
}

func getO2OrderPaymentFailedJEventPayload(event *o2carttracking.O2CartTracking) ([]*models.JEventsPayload, error) {
	isPremiumCheckoutFlow := "0"
	if event.GetPaymentDetails().GetIsPremiumCheckoutFlow().GetValue() {
		isPremiumCheckoutFlow = "1"
	}

	eventsData := []*models.JEventsPayload{}
	for _, orderDetails := range event.GetOrderDetails() {
		eventData := &models.JEventsPayload{
			EventName: "SdkMakePaymentCallFailure",
			Var1:      event.GetPaymentDetails().GetPaymentMethodId().GetValue(),
			Var2:      isPremiumCheckoutFlow,
			Var3:      orderDetails.GetOrderId().GetValue(),
			Var4:      event.GetPaymentDetails().GetPaymentFailureReason().GetValue(),
		}

		eventsData = append(eventsData, eventData)
	}

	return eventsData, nil
}

func getO2OrderPaymentSuccessJEventPayload(event *o2carttracking.O2CartTracking) ([]*models.JEventsPayload, error) {
	isPremiumCheckoutFlow := "0"
	if event.GetPaymentDetails().GetIsPremiumCheckoutFlow().GetValue() {
		isPremiumCheckoutFlow = "1"
	}

	eventsData := []*models.JEventsPayload{}
	for _, orderDetails := range event.GetOrderDetails() {
		eventData := &models.JEventsPayload{
			EventName: "SdkMakePaymentCallSuccess",
			Var1:      orderDetails.GetOrderId().GetValue(),
			Var2:      isPremiumCheckoutFlow,
		}

		eventsData = append(eventsData, eventData)
	}

	return eventsData, nil
}

func getO2OrderPollingStartedJEventPayload(event *o2carttracking.O2CartTracking) ([]*models.JEventsPayload, error) {
	isPremiumCheckoutFlow := "0"
	if event.GetPaymentDetails().GetIsPremiumCheckoutFlow().GetValue() {
		isPremiumCheckoutFlow = "1"
	}

	eventsData := []*models.JEventsPayload{}
	for _, orderDetails := range event.GetOrderDetails() {
		eventData := &models.JEventsPayload{
			EventName: "CartPollingStarted",
			Var1:      event.GetPaymentDetails().GetPaymentMethodId().GetValue(),
			Var2:      orderDetails.GetOrderId().GetValue(),
			Var3:      isPremiumCheckoutFlow,
		}

		eventsData = append(eventsData, eventData)
	}

	return eventsData, nil
}

func getO2OrderPollingFailedJEventPayload(event *o2carttracking.O2CartTracking) ([]*models.JEventsPayload, error) {
	isPremiumCheckoutFlow := "0"
	if event.GetPaymentDetails().GetIsPremiumCheckoutFlow().GetValue() {
		isPremiumCheckoutFlow = "1"
	}

	eventsData := []*models.JEventsPayload{}
	for _, orderDetails := range event.GetOrderDetails() {
		eventData := &models.JEventsPayload{
			EventName: "CartPollingFailed",
			Var1:      event.GetPaymentDetails().GetPaymentMethodId().GetValue(),
			Var2:      orderDetails.GetOrderId().GetValue(),
			Var3:      isPremiumCheckoutFlow,
		}

		eventsData = append(eventsData, eventData)
	}

	return eventsData, nil
}

func getO2OrderPollingSuccessJEventPayload(event *o2carttracking.O2CartTracking) ([]*models.JEventsPayload, error) {
	isPremiumCheckoutFlow := "0"
	if event.GetPaymentDetails().GetIsPremiumCheckoutFlow().GetValue() {
		isPremiumCheckoutFlow = "1"
	}

	eventsData := []*models.JEventsPayload{}
	for _, orderDetails := range event.GetOrderDetails() {
		eventData := &models.JEventsPayload{
			EventName: "CartPollingSuccess",
			Var1:      event.GetPaymentDetails().GetPaymentMethodId().GetValue(),
			Var2:      orderDetails.GetOrderId().GetValue(),
			Var3:      isPremiumCheckoutFlow,
		}

		eventsData = append(eventsData, eventData)
	}

	return eventsData, nil
}
