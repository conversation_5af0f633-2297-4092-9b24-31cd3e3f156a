package transformers

import (
	"errors"
	"fmt"
	"reflect"
	"strings"

	log "github.com/Zomato/go/logger"
	"go.uber.org/zap"
)

// TODO: Move to event registry model from these hard-coded models
type FGStandardEvent struct {
	AnonymousID string     `json:"anonymousId,omitempty"`
	UserID      string     `json:"userId,omitempty"`
	Channel     string     `json:"channel,omitempty"`
	Context     Context    `json:"context,omitempty"`
	Type        string     `json:"type,omitempty"`
	CreatedAt   string     `json:"createdAt,omitempty"`
	SentAt      string     `json:"sentAt,omitempty"`
	EventName   string     `json:"event,omitempty"`
	MessageID   string     `json:"messageId,omitempty"`
	Properties  Properties `json:"properties,omitempty"`
}

type Context struct {
	Device   Device     `json:"device"`
	OS       OS         `json:"os"`
	Location Location   `json:"location"`
	Traits   Properties `json:"traits"`
	Segments Properties `json:"segments"`
}

type Device struct {
	AdvertisingID string `json:"advertisingId"`
	Type          string `json:"type"`
	ID            string `json:"id,omitempty"`
}
type OS struct {
	Name      string `json:"name"`
	OsVersion string `json:"version"`
}

type Location struct {
	Country string `json:"country"`
}

type Properties map[string]interface{}

type EventMapping struct {
	DestKey    string `json:"destKey"`
	SourceKeys string `json:"sourceKeys"`
	Required   bool   `json:"required,omitempty"`
}

// GetField retrieves the value of a field from a struct using reflection
func GetField(obj interface{}, fieldName string) (interface{}, error) {
	v := reflect.ValueOf(obj)
	t := reflect.TypeOf(obj)

	// Traverse nested fields if needed
	fields := strings.Split(fieldName, ".")
	for _, field := range fields {
		v = reflect.Indirect(v)

		// Check if the current field is a map
		if v.Kind() == reflect.Map {
			mapKey := reflect.ValueOf(field)
			mapValue := v.MapIndex(mapKey)

			if !mapValue.IsValid() {
				return nil, fmt.Errorf("field %s not found", fieldName)
			}

			v = mapValue
		} else {
			structField, found := t.FieldByName(field)
			if !found {
				return nil, fmt.Errorf("field %s not found", fieldName)
			}

			v = v.FieldByName(field)
			if !v.IsValid() {
				return nil, fmt.Errorf("field %s not found", fieldName)
			}

			// Check if the field is exported
			if structField.PkgPath != "" {
				return nil, fmt.Errorf("field %s is not exported", fieldName)
			}
		}

		if !v.CanInterface() {
			return nil, fmt.Errorf("cannot access field %s", fieldName)
		}

		t = v.Type()
	}

	return v.Interface(), nil
}

// ConstructPayload constructs the payload for the event based on the provided mapping
func ConstructPayload(message FGStandardEvent, mappingJson []EventMapping) (interface{}, error) {
	payload := make(map[string]interface{})
	for _, mapping := range mappingJson {
		fieldValue, err := GetField(message, mapping.SourceKeys)
		if err != nil {
			log.Debug("Error while getting field", zap.Any("error", err))
			if mapping.Required {
				log.Error("Unable to get required field. Returning payload as nil", zap.Any("key", mapping.SourceKeys))
				return nil, errors.New("Unable to get required field: " + mapping.SourceKeys)
			}
			continue
		}
		payload[mapping.DestKey] = fieldValue
	}
	return payload, nil
}

// ExtractCustomFields extracts the custom fields from the message and adds them to the destination map
func ExtractCustomFields(message interface{}, destination map[string]interface{}, keys []string, exclusionFields []string) map[string]interface{} {
	for _, key := range keys {
		messageContext, err := GetField(message, key)
		if err == nil {
			// TODO: Figure out why contextMap, isMap := messageContext.(map[string]interface{}) does not work
			contextMap, isMap := messageContext.(Properties)
			if isMap {
				for k, v := range contextMap {
					if !Contains(exclusionFields, k) {
						destination[k] = v
					}
				}
			}
		}
	}

	return destination
}

func Contains(slice []string, element string) bool {
	for _, item := range slice {
		if item == element {
			return true
		}
	}
	return false
}
