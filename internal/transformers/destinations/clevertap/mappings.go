package clevertap

import (
	"github.com/Zomato/flash-gateway/internal/transformers"
)

var EcomMapping = []transformers.EventMapping{
	{
		DestKey: "Charged ID",
		SourceKeys: "Properties.checkout_id",
		Required: false,
	},
	{
		DestKey: "Amount",
		SourceKeys: "Properties.revenue",
		Required: false,
	},
	{
		DestKey: "Items",
		SourceKeys: "Properties.products",
		Required: false,
	},
}

var TrackMapping = []transformers.EventMapping{
	{
		DestKey: "evtName",
		SourceKeys: "EventName",
		Required: true,
	},
	{
		DestKey: "evtData",
		SourceKeys: "Properties",
		Required: true,
	},
	{
		DestKey: "ts",
		SourceKeys: "Properties.ts",
		Required: false,
	},
}

var IdentifyMapping = []transformers.EventMapping{
	{
		DestKey: "Email",
		SourceKeys: "email",
		Required: false,
	},
	{
		DestKey: "Name",
		SourceKeys: "Context.Traits.name",
		Required: false,
	},
	{
		DestKey: "Phone",
		SourceKeys: "Context.Traits.phone",
		Required: false,
	},
	{
		DestKey: "Gender",
		SourceKeys: "Context.Traits.gender",
		Required: false,
	},
	{
		DestKey: "DOB",
		SourceKeys: "Context.Traits.birthday",
		Required: false,
	},
	{
		DestKey: "Employed",
		SourceKeys: "Context.Traits.employed",
		Required: false,
	},
	{
		DestKey: "Education",
		SourceKeys: "Context.Traits.education",
		Required: false,
	},
	{
		DestKey: "Married",
		SourceKeys: "Context.Traits.married",
		Required: false,
	},
	{
		DestKey: "Customer Type",
		SourceKeys: "Context.Traits.customerType",
		Required: false,
	},
	{
		DestKey: "Photo",
		SourceKeys: "Context.Traits.avatar",
		Required: false,
	},
}
