package clevertap

import (
	"errors"
	"fmt"

	"github.com/Zomato/flash-gateway/internal/transformers"
	"github.com/Zomato/flash-gateway/pkg/services/clevertap"
	jsoniter "github.com/json-iterator/go"
)

var json = jsoniter.ConfigCompatibleWithStandardLibrary

// These are clevertap specific properties we are already mapping using IdentifyMapping
var CLEVERTAP_DEFAULT_MAPPING_EXCLUSION = []string{
	"name",
	"phone",
	"gender",
	"birthday",
	"employed",
	"education",
	"married",
	"customerType",
	"avatar",
}

func TransformEvent(message []byte) (clevertap.ClevertapEventData, error) {
	var fgEvent transformers.FGStandardEvent
	var ctEvent clevertap.ClevertapEventData
	err := json.Unmarshal(message, &fgEvent)
	if err != nil {
		return clevertap.ClevertapEventData{}, errors.New("Unable to unmarshal message. Error: " + err.Error())
	}

	if fgEvent.Type == "identify" {
		profile, err := transformers.ConstructPayload(fgEvent, IdentifyMapping)
		if err != nil {
			return clevertap.ClevertapEventData{}, err
		}

		ctEvent.ProfileData = profile.(map[string]interface{})

		ctEvent.ProfileData = transformers.ExtractCustomFields(fgEvent, ctEvent.ProfileData, []string{"Context.Segments"}, []string{})
		// Incase some traits properties are being sent in segments as well, then traits should take precedence
		ctEvent.ProfileData = transformers.ExtractCustomFields(fgEvent, ctEvent.ProfileData, []string{"Context.Traits"}, CLEVERTAP_DEFAULT_MAPPING_EXCLUSION)
		ctEvent.Type = "profile"
	} else {
		// TODO: Fix hardcoding in v2. We referred rudder for v1 https://github.com/rudderlabs/rudder-transformer/blob/develop/src/v0/destinations/clevertap/transform.js#L295
		if fgEvent.EventName == "Order Completed" {
			ctEvent.EventName = "Charged"
			payload, err := transformers.ConstructPayload(fgEvent, EcomMapping)
			if err != nil {
				return clevertap.ClevertapEventData{}, err
			}

			ctEvent.EventData = payload.(map[string]interface{})

			ctEvent.EventData = transformers.ExtractCustomFields(fgEvent, ctEvent.EventData, []string{"Properties"}, []string{"checkout_id", "revenue", "products", "ts"})

		} else if fgEvent.EventName == "Order Cancelled" || fgEvent.EventName == "Order Delivered" {
			payload, err := transformers.ConstructPayload(fgEvent, TrackMapping)
			if err != nil {
				return clevertap.ClevertapEventData{}, err
			}

			jsonPayload, err := json.Marshal(payload)
			if err != nil {
				return clevertap.ClevertapEventData{}, fmt.Errorf("failed to marshal payload to JSON: %v", err)
			}

			err = json.Unmarshal(jsonPayload, &ctEvent)
			if err != nil {
				return clevertap.ClevertapEventData{}, fmt.Errorf("failed to unmarshal JSON to struct: %v", err)
			}
		} else {
			return clevertap.ClevertapEventData{}, errors.New("Not implemented event type: " + fgEvent.Type)
		}
		ctEvent.Type = "event"
	}

	ctEvent.Identity = fgEvent.UserID

	return ctEvent, nil
}
