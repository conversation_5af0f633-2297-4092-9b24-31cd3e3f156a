package helpers

import (
	"context"

	ztracer "github.com/Zomato/flash-gateway/pkg/tracer"
	log "github.com/Zomato/go/logger"
	tracer "github.com/Zomato/go/tracer"
	"github.com/spf13/viper"
	"go.uber.org/zap"
)

var (
	// Tables stores the tables as defined in jobs.json
	Tables []string

	// V2FormatTables stores the tables which use the v2 format
	V2FormatTables []string

	// TablesEventsMap stores info for where to push data for a event
	TablesEventsMap = make(map[string]Table)

	// ProducersToSetup producers to setup
	ProducersToSetup = []string{}
)

type Filter struct {
	FieldName string
	Values    []string
}

// TopicProducer defines a kafka producer and its topics on which data has
// to published in a given format
type TopicProducer struct {
	Topics          []string
	Producer        string
	Format          string
	Transformations map[string]string
	Filters         []Filter
}

// Table stores jobs.json
type Table struct {
	EventNameField     string
	AllowedEvents      map[string][]TopicProducer
	AllowedEventsStore []string
}

// AddProducerToSetup adds extra producers to setup
func AddProducerToSetup(label string) {
	ProducersToSetup = append(ProducersToSetup, label)
}

// TablesInit inits the jobgroup data
func TablesInit(jobGroup string, ctx context.Context) {
	// verify job group
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "Helpers/TablesInit", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}
	log.Warn(jobGroup)
	if group := viper.Get("job_groups." + jobGroup); group == nil {
		panic("Invalid job group.")
	}

	V2FormatTables = viper.GetStringSlice("job_groups." + jobGroup + ".v2_format_tables")
	log.Debug("V2FormatTables", zap.Any("V2FormatTables", V2FormatTables))

	for tableName, value := range viper.Get("job_groups." + jobGroup + ".tables").(map[string]interface{}) {
		Tables = append(Tables, tableName)
		tableProp, _ := value.(map[string]interface{})

		table := Table{
			EventNameField:     tableProp["event_name_field"].(string),
			AllowedEvents:      make(map[string][]TopicProducer),
			AllowedEventsStore: make([]string, 0),
		}

		allowedEvents := make(map[string][]TopicProducer)
		for _, allowedEvent := range tableProp["allowed_events"].([]interface{}) {
			allowedEventCasted := allowedEvent.(map[string]interface{})
			producer := allowedEventCasted["producer"].(string)

			for _, topicEventMap := range allowedEventCasted["topic_events_map"].([]interface{}) {
				topicEventMapCasted, _ := topicEventMap.(map[string]interface{})
				topic := topicEventMapCasted["topic"].(string)
				format := topicEventMapCasted["format"].(string)
				events := topicEventMapCasted["events"].([]interface{})

				transformations := make(map[string]string)
				if _, ok := topicEventMapCasted["transformations"]; ok {
					transformationsInterface := topicEventMapCasted["transformations"].(map[string]interface{})
					for k, v := range transformationsInterface {
						transformations[k] = v.(string)
					}
				}

				filters := make([]Filter, 0)
				if _, ok := topicEventMapCasted["filters"]; ok {
					for fieldName, valueInterface := range topicEventMapCasted["filters"].(map[string]interface{}) {
						filter := Filter{
							FieldName: fieldName,
							Values:    make([]string, 0),
						}

						for _, value := range valueInterface.([]interface{}) {
							filter.Values = append(filter.Values, value.(string))
						}

						filters = append(filters, filter)
					}
				}

				topicProducer := TopicProducer{
					Format:          format,
					Topics:          []string{topic},
					Producer:        producer,
					Transformations: transformations,
					Filters:         filters,
				}

				ProducersToSetup = append(ProducersToSetup, producer)

				for _, event := range events {
					eventName, _ := event.(string)

					if val, ok := allowedEvents[eventName]; ok {
						allowedEvents[eventName] = append(val, topicProducer)
					} else {
						allowedEvents[eventName] = []TopicProducer{topicProducer}
					}

					table.AllowedEventsStore = append(table.AllowedEventsStore, eventName)
				}
			}
		}

		table.AllowedEvents = allowedEvents
		table.AllowedEventsStore = UniqueStringSlice(table.AllowedEventsStore)
		TablesEventsMap[tableName] = table
	}

	ProducersToSetup = UniqueStringSlice(ProducersToSetup)
}
