package helpers

import (
	"github.com/Zomato/flash-gateway/internal/constants"
)

var (
	skipTenantsForLegacyFlowCheck map[string]struct{} = map[string]struct{}{

		constants.FEEDINGINDIA_TENANT: struct{}{},
		constants.DISTRICT_TENANT:     struct{}{},
		constants.ZOMATO_TENANT:       struct{}{},
		constants.HYPERPURE_TENANT:    struct{}{},
	}
)

var (
	whiteListedBackendTables map[string]struct{} = map[string]struct{}{
		`aaaq_scores`:                                      struct{}{},
		`abexperiment`:                                     struct{}{},
		`ad_capacity_ledgers`:                              struct{}{},
		`ad_cell_capacities`:                               struct{}{},
		`ad_cell_pricings`:                                 struct{}{},
		`ad_restaurant_cells`:                              struct{}{},
		`ad_restaurant_ctrs`:                               struct{}{},
		`ad_serving_campaign_events`:                       struct{}{},
		`ad_serving_events`:                                struct{}{},
		`ad_serving_experiments`:                           struct{}{},
		`ad_shadow_events`:                                 struct{}{},
		`address_predictions`:                              struct{}{},
		`ads_booked_capacities`:                            struct{}{},
		`ads_campaigns_tracking`:                           struct{}{},
		`ads_central_web_tracking`:                         struct{}{},
		`ads_cpx_tracking`:                                 struct{}{},
		`ads_doomsday`:                                     struct{}{},
		`ads_grpc_tracking`:                                struct{}{},
		`ads_serving_logs`:                                 struct{}{},
		`ads_studio_tracking`:                              struct{}{},
		`ads_target_events`:                                struct{}{},
		`aerobar_entities_app_tracking`:                    struct{}{},
		`aerobar_service_consumer_events`:                  struct{}{},
		`aerobar_service_migration_events`:                 struct{}{},
		`ai_blinkit_classifier_service_results`:            struct{}{},
		`ai_copilots_demo_logs`:                            struct{}{},
		`amplify_tracking`:                                 struct{}{},
		`api_trace_events`:                                 struct{}{},
		`apitrack`:                                         struct{}{},
		`app_auth_metrics`:                                 struct{}{},
		`app_debug_events`:                                 struct{}{},
		`app_error_metrics`:                                struct{}{},
		`app_location_metrics`:                             struct{}{},
		`app_login_metrics`:                                struct{}{},
		`app_notification_events`:                          struct{}{},
		`app_order_transaction_metrics`:                    struct{}{},
		`app_parsing_metrics`:                              struct{}{},
		`app_performance_metrics`:                          struct{}{},
		`app_request_metrics`:                              struct{}{},
		`app_ssl_pinning_metrics`:                          struct{}{},
		`app_ui_events_tracking_metrics`:                   struct{}{},
		`appsflyer_event_log`:                              struct{}{},
		`appsflyer_events`:                                 struct{}{},
		`apsalar_device_export`:                            struct{}{},
		`apsalar_events`:                                   struct{}{},
		`ar_menu`:                                          struct{}{},
		`athena_api_events`:                                struct{}{},
		`athena_dev_events`:                                struct{}{},
		`auth_audit_logs`:                                  struct{}{},
		`auth_sdk_logs`:                                    struct{}{},
		`authentication_login_api_response`:                struct{}{},
		`autopilot_gateway_selection_events`:               struct{}{},
		`autopilot_transaction_mapping`:                    struct{}{},
		`autosuggest_tracking`:                             struct{}{},
		`autosuggestion_log`:                               struct{}{},
		`b2b_merchant_order_pings`:                         struct{}{},
		`b2b_order_events`:                                 struct{}{},
		`benchmark_events`:                                 struct{}{},
		`billing_aggregator_request_logs`:                  struct{}{},
		`billing_logs`:                                     struct{}{},
		`bin_inventory_updates`:                            struct{}{},
		`black_white_list_tracking`:                        struct{}{},
		`bulk_campaign_creation_row_events`:                struct{}{},
		`butterfly_api_logs`:                               struct{}{},
		`button`:                                           struct{}{},
		`campaign_cell_level_searches`:                     struct{}{},
		`campaign_cycle_events`:                            struct{}{},
		`cart_backend_events`:                              struct{}{},
		`cart_reconciliation_events`:                       struct{}{},
		`cart_tracking`:                                    struct{}{},
		`cart_ui_events`:                                   struct{}{},
		`cart_updates`:                                     struct{}{},
		`cart_updates_v2`:                                  struct{}{},
		`carthero_driver_ratings`:                          struct{}{},
		`carthero_low_orders`:                              struct{}{},
		`catalogue_dish_mapping_events`:                    struct{}{},
		`catalogue_dish_tagging_events`:                    struct{}{},
		`catalogue_dish_taggings`:                          struct{}{},
		`cell_candidate_res_mapping_validation_events`:     struct{}{},
		`cell_distance_events`:                             struct{}{},
		`cell_logs_events`:                                 struct{}{},
		`charge_service_events`:                            struct{}{},
		`chat_agent_metrics`:                               struct{}{},
		`chat_agent_metrics_shadow`:                        struct{}{},
		`chat_events`:                                      struct{}{},
		`chat_refund_metrics`:                              struct{}{},
		`cod_realtime_actions`:                             struct{}{},
		`code_upload_metrics`:                              struct{}{},
		`code_upload_user_metrics`:                         struct{}{},
		`collectionclick`:                                  struct{}{},
		`composite_order_events`:                           struct{}{},
		`config_tracking`:                                  struct{}{},
		`consumer_order_events`:                            struct{}{},
		`credit_line_events`:                               struct{}{},
		`crystal_order_tracking`:                           struct{}{},
		`csp_reports`:                                      struct{}{},
		`cx_support_karma_tracking`:                        struct{}{},
		`da_distance_hotspot_tracking`:                     struct{}{},
		`datadog_alerts`:                                   struct{}{},
		`dc_events`:                                        struct{}{},
		`ddt_salience_tracking`:                            struct{}{},
		`debug_search_serviceability`:                      struct{}{},
		`deeplink_redirection_tracking`:                    struct{}{},
		`deeplink_tracking`:                                struct{}{},
		`delivery_area_rules`:                              struct{}{},
		`delivery_charge_rules`:                            struct{}{},
		`delivery_driver_actions`:                          struct{}{},
		`delivery_driver_state_change_events`:              struct{}{},
		`delivery_surge_configs`:                           struct{}{},
		`demand_manager_irctc_events`:                      struct{}{},
		`demand_manager_logistics_level_automation_events`: struct{}{},
		`demand_manager_scheduled_orders_tracking`:         struct{}{},
		`demand_manager_tracking`:                          struct{}{},
		`device_info_tracking`:                             struct{}{},
		`dh_eta_tracking`:                                  struct{}{},
		`dining_admin_web_tracking`:                        struct{}{},
		`dining_ads_serving_details`:                       struct{}{},
		`dining_ads_serving_events`:                        struct{}{},
		`dining_catalog_tracking`:                          struct{}{},
		`dining_contract_tracking`:                         struct{}{},
		`dining_events_reward_actions_tracking`:            struct{}{},
		`dining_inventory_update_events`:                   struct{}{},
		`dining_listing_page_tracking`:                     struct{}{},
		`dining_merchant_app_tracking`:                     struct{}{},
		`dining_merchant_product_tracking`:                 struct{}{},
		`dining_order_events`:                              struct{}{},
		`dining_partner_music_data`:                        struct{}{},
		`dining_pay_tracking`:                              struct{}{},
		`dining_pay_transaction_events`:                    struct{}{},
		`dining_pos_tracking`:                              struct{}{},
		`dining_product_config_update_events`:              struct{}{},
		`dining_product_tracking`:                          struct{}{},
		`dining_res_page_tracking`:                         struct{}{},
		`dining_search_indexing_events`:                    struct{}{},
		`dining_search_tracking`:                           struct{}{},
		`dining_service_eta_tracking`:                      struct{}{},
		`dining_service_events`:                            struct{}{},
		`dining_sync_business_event`:                       struct{}{},
		`dining_table_reservation_tracking`:                struct{}{},
		`dining_tr_transaction_events`:                     struct{}{},
		`dining_vcs_events`:                                struct{}{},
		`discount_info`:                                    struct{}{},
		`discount_mismatch_info`:                           struct{}{},
		`dish_tagging_res_menu`:                            struct{}{},
		`dlos_live_tracking`:                               struct{}{},
		`driver_accounting_ledgers`:                        struct{}{},
		`driver_accounting_reports`:                        struct{}{},
		`driver_assets_issuances`:                          struct{}{},
		`driver_authentication_api_response`:               struct{}{},
		`driver_cash_deposit_events`:                       struct{}{},
		`driver_cash_limits`:                               struct{}{},
		`driver_creation_events`:                           struct{}{},
		`driver_ev_leads`:                                  struct{}{},
		`driver_excess_cashes`:                             struct{}{},
		`driver_insurance_failures`:                        struct{}{},
		`driver_match_stats`:                               struct{}{},
		`driver_milestones`:                                struct{}{},
		`driver_notifications`:                             struct{}{},
		`driver_offer_payouts`:                             struct{}{},
		// `driver_offer_progress_tracking`:                     struct{}{},
		`driver_onboarded_events`:                            struct{}{},
		`driver_order_store_events`:                          struct{}{},
		`driver_pings`:                                       struct{}{},
		`driver_pings_back`:                                  struct{}{},
		`driver_pings_shadow`:                                struct{}{},
		`driver_profile_verification_events`:                 struct{}{},
		`driver_referral_notifications`:                      struct{}{},
		`driver_rented_vehicle_events`:                       struct{}{},
		`driver_selfie_events`:                               struct{}{},
		`driver_selfie_events_v2`:                            struct{}{},
		`driver_service_mappings`:                            struct{}{},
		`driver_session_events`:                              struct{}{},
		`driver_settlement_ledgers`:                          struct{}{},
		`driver_slot_events`:                                 struct{}{},
		`driver_state_update_events`:                         struct{}{},
		`driver_status_api_response`:                         struct{}{},
		`driver_support_request`:                             struct{}{},
		`driver_task_update_events`:                          struct{}{},
		`driver_trip_pings_analysis`:                         struct{}{},
		`driver_wallet_breach_status_events`:                 struct{}{},
		`driver_wallets`:                                     struct{}{},
		`dynamic_delivery_area_computation_logs`:             struct{}{},
		`dynamic_delivery_area_events`:                       struct{}{},
		`dynamic_delivery_area_log`:                          struct{}{},
		`dynamic_delivery_area_logs`:                         struct{}{},
		`dynamic_dish_filters`:                               struct{}{},
		`email_tracking`:                                     struct{}{},
		`emailtracking`:                                      struct{}{},
		`eta_tracking`:                                       struct{}{},
		`eternal_code_insights`:                              struct{}{},
		`eternal_form_request_tracking`:                      struct{}{},
		`everyday_order_scheduling`:                          struct{}{},
		`everyday_ui_events`:                                 struct{}{},
		`everyday_user_interaction`:                          struct{}{},
		`experimentation_package_shadow`:                     struct{}{},
		`express_slot_listing_events`:                        struct{}{},
		`external_chat_agent_metrics`:                        struct{}{},
		`firefly_app_events`:                                 struct{}{},
		`firefly_logs`:                                       struct{}{},
		`firefly_voice_bot_events`:                           struct{}{},
		`fitso_booking_events`:                               struct{}{},
		`fitso_events`:                                       struct{}{},
		`fitso_login_events`:                                 struct{}{},
		`fitso_pageviews`:                                    struct{}{},
		`fitso_purchase_events`:                              struct{}{},
		`fleet_coach_app_events`:                             struct{}{},
		`fleet_partner_web_tracking`:                         struct{}{},
		`flywheel_attribution_events`:                        struct{}{},
		`flywheel_audiences`:                                 struct{}{},
		`flywheel_brand_update_events`:                       struct{}{},
		`flywheel_campaign_estimates`:                        struct{}{},
		`flywheel_merchant_comms_tracking`:                   struct{}{},
		`flywheel_reporting_campaign_metadata`:               struct{}{},
		`flywheel_reporting_insights`:                        struct{}{},
		`flywheel_third_party_logs`:                          struct{}{},
		`fraud_check_shadow_logs`:                            struct{}{},
		`gamification_general_events`:                        struct{}{},
		`gc_claim_tracking`:                                  struct{}{},
		`gc_purchase_tracking`:                               struct{}{},
		`geofence_targeting`:                                 struct{}{},
		`geofences`:                                          struct{}{},
		`gift_card_events`:                                   struct{}{},
		`google_weather_api_predictions`:                     struct{}{},
		`graph_events`:                                       struct{}{},
		`group_ordering_ui_events`:                           struct{}{},
		`hp_predictions_tracking`:                            struct{}{},
		`hp_scm_event_tracking`:                              struct{}{},
		`hand_cricket_metrics`:                               struct{}{},
		`hermes_app_lifecycle_events`:                        struct{}{},
		`hermes_batching_rejections`:                         struct{}{},
		`hermes_batching_success_pairs`:                      struct{}{},
		`hermes_cell_speed_stats`:                            struct{}{},
		`hermes_cell_stats`:                                  struct{}{},
		`hermes_driver_alerts`:                               struct{}{},
		`hermes_driver_match_stats`:                          struct{}{},
		`hermes_driver_rejections`:                           struct{}{},
		`hermes_driver_state_updates`:                        struct{}{},
		`hermes_drop_polygon_stats`:                          struct{}{},
		`hermes_dynamic_orders`:                              struct{}{},
		`hermes_entity_stats`:                                struct{}{},
		`hermes_experiment_stats`:                            struct{}{},
		`hermes_iteration_drivers`:                           struct{}{},
		`hermes_iteration_groups`:                            struct{}{},
		`hermes_iteration_orders`:                            struct{}{},
		`hermes_matcher_states`:                              struct{}{},
		`hermes_ml_model_predictions`:                        struct{}{},
		`hermes_next_order_prediction`:                       struct{}{},
		`hermes_potential_order_universe`:                    struct{}{},
		`hermes_potential_orders`:                            struct{}{},
		`hermes_potential_orders_model`:                      struct{}{},
		`hermes_raw_potential_order_stats`:                   struct{}{},
		`hermes_relaxation_rules`:                            struct{}{},
		`hermes_swapping_rejections`:                         struct{}{},
		`hotspot_data`:                                       struct{}{},
		`hotspot_live_tracking`:                              struct{}{},
		`hotspot_tracking`:                                   struct{}{},
		`hp_account_based_price_error_tracking`:              struct{}{},
		`hp_app_tracking`:                                    struct{}{},
		`hp_assignment_user_event_tracking`:                  struct{}{},
		`hp_foodpark_event_tracking`:                         struct{}{},
		`hp_negative_margin_price`:                           struct{}{},
		`hp_search_experiment_tracking`:                      struct{}{},
		`hp_view_tracking`:                                   struct{}{},
		`hypergraph_edges`:                                   struct{}{},
		`hypergraph_nodes`:                                   struct{}{},
		`hypergraph_true_new`:                                struct{}{},
		`hyperpure_clicks`:                                   struct{}{},
		`hyperpure_credit_events`:                            struct{}{},
		`hyperpure_entity_events`:                            struct{}{},
		`hyperpure_external_price_input`:                     struct{}{},
		`hyperpure_ledger_events`:                            struct{}{},
		`hyperpure_location_tracking`:                        struct{}{},
		`hyperpure_order_events`:                             struct{}{},
		`hyperpure_product_addition_attempts`:                struct{}{},
		`hyperpure_push_notifications`:                       struct{}{},
		`hyperpure_ticket_events`:                            struct{}{},
		`instructions_tracking`:                              struct{}{},
		`intercity_order_tracking`:                           struct{}{},
		`intercity_ruleengine_events`:                        struct{}{},
		`intercity_throttling_events`:                        struct{}{},
		`intercity_tracking`:                                 struct{}{},
		`interim_pro_membership_tracking`:                    struct{}{},
		`ios_metrickit_metrics`:                              struct{}{},
		`jadtracking`:                                        struct{}{},
		`jadtracking_serves`:                                 struct{}{},
		`jevent`:                                             struct{}{},
		`jevent_rt`:                                          struct{}{},
		`jumbo_alert_bridge_meta`:                            struct{}{},
		`jumbo_failed_events`:                                struct{}{},
		`karma_bulk_ops_tracking`:                            struct{}{},
		`karma_clients_unified_methods`:                      struct{}{},
		`karma_enforcer_events`:                              struct{}{},
		`karma_events`:                                       struct{}{},
		`karma_label_events`:                                 struct{}{},
		`karma_tracking`:                                     struct{}{},
		`karma_user_attributes`:                              struct{}{},
		`kitchen_bin_events_tracking`:                        struct{}{},
		`kitchen_dish_item_events`:                           struct{}{},
		`kitchen_dish_item_events_tracking`:                  struct{}{},
		`kitchen_inventory_alerts`:                           struct{}{},
		`kitchen_inventory_tracking`:                         struct{}{},
		`kitchen_roster_tracking`:                            struct{}{},
		`legal_entity_change_events`:                         struct{}{},
		`lifeline_events`:                                    struct{}{},
		`live_order_service_events`:                          struct{}{},
		`live_order_service_recon`:                           struct{}{},
		`location_change_log_events`:                         struct{}{},
		`location_events`:                                    struct{}{},
		`location_general_events`:                            struct{}{},
		`location_migration_events`:                          struct{}{},
		`location_search`:                                    struct{}{},
		`logistics_accounting_cash_ledger`:                   struct{}{},
		`logistics_accounting_event_ledger`:                  struct{}{},
		`logistics_accounting_offers`:                        struct{}{},
		`logistics_accounting_pocket_diff_events`:            struct{}{},
		`logistics_accounting_pocket_events`:                 struct{}{},
		`logistics_dh_dda_events`:                            struct{}{},
		`logistics_migration_events`:                         struct{}{},
		`logistics_mqtt_auth_tracking`:                       struct{}{},
		`logistics_order_audit_events`:                       struct{}{},
		`logistics_order_state_events`:                       struct{}{},
		`logistics_payment_callbacks`:                        struct{}{},
		`logistics_quality_events`:                           struct{}{},
		`logistics_serviceability_events`:                    struct{}{},
		`logistics_shutdown_events`:                          struct{}{},
		`logistics_support_audit_trails`:                     struct{}{},
		`logistics_support_live_order_automation_events`:     struct{}{},
		`logs_fleet_coach_meetings`:                          struct{}{},
		`logs_user_grievances`:                               struct{}{},
		`loki_events`:                                        struct{}{},
		`map_update_events_tracking`:                         struct{}{},
		`match_score_events`:                                 struct{}{},
		`menu_entities`:                                      struct{}{},
		`menu_entities_app_tracking`:                         struct{}{},
		`menu_fab_rail`:                                      struct{}{},
		`menu_moderation_tracking`:                           struct{}{},
		`menu_reco_tracking`:                                 struct{}{},
		`menu_res_chain_tracking`:                            struct{}{},
		`menu_res_details_app_tracking`:                      struct{}{},
		`menu_status_tracking`:                               struct{}{},
		`menu_story_tracking`:                                struct{}{},
		`menu_tool_update_tracking`:                          struct{}{},
		`menu_tracking`:                                      struct{}{},
		`menu_tracking_events`:                               struct{}{},
		`menu_update_tracking`:                               struct{}{},
		`merchant_api_gateway_logging`:                       struct{}{},
		`merchant_app_events`:                                struct{}{},
		`merchant_bulk_ops_events`:                           struct{}{},
		`merchant_data_requests_tracking`:                    struct{}{},
		`merchant_dsa_events`:                                struct{}{},
		`merchant_escalation_events`:                         struct{}{},
		`merchant_event_scheduler`:                           struct{}{},
		`merchant_generic_optins`:                            struct{}{},
		`merchant_gst_refresh_event`:                         struct{}{},
		`merchant_meta_events`:                               struct{}{},
		`merchant_order_events`:                              struct{}{},
		`merchant_promo_recommendation`:                      struct{}{},
		`merchant_ratecard_events`:                           struct{}{},
		`merchant_relay_events`:                              struct{}{},
		`merchant_replacement_offers`:                        struct{}{},
		`merchant_serviceability_migration_events`:           struct{}{},
		`mint_tabs_events`:                                   struct{}{},
		`ml_data_moderation_tool_events`:                     struct{}{},
		`model_rain_reporting`:                               struct{}{},
		`mov_events`:                                         struct{}{},
		`movies_events`:                                      struct{}{},
		`ms_contract`:                                        struct{}{},
		`ms_rate_card`:                                       struct{}{},
		`ms_third_party_api_data`:                            struct{}{},
		`mx_ads_callback`:                                    struct{}{},
		`mx_ads_ivr_status`:                                  struct{}{},
		`mx_comms_service_events`:                            struct{}{},
		`mx_gamification_task_group_changelog_events`:        struct{}{},
		`mx_onboarding_events`:                               struct{}{},
		`mx_order_changelog_events`:                          struct{}{},
		`mx_order_svc_events`:                                struct{}{},
		`mx_outlet_changelog_events`:                         struct{}{},
		`mx_outlet_snapshot_events`:                          struct{}{},
		`mx_policy_events`:                                   struct{}{},
		`mx_pos_integration_menu_events`:                     struct{}{},
		`mx_pos_integration_order_events`:                    struct{}{},
		`mx_pos_integration_outlet_events`:                   struct{}{},
		`mx_pos_service_migration_events`:                    struct{}{},
		`mx_web_engagement_tracking`:                         struct{}{},
		`mysql_tables_metadata`:                              struct{}{},
		`navigation_imu_data_collection`:                     struct{}{},
		`navigation_wifi_data_collection`:                    struct{}{},
		`number_masking_failures`:                            struct{}{},
		`o2_campaign`:                                        struct{}{},
		`o2_cart_req_tracking`:                               struct{}{},
		`o2_cart_tracking`:                                   struct{}{},
		`o2_cart_ui_tracking`:                                struct{}{},
		`o2_menu_tracking`:                                   struct{}{},
		`o2searchlog`:                                        struct{}{},
		`ofs_payload_validation_events`:                      struct{}{},
		`one_support_events`:                                 struct{}{},
		`order_address_updates`:                              struct{}{},
		`order_distances_tracking`:                           struct{}{},
		`order_history_events`:                               struct{}{},
		`order_rush_events`:                                  struct{}{},
		`os_assignment_metrics`:                              struct{}{},
		`osm_order_tracking`:                                 struct{}{},
		`osrm_bloom_model_info`:                              struct{}{},
		`pageview`:                                           struct{}{},
		`pageview_tracking`:                                  struct{}{},
		`payments_enriched_events`:                           struct{}{},
		`payments_enriched_events_v2`:                        struct{}{},
		`payments_events_tracking`:                           struct{}{},
		`pdfgeneratorv2tracking`:                             struct{}{},
		`phone_verification_tracking`:                        struct{}{},
		`photo_shoot_tracking`:                               struct{}{},
		`ping_distance_tracking`:                             struct{}{},
		`ping_model_simulation`:                              struct{}{},
		`ping_tracking`:                                      struct{}{},
		`pinot_query_logs`:                                   struct{}{},
		`po_approval_events`:                                 struct{}{},
		`po_grn_mapping_events`:                              struct{}{},
		`poi_walkers`:                                        struct{}{},
		`pos_menu_request`:                                   struct{}{},
		`pos_vendor_tracking`:                                struct{}{},
		`pr_agent_events`:                                    struct{}{},
		`pr_coverage`:                                        struct{}{},
		`premium_checkout_tracking`:                          struct{}{},
		`preview_env_events`:                                 struct{}{},
		`privacy_shield_tracking`:                            struct{}{},
		`pro_membership_tracking`:                            struct{}{},
		`product_inventory_updates`:                          struct{}{},
		`product_warehouse_price_mapping_events`:             struct{}{},
		`promo_entities`:                                     struct{}{},
		`promo_events`:                                       struct{}{},
		`promo_journey_logs`:                                 struct{}{},
		`promo_priority_shift`:                               struct{}{},
		`promo_salt_updates`:                                 struct{}{},
		`promo_stress_updates`:                               struct{}{},
		`purchase_order_events`:                              struct{}{},
		`push_dispatched_events`:                             struct{}{},
		`push_service_events`:                                struct{}{},
		`rain_report_event`:                                  struct{}{},
		`rain_reporting_tickets`:                             struct{}{},
		`rat_logging`:                                        struct{}{},
		`rate_card_creation_events`:                          struct{}{},
		`rate_card_events`:                                   struct{}{},
		`raw_driver_events`:                                  struct{}{},
		`reamplify_tracking`:                                 struct{}{},
		`res_business_details`:                               struct{}{},
		`res_location_serviceability_events`:                 struct{}{},
		`res_onboarding_events`:                              struct{}{},
		`res_promo_updates`:                                  struct{}{},
		`res_serviceability_events`:                          struct{}{},
		`resrank`:                                            struct{}{},
		`restanalytics`:                                      struct{}{},
		`restaurant_category_ratings`:                        struct{}{},
		`restaurant_diy_events`:                              struct{}{},
		`restaurant_dynamic_tags`:                            struct{}{},
		`restaurant_popular_sections`:                        struct{}{},
		`restaurant_ratings`:                                 struct{}{},
		`restaurant_ratings_meta`:                            struct{}{},
		`restaurant_status_active_log`:                       struct{}{},
		`review_migration_events`:                            struct{}{},
		`review_search_events`:                               struct{}{},
		`review_tracking_events`:                             struct{}{},
		`reward_credit_events`:                               struct{}{},
		`reward_initialization_events`:                       struct{}{},
		`reward_service_status_updates`:                      struct{}{},
		`rewards_page_events`:                                struct{}{},
		`rider_app_packages`:                                 struct{}{},
		`rider_app_sanity_checks`:                            struct{}{},
		`rider_bgv_events`:                                   struct{}{},
		`rider_bonus_events`:                                 struct{}{},
		`rider_cancellation_fraud_events`:                    struct{}{},
		`rider_dispute_events`:                               struct{}{},
		`rider_fraud_events`:                                 struct{}{},
		`rider_karma_tracking`:                               struct{}{},
		`rider_lifecycle_events`:                             struct{}{},
		`rider_onboarding_web_tracking`:                      struct{}{},
		`rider_qc_events`:                                    struct{}{},
		`rider_trip_earnings`:                                struct{}{},
		`rider_trip_estimated_earnings`:                      struct{}{},
		`rider_weekly_payouts`:                               struct{}{},
		`route_distance_data`:                                struct{}{},
		`route_distance_records`:                             struct{}{},
		`rule_engine_evaluations`:                            struct{}{},
		`runnr_serviceability`:                               struct{}{},
		`s2_cell_locality_mapping`:                           struct{}{},
		`sa_merchant_events_tracking`:                        struct{}{},
		`sa_single_res_serviceability_events`:                struct{}{},
		`sa_slot_serviceability_tracking`:                    struct{}{},
		`scheduler_service_events`:                           struct{}{},
		`scheduling_slots`:                                   struct{}{},
		`search_approximate_serviceability`:                  struct{}{},
		`search_indexing_log`:                                struct{}{},
		`search_reranker_log`:                                struct{}{},
		`search_serviceability`:                              struct{}{},
		`search_serviceability_aggregator_logs`:              struct{}{},
		`search_serviceability_extension_log`:                struct{}{},
		`search_tracking`:                                    struct{}{},
		`search_tracking_test`:                               struct{}{},
		`searchlog`:                                          struct{}{},
		`searchlogaction`:                                    struct{}{},
		`select_and_go_events`:                               struct{}{},
		`send_tracking`:                                      struct{}{},
		`sendgrid_tracking`:                                  struct{}{},
		`serviceability_aggregator_events`:                   struct{}{},
		`serviceability_locality_stats`:                      struct{}{},
		`serviceability_reminder_subscription_events`:        struct{}{},
		`serviceability_reminder_tracking`:                   struct{}{},
		`serviceability_stress_levels`:                       struct{}{},
		`serviceability_stress_rules`:                        struct{}{},
		`ses_tracking`:                                       struct{}{},
		`shadow_rider_trip_earnings`:                         struct{}{},
		`shadow_serviceability_manager_response`:             struct{}{},
		`share`:                                              struct{}{},
		`shared_links_tracking`:                              struct{}{},
		`ship_rocket_callbacks`:                              struct{}{},
		`simulation_run_events`:                              struct{}{},
		`sms_tracking`:                                       struct{}{},
		`snap_distance_tracking`:                             struct{}{},
		`social_ads_web_tracking`:                            struct{}{},
		`solr_o2_v3_data_validation`:                         struct{}{},
		`solrindexing_log`:                                   struct{}{},
		`spot_interruption_events`:                           struct{}{},
		`stitch_events`:                                      struct{}{},
		`stock_transfer_order_events`:                        struct{}{},
		`store_update_events`:                                struct{}{},
		`story_tracking_events`:                              struct{}{},
		`stress_model_data`:                                  struct{}{},
		`subscription_events`:                                struct{}{},
		`subscription_tracking_events`:                       struct{}{},
		`supply_sufficiency_metrics`:                         struct{}{},
		`sx_ticket_metrics`:                                  struct{}{},
		`tabbed_home_migration_events`:                       struct{}{},
		`tech_cost_tracking_events`:                          struct{}{},
		`telecom_call_tracking`:                              struct{}{},
		`telecom_ivr_tracking`:                               struct{}{},
		`telecom_sync_recording_failures`:                    struct{}{},
		`thirdparty_eta_events`:                              struct{}{},
		`time_serviceability_demand_supply_events`:           struct{}{},
		`tinyint_processed_butterfly_api_logs`:               struct{}{},
		`tinyint_processed_driver_pings_back`:                struct{}{},
		`tinyint_processed_logistics_serviceability_events`:  struct{}{},
		`tinyint_processed_restaurant_ratings`:               struct{}{},
		`tinyint_processed_restaurant_ratings_meta`:          struct{}{},
		`tmp_app_performance_metrics`:                        struct{}{},
		`track_service_vicinity_events`:                      struct{}{},
		`train_fulfillment_app_events`:                       struct{}{},
		`train_fulfillment_order_events`:                     struct{}{},
		`trip_pings_spoofing_data`:                           struct{}{},
		`trip_rain_intensity_response_data`:                  struct{}{},
		`trivia_events`:                                      struct{}{},
		`trufflehog_events`:                                  struct{}{},
		`ts_distance_overrides`:                              struct{}{},
		`ts_order_duration_tracking`:                         struct{}{},
		`ugc_flows`:                                          struct{}{},
		`unified_chat_events`:                                struct{}{},
		`unified_chat_messages`:                              struct{}{},
		`unified_external_ticket_events`:                     struct{}{},
		`unified_ticket_events`:                              struct{}{},
		`upi_sdk_tracking`:                                   struct{}{},
		`user_badge_events`:                                  struct{}{},
		`user_coordinates`:                                   struct{}{},
		`user_device_info`:                                   struct{}{},
		`user_kyc_transactions`:                              struct{}{},
		`user_menu_feedbacks`:                                struct{}{},
		`user_personalised_ratings`:                          struct{}{},
		`user_preferences_events`:                            struct{}{},
		`user_support_session_details`:                       struct{}{},
		`userlifecycle`:                                      struct{}{},
		`utm_tracking`:                                       struct{}{},
		`vendor_portal_events`:                               struct{}{},
		`video_message_events`:                               struct{}{},
		`voip_events`:                                        struct{}{},
		`walker_orders`:                                      struct{}{},
		`wallet_tracking`:                                    struct{}{},
		`watch_app_metrics`:                                  struct{}{},
		`weather_action_events`:                              struct{}{},
		`weather_aggregated_rain_status`:                     struct{}{},
		`weather_airport_events`:                             struct{}{},
		`weather_data_download_logs`:                         struct{}{},
		`weather_device_localities`:                          struct{}{},
		`weather_external_api_tracking`:                      struct{}{},
		`weather_prediction_status`:                          struct{}{},
		`weather_signal_events`:                              struct{}{},
		`weather_station_events`:                             struct{}{},
		`weather_status_events`:                              struct{}{},
		`weather_user_events`:                                struct{}{},
		`weather_web_tracking`:                               struct{}{},
		`web_request_metrics`:                                struct{}{},
		`webfrontend_tracking`:                               struct{}{},
		`webtrack`:                                           struct{}{},
		`webviews_tracking`:                                  struct{}{},
		`whatsapp_tracking`:                                  struct{}{},
		`winback_events`:                                     struct{}{},
		`xtreme_app_events`:                                  struct{}{},
		`xtreme_events`:                                      struct{}{},
		`xtreme_orders`:                                      struct{}{},
		`xtreme_web_tracking`:                                struct{}{},
		`zautosuggestion_events_log`:                         struct{}{},
		`zautosuggestion_log`:                                struct{}{},
		`zexperiment_buckets`:                                struct{}{},
		`zfe_company_employees`:                              struct{}{},
		`zfe_corporate_dashboard_tracking`:                   struct{}{},
		`zfe_credit_transaction_events`:                      struct{}{},
		`zfe_employee_events`:                                struct{}{},
		`zfe_events_logging`:                                 struct{}{},
		`zfe_limit_reset_events`:                             struct{}{},
		`zfe_limit_transaction_events`:                       struct{}{},
		`zfe_order_events`:                                   struct{}{},
		`zfe_program_cyclic_events`:                          struct{}{},
		`zfe_rpc_events`:                                     struct{}{},
		`zfe_tracking`:                                       struct{}{},
		`zinnovation_user_input_log`:                         struct{}{},
		`zlive_order_changelog`:                              struct{}{},
		`zlive_order_events`:                                 struct{}{},
		`zlive_user_interaction_events`:                      struct{}{},
		`zomato_live_web_tracking`:                           struct{}{},
		`zomato_money_tracking`:                              struct{}{},
		`zomato_online_ordering_agents`:                      struct{}{},
		`zomato_online_ordering_ticket_data`:                 struct{}{},
		`zomato_places`:                                      struct{}{},
		`zomato_polls_tracking`:                              struct{}{},
		`zsearch_events_log`:                                 struct{}{},
		`zsearch_log`:                                        struct{}{},
		`ztracking`:                                          struct{}{},
		`ztransactions_srt_events`:                           struct{}{},
		`ztrends_tracking`:                                   struct{}{},
		`zws_device_status_change_events`:                    struct{}{},
		`zws_signal_data_validation`:                         struct{}{},
		`app_user_feedback`:                                  struct{}{},
		`cinema_session_events`:                              struct{}{},
		`dining_web_tracking`:                                struct{}{},
		`district_api_requests_tracking`:                     struct{}{},
		`district_business_app_events`:                       struct{}{},
		`district_cashless_app_events`:                       struct{}{},
		`district_events_awb_ops_events`:                     struct{}{},
		`district_events_ticket_ops_events`:                  struct{}{},
		`district_order_events`:                              struct{}{},
		`district_reward_events`:                             struct{}{},
		`district_shopping_events`:                           struct{}{},
		`district_user_list_update_events`:                   struct{}{},
		`event_item_inventory_changelog_events`:              struct{}{},
		`event_publisher_analytics`:                          struct{}{},
		`event_signups_tracking_events`:                      struct{}{},
		`events_bussiness_tracking`:                          struct{}{},
		`events_cart_tracking`:                               struct{}{},
		`events_central_billing_system_transaction_tracking`: struct{}{},
		`events_feedback_tracking`:                           struct{}{},
		`events_inventory_updates`:                           struct{}{},
		`events_ticket`:                                      struct{}{},
		`frontend_menu_events`:                               struct{}{},
		`homepage_backend_tracking`:                          struct{}{},
		`homepage_tracking`:                                  struct{}{},
		`lists_app_tracking`:                                 struct{}{},
		`movie_show_cancellation_events`:                     struct{}{},
		`movies_feedback_tracking`:                           struct{}{},
		`order_transition`:                                   struct{}{},
		`search_discovery_chatbot_tracking`:                  struct{}{},
		`searchbar_tracking`:                                 struct{}{},
		`searchbar_tracking_backend`:                         struct{}{},
		`supply_consumer_logs`:                               struct{}{},
		`supply_sync_logs`:                                   struct{}{},
		`third_party_api_tracking_events`:                    struct{}{},
		`transaction_provider_logs`:                          struct{}{}}
)

func IsTableWhitelistedForBackendLegacyFlow(tenant string, tableKey string) bool {
	_, skipTenant := skipTenantsForLegacyFlowCheck[tenant]
	if skipTenant {
		return true
	}

	_, isWhitelisted := whiteListedBackendTables[tableKey]
	return isWhitelisted
}
