package helpers

import (
	"context"
	"strconv"
	"strings"

	"github.com/Zomato/flash-gateway/internal/models"
	"github.com/Zomato/flash-gateway/pkg/services/statsd"
	ztracer "github.com/Zomato/flash-gateway/pkg/tracer"
	log "github.com/Zomato/go/logger"
	tracer "github.com/Zomato/go/tracer"
	"github.com/tidwall/gjson"
	"go.uber.org/zap"
)

// ShouldHandleV2Format returns if table should emit old header format, Never change this
func ShouldHandleV2Format(key string) bool {
	switch key {
	case
		"jevent",
		"jadtracking",
		"raw_driver_events",
		"zsearch_events_log",
		"eta_tracking",
		"live_order_service_events",
		"subscription_tracking_events":
		return false
	}

	return true
}

func GetTableSpecificPayload(ctx context.Context, key string, eventName string, url string, header string, rootPayload string) (interface{}, int) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "Handlers/GetTableSpecificPayload", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}
	if key == "zsearch_events_log" {
		varMap := map[string]string{
			"var1":  "",
			"var2":  "",
			"var3":  "",
			"var4":  "",
			"var5":  "",
			"var6":  "",
			"ename": eventName,
		}

		topicProducers := TablesEventsMap[key].AllowedEvents[eventName]
		if len(topicProducers) > 0 {
			if !applyFilterOperations(topicProducers[0].Filters, rootPayload) {
				return nil, 0
			}

			applyTransformationOperations(topicProducers[0].Transformations, &varMap, rootPayload)
		}

		payload := models.JEventsPayload{
			Header:    buildHeader(ctx, header),
			Var1:      varMap["var1"],
			Var2:      varMap["var2"],
			Var3:      varMap["var3"],
			Var4:      varMap["var4"],
			Var5:      varMap["var5"],
			Var6:      varMap["var6"],
			Key:       key,
			EventName: varMap["ename"],
			Url:       url,
		}

		return payload, 0
	}

	if key == "jevent" {

		if !IsJeventEnameWhitelisted(eventName) {
			return nil, 0
		}

		payload := models.JEventsPayload{
			Header:    buildHeader(ctx, header),
			Var1:      gjson.Get(rootPayload, "value.var1").String(),
			Var2:      gjson.Get(rootPayload, "value.var2").String(),
			Var3:      gjson.Get(rootPayload, "value.var3").String(),
			Var4:      gjson.Get(rootPayload, "value.var4").String(),
			Var5:      gjson.Get(rootPayload, "value.var5").String(),
			Var6:      gjson.Get(rootPayload, "value.var6").String(),
			Key:       key,
			EventName: eventName,
			Url:       url,
		}

		if len(payload.Var1) > 0 && IsJSONHacky(payload.Var1) {
			payload.Var1 = ""
		}
		if len(payload.Var2) > 0 && IsJSONHacky(payload.Var2) {
			payload.Var2 = ""
		}
		if len(payload.Var3) > 0 && IsJSONHacky(payload.Var3) {
			payload.Var3 = ""
		}
		if len(payload.Var4) > 0 && IsJSONHacky(payload.Var4) {
			payload.Var4 = ""
		}
		if len(payload.Var5) > 0 && IsJSONHacky(payload.Var5) {
			payload.Var5 = ""
		}
		if len(payload.Var6) > 0 && IsJSONHacky(payload.Var6) {
			if eventName == "HomeLocationLoaded" {
				payload.Var6 = gjson.Get(payload.Var6, "var7").String()
			} else {
				payload.Var6 = ""
			}
		}

		return payload, 0
	}

	if key == "jadtracking" {
		campaignID := gjson.Get(rootPayload, "value.campaign_id").String()
		if !(len(campaignID) > 0) {
			return nil, 0
		}

		campaignIDInt, err := strconv.Atoi(campaignID)
		if err != nil {
			return nil, 1 // throw error if unable to parse campaign id
		}

		if !(campaignIDInt > 0) {
			return nil, 0
		}

		facetsResult := gjson.Get(rootPayload, "value.facets")
		var facets []string
		facetsResult.ForEach(func(_, value gjson.Result) bool {
			facets = append(facets, value.String())
			return true
		})

		return models.JAdTrackingPayload{
			Header:            buildHeader(ctx, header),
			BannerID:          gjson.Get(rootPayload, "value.banner_id").String(),
			Bzone:             gjson.Get(rootPayload, "value.bzone").String(),
			Action:            gjson.Get(rootPayload, "value.action").String(),
			CollectionID:      gjson.Get(rootPayload, "value.collection_id").String(),
			CategoryID:        gjson.Get(rootPayload, "value.category_id").String(),
			SlotID:            gjson.Get(rootPayload, "value.slot_id").String(),
			DisplayPage:       gjson.Get(rootPayload, "value.display_page").String(),
			AdPosition:        gjson.Get(rootPayload, "value.ad_position").String(),
			SliderPosition:    gjson.Get(rootPayload, "value.slider_position").String(),
			SliderSequence:    gjson.Get(rootPayload, "value.slider_sequence").String(),
			CreativeID:        gjson.Get(rootPayload, "value.creative_id").String(),
			EntityType:        gjson.Get(rootPayload, "value.entity_type").String(),
			EntityID:          gjson.Get(rootPayload, "value.entity_id").String(),
			LocationID:        gjson.Get(rootPayload, "value.location_id").String(),
			LocationType:      gjson.Get(rootPayload, "value.location_type").String(),
			Rating:            gjson.Get(rootPayload, "value.rating").String(),
			IsNewAd:           gjson.Get(rootPayload, "value.isNewAd").String(),
			BillingUnit:       gjson.Get(rootPayload, "value.billing_unit").String(),
			CampaignID:        campaignID,
			ExperimentGroupID: gjson.Get(rootPayload, "value.experiment_group_id").String(),
			Facets:            facets,
			Gamma:             *GetFloat64ValueFromJsonString(rootPayload, "value.gamma"),
			Key:               key,
			EncryptedPrice:    gjson.Get(rootPayload, "value.encrypted_price").String(),
			FlinkDisplayPage:  gjson.Get(rootPayload, "value.flink_display_page").String(),
		}, 0
	}

	if key == "raw_driver_events" {
		// .Map() returns map[string]gjson.Result
		propertiesResult := gjson.Get(rootPayload, "value.properties").Map()
		properties := make(map[string]string)

		for key, value := range propertiesResult {
			properties[key] = value.String()
		}

		return models.RawDriverEvents{
			Header:     buildHeader(ctx, header),
			DriverID:   gjson.Get(rootPayload, "value.driver_id").String(),
			EventType:  gjson.Get(rootPayload, "value.event_type").String(),
			AppVersion: gjson.Get(rootPayload, "value.app_version").String(),
			EventName:  gjson.Get(rootPayload, "value.event_name").String(),
			EventValue: gjson.Get(rootPayload, "value.event_value").String(),
			Properties: properties,
		}, 0
	}

	if key == "eta_tracking" {
		metadataResult := gjson.Get(rootPayload, "value.metadata").Map()
		return models.EtaTracking{
			Header:                buildHeader(ctx, header),
			Category:              gjson.Get(rootPayload, "value.category").String(),
			ResId:                 gjson.Get(rootPayload, "value.res_id").Int(),
			OrderId:               gjson.Get(rootPayload, "value.order_id").Int(),
			RawEta:                gjson.Get(rootPayload, "value.raw_eta").Float(),
			SmoothenEta:           gjson.Get(rootPayload, "value.smoothen_eta").Float(),
			Polyline:              gjson.Get(rootPayload, "value.polyline").String(),
			OrderStatus:           gjson.Get(rootPayload, "value.order_status").String(),
			UserLatitude:          gjson.Get(rootPayload, "value.user_latitude").Float(),
			UserLongitude:         gjson.Get(rootPayload, "value.user_longitude").Float(),
			RiderLatitude:         gjson.Get(rootPayload, "value.rider_latitude").Float(),
			RiderLongitude:        gjson.Get(rootPayload, "value.rider_longitude").Float(),
			ResLatitude:           gjson.Get(rootPayload, "value.res_latitude").Float(),
			ResLongitude:          gjson.Get(rootPayload, "value.res_longitude").Float(),
			PingTimestamp:         gjson.Get(rootPayload, "value.ping_timestamp").Int(),
			Rat:                   gjson.Get(rootPayload, "value.rat").Float(),
			Kpt:                   gjson.Get(rootPayload, "value.kpt").Float(),
			PickupEta:             gjson.Get(rootPayload, "value.pickup_eta").Float(),
			DropEta:               gjson.Get(rootPayload, "value.drop_eta").Float(),
			PickupDistance:        gjson.Get(rootPayload, "value.pickup_distance").Float(),
			DropDistance:          gjson.Get(rootPayload, "value.drop_distance").Float(),
			AcceptanceTime:        gjson.Get(rootPayload, "value.acceptance_time").Float(),
			Buffer:                gjson.Get(rootPayload, "value.buffer").Float(),
			KptHandoverTime:       gjson.Get(rootPayload, "value.kpt_handover_time").Float(),
			TimeToReach:           gjson.Get(rootPayload, "value.time_to_reach").Float(),
			MetaDataIsLogs:        metadataResult["is_logs"].String(),
			MetaDataEstablishment: metadataResult["establishment"].String(),
			MetaDataCallerService: metadataResult["caller_service"].String(),
			MetaDataKptBuffer:     metadataResult["kpt_buffer"].String(),
			MetaDataRdtBuffer:     metadataResult["rdt_buffer"].String(),
		}, 0
	}

	if key == "subscription_tracking_events" {
		return models.SubscriptionTrackingPayload{
			Header:      buildHeader(ctx, header),
			EventType:   gjson.Get(rootPayload, "value.event_type").String(),
			SourceType:  gjson.Get(rootPayload, "value.source_type").String(),
			PlanType:    gjson.Get(rootPayload, "value.plan_type").String(),
			VoucherType: gjson.Get(rootPayload, "value.voucher_type").String(),
			CampaignID:  gjson.Get(rootPayload, "value.campaign_id").String(),
			PlanID:      gjson.Get(rootPayload, "value.plan_id").String(),
			PlanGroupID: gjson.Get(rootPayload, "value.plan_group_id").String(),
			OrderID:     gjson.Get(rootPayload, "value.order_id").String(),
			PromoId:     gjson.Get(rootPayload, "value.promo_id").String(),
			Key:         key,
		}, 0
	}

	if key == "live_order_service_events" {
		return models.LiveOrderServiceEvents{
			Header:           buildHeader(ctx, header),
			Key:              key,
			EventName:        gjson.Get(rootPayload, "value.event_name").String(),
			OrderId:          gjson.Get(rootPayload, "value.order_id").Int(),
			CarouselBannerId: gjson.Get(rootPayload, "value.event_data.carousel_banner_id").String(),
			TrackingState:    gjson.Get(rootPayload, "value.event_data.tracking_state").String(),
			PlanID:           gjson.Get(rootPayload, "value.event_data.plan_id").String(),
			GoldData:         gjson.Get(rootPayload, "value.event_data.gold_data").String(),
		}, 0
	}

	return nil, 1
}

func GetV2FormatTableSpecificPayload(ctx context.Context, header string, rootPayload string, tenant string) interface{} {
	eventTime := gjson.Get(header, "timestamp").Int()
	if eventTime == 0 {
		eventTime = gjson.Get(header, "time").Int()
	}

	ipAddress := gjson.Get(header, "ip").String()
	if ipAddress == "" {
		ipAddress = gjson.Get(header, "ip_address").String()
	}
	namespace := gjson.Get(header, "namespace").String()
	if namespace == "" {
		namespace = strings.ToLower(tenant)
	}

	payload, ok := gjson.Parse(rootPayload).Value().(map[string]interface{})
	if !ok {
		return nil
	}

	locationInfo := models.LocationInfo{
		UserDefinedLatitude:  GetFloat64ValueFromJsonString(header, "location_info.user_defined_latitude"),
		UserDefinedLongitude: GetFloat64ValueFromJsonString(header, "location_info.user_defined_longitude"),
		CurrentLongitude:     GetFloat64ValueFromJsonString(header, "location_info.current_longitude"),
		CurrentLatitude:      GetFloat64ValueFromJsonString(header, "location_info.current_latitude"),
	}

	value := payload["value"].(map[string]interface{})
	value["key"] = payload["key"].(string)
	urlVal, ok := payload["url"]
	if ok {
		value["url"] = urlVal.(string)
	} else {
		value["url"] = ""
		statsd.RecordIncrMetricWithTags(ctx, "v2_format_table_specific_payload_url_empty", 1, map[string]string{"table": value["key"].(string)})
		log.Warn(ctx, "URL is null or empty for downstream processing", zap.Any("payload", payload))
	}

	value["source"] = gjson.Get(header, "source").String()
	value["device_id"] = gjson.Get(header, "device_id").String()
	value["session_id"] = gjson.Get(header, "session_id").String()
	value["user_id"] = gjson.Get(header, "user_id").String()
	value["user_agent"] = gjson.Get(header, "user_agent").String()
	value["ip_address"] = ipAddress
	value["location"] = gjson.Get(header, "location").Int()
	value["time"] = eventTime
	value["namespace"] = namespace
	value["location_info"] = locationInfo

	return value
}
