package helpers

import (
	"github.com/spf13/viper"
)

type FieldType string

const (
	Derived FieldType = "derived"
	Now     FieldType = "now"
	Cache   FieldType = "cache"
)

type EnricherField struct {
	EnrichedField string    `json:"enriched_field"`
	LookupKey     string    `json:"lookup_key"`
	LookupField   string    `json:"lookup_field"`
	BaseField     string    `json:"base_field"`
	FieldType     FieldType `json:"field_type"`
}

type EnrichTopicConf struct {
	InputTopic      string          `json:"input_topic"`
	OutputTopic     string          `json:"output_topic"`
	EventTimeColumn string          `json:"event_time_column"`
	EventTimeFormat string          `json:"event_time_format"`
	Enrichment      []EnricherField `json:"enrichment"`
}

var EnricherDB map[string]EnrichTopicConf

func SetupEnricher() error {
	EnricherDB = make(map[string]EnrichTopicConf)

	enricherDB := viper.Get("enrichments").([]interface{})
	enricherDBBytes, err := json.Marshal(enricherDB)
	if err != nil {
		return err
	}

	var enrichTopicConfs []EnrichTopicConf
	err = json.Unmarshal(enricherDBBytes, &enrichTopicConfs)
	if err != nil {
		return err
	}

	for _, enrichTopicConf := range enrichTopicConfs {
		EnricherDB[enrichTopicConf.InputTopic] = enrichTopicConf
	}

	return nil
}
