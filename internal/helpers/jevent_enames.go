package helpers

import (
	"strings"
)

var (
	whiteListedEnames map[string]struct{} = map[string]struct{}{
		`ZPLSpecialOfferBannerTapped`:                       struct{}{},
		`api_success`:                                       struct{}{},
		`location_search_latencies`:                         struct{}{},
		`cell_mov_logs`:                                     struct{}{},
		`rejection_education_screen_impression`:             struct{}{},
		`outlet-location-diy-submit-tap`:                    struct{}{},
		`EVENTS_DETAIL_PAGE_VIEWS`:                          struct{}{},
		`V2LoginScreenPresented`:                            struct{}{},
		`GOLD_DESKTOP_PLAN_TILE_CLICK`:                      struct{}{},
		`sample_menu_item_click`:                            struct{}{},
		`menu_booster_pack_request_tap`:                     struct{}{},
		`reorder_from_history_page`:                         struct{}{},
		`rejected_orders_see_more_tap`:                      struct{}{},
		`InstantAddonsDismissed`:                            struct{}{},
		`mx_dining_web_click_on_logout_from_instagram`:      struct{}{},
		`O2CartGoldSubscriptionServiceResponse`:             struct{}{},
		`ZomatoWebOTPEntered`:                               struct{}{},
		`PUDishRemoved`:                                     struct{}{},
		`city_id_mismatch`:                                  struct{}{},
		`menu-image-detected-as-stock`:                      struct{}{},
		`cart_api_soft_error`:                               struct{}{},
		`init_sdk_completed`:                                struct{}{},
		`aerobar_hidden`:                                    struct{}{},
		`O2CartSuperaddonDishImageTapped`:                   struct{}{},
		`O2CrystalDIYRiderChatButtonTapped`:                 struct{}{},
		`SDKCardAdded`:                                      struct{}{},
		`SDKEnterCVVSubmitTapped`:                           struct{}{},
		`ResDosChatHelpButtonImpression`:                    struct{}{},
		`bulk_order_screen_impression`:                      struct{}{},
		`onboarding_stepper_impression`:                     struct{}{},
		`print_kot_click`:                                   struct{}{},
		`api_manual_otp_login_failed`:                       struct{}{},
		`insights_kpt_widget_filter_select`:                 struct{}{},
		`dining_make_payment_pos`:                           struct{}{},
		`timeout_screen_acknowledgement`:                    struct{}{},
		`O2CartPromoClicked`:                                struct{}{},
		`page_rendered''`:                                   struct{}{},
		`O2AddAddressNickNameTapped`:                        struct{}{},
		`new_order_aerobar_tap`:                             struct{}{},
		`soft_app_updates_pos`:                              struct{}{},
		`taxes_tap`:                                         struct{}{},
		`restaurant_action_object_impression`:               struct{}{},
		`mx_bot_flow_default_option`:                        struct{}{},
		`backgrounds_download_all_click`:                    struct{}{},
		`ZomatoWebSignupPageTap'`:                           struct{}{},
		`O2GoldPageLoaderDismiss`:                           struct{}{},
		`LocationMapScreenNickNameSelected`:                 struct{}{},
		`deeplink_share`:                                    struct{}{},
		`gold_selling_snippet_seen`:                         struct{}{},
		`multi_nearby_addresses`:                            struct{}{},
		`tour_start_click`:                                  struct{}{},
		`print_bill_click`:                                  struct{}{},
		`pro_member_section_viewed`:                         struct{}{},
		`USER_BLOCKED_SCREEN_SHOWN`:                         struct{}{},
		`O2CrystalMapCurrentLocationTapped`:                 struct{}{},
		`order_history_card_tap`:                            struct{}{},
		`vendor_api_failure_12_233`:                         struct{}{},
		`LocationMapScreenGPSPromptDismiss`:                 struct{}{},
		`GoldMembershipTellFriendsTap`:                      struct{}{},
		`ZPLMatchScreenPopUpTapped`:                         struct{}{},
		`PackagesDateChanged`:                               struct{}{},
		`ZomatoWebSignupSuccess`:                            struct{}{},
		`GoldAppIconChangePositiveTap`:                      struct{}{},
		`edit_contact_details_otp_submit`:                   struct{}{},
		`interaction-type-with-map`:                         struct{}{},
		`new-order-fcm-event`:                               struct{}{},
		`menu_edit_category_save_click`:                     struct{}{},
		`select_res`:                                        struct{}{},
		`O2CrystalResRatingTapped`:                          struct{}{},
		`LocationPermissionCurrentLocationTapped`:           struct{}{},
		`CartBillingSectionExpanded`:                        struct{}{},
		`GoldMembershipHeaderImpression`:                    struct{}{},
		`PinningKeyFetchSuccess`:                            struct{}{},
		`StoryCollectionViewed`:                             struct{}{},
		`ad_campaign_budget_save`:                           struct{}{},
		`BAD_REQUEST`:                                       struct{}{},
		`gift_card_balance_page_claim_gift_card_tapped`:     struct{}{},
		`review_bar_post_success`:                           struct{}{},
		`online_ordering`:                                   struct{}{},
		`pdpa_page_tracking`:                                struct{}{},
		`logstore_web_vitals`:                               struct{}{},
		`PollingPageBackDeclined`:                           struct{}{},
		`ad_campaign_video_play_tap`:                        struct{}{},
		`ticket_not_helpful_sub_option_tap`:                 struct{}{},
		`SDKAddWalletSuccess`:                               struct{}{},
		`deeplink_zomaland`:                                 struct{}{},
		`ownership-transfer/send-contract/contract-pending`: struct{}{},
		`O2CrystalMembershipSnippetTapped`:                  struct{}{},
		`AppleLoginDataFetched`:                             struct{}{},
		`ResAwardsWinnerCardShareTapped`:                    struct{}{},
		`O2QuickMealsChangeLocationClicked`:                 struct{}{},
		`GenericWebviewCTA-4_anniversary_push`:              struct{}{},
		`AppInstall`:                                        struct{}{},
		`SearchLocationPreLoadedResultsTapped`:              struct{}{},
		`JumboEnameO2ExtrasSelection`:                       struct{}{},
		`PhotosServiceError`:                                struct{}{},
		`deeplink_share_address`:                            struct{}{},
		`res_page_view_all_reviews_click`:                   struct{}{},
		`home_page_loaded_failed`:                           struct{}{},
		`ci_more_link`:                                      struct{}{},
		`remove_pickup_lat_lng`:                             struct{}{},
		`O2DishAdded`:                                       struct{}{},
		`PackagesViewCartTapped`:                            struct{}{},
		`PinningKeyFetchFailed`:                             struct{}{},
		`api_manual_otp_login_intent`:                       struct{}{},
		`gold_special_cart_back`:                            struct{}{},
		`zfb_access`:                                        struct{}{},
		`zpay_scratch_banner_clicked`:                       struct{}{},
		`webview-dining-venue-picker-recommend-recommend-res-ids-api-failed`: struct{}{},
		`O2CrystalOrderSharingResDetailsImpression`:                          struct{}{},
		`O2DeliveryInstructionSnippetImpression`:                             struct{}{},
		`ResShareTapped`:                                                     struct{}{},
		`O2PaymentFailedPage`:                                                struct{}{},
		`socket_service_destroy`:                                             struct{}{},
		`trivia_low_internet_config_attempted`:                               struct{}{},
		`see_all_home_page_tapped`:                                           struct{}{},
		`JumboEnameO2HomePageSearchBoxClicked`:                               struct{}{},
		`SearchLocationCurrentLocationGPSStatus`:                             struct{}{},
		`SearchResultTapped`:                                                 struct{}{},
		`bottom_bar_close_click`:                                             struct{}{},
		`raise_ticket_success`:                                               struct{}{},
		`moderation_reject_log_button`:                                       struct{}{},
		`webview-dining-venue-picker-page-load`:                              struct{}{},
		`mx_dining_web_menu_get_menu_api_request_failure`:                    struct{}{},
		`O2CartPromoTapped`:                                                  struct{}{},
		`add_customization_tap`:                                              struct{}{},
		`crystal_map_initialisation_started`:                                 struct{}{},
		`O2OrderPlaceConfirmCancelClicked`:                                   struct{}{},
		`LoginConsentCancelButtonTap`:                                        struct{}{},
		`ratecard_creation`:                                                  struct{}{},
		`ResAwardsWinnerViewMenuTapped`:                                      struct{}{},
		`O2CrystalRunnrRatingTapped`:                                         struct{}{},
		`CRYSTAL_FALLBACK_SCHEDULE`:                                          struct{}{},
		`StoryCompleteLoadTime`:                                              struct{}{},
		`PackagesBookingsHistoryLoaded`:                                      struct{}{},
		`menu_tag_button`:                                                    struct{}{},
		`order_history_search_bar_tapped`:                                    struct{}{},
		`photoLoadTimes`:                                                     struct{}{},
		`web_nav_tab_active`:                                                 struct{}{},
		`reporting_outlet_breakdown_tap`:                                     struct{}{},
		`menu_score_banner_tap`:                                              struct{}{},
		`O2CrystalCarouselCXRXChatImpressionEvent`:                           struct{}{},
		`SignupLogin`:                                                        struct{}{},
		`O2TipPaymentSuccess`:                                                struct{}{},
		`Pro_Brand_Rail_Tile_Viewed`:                                         struct{}{},
		`cc-dismissed-clicked`:                                               struct{}{},
		`appsTracking`:                                                       struct{}{},
		`verify_new_phone_with_otp`:                                          struct{}{},
		`zwallet_addmoney_change_payment_method_tapped`:                      struct{}{},
		`res_page_single_review_follow_click`:                                struct{}{},
		`menu_add_timings_tap`:                                               struct{}{},
		`TRSlotCartPageBottomButtonClicked`:                                  struct{}{},
		`whatsapp_async_opt_in`:                                              struct{}{},
		`GeofenceSyncInitiated`:                                              struct{}{},
		`MERCHANT_RATE_CARD_DATA`:                                            struct{}{},
		`SDKPaymentMethodDetailsCVVEntered`:                                  struct{}{},
		`in_app_popup_positive_tap`:                                          struct{}{},
		`menu_add_sub_category_click`:                                        struct{}{},
		`mweb_amp_open_app_review_modal_resinfo_page_impression`:             struct{}{},
		`order_timeline_detail_tap`:                                          struct{}{},
		`gold_special_cart_capsule_focus_loss`:                               struct{}{},
		`multi-package-menu-upload-postpaid-payment-click`:                   struct{}{},
		`photos`:                                                     struct{}{},
		`OPEN_RES_PAGE`:                                              struct{}{},
		`O2CartDismissed`:                                            struct{}{},
		`DeleteReviewRequest`:                                        struct{}{},
		`EverydayHomeCookRailItemImpression`:                         struct{}{},
		`PackagesAdded`:                                              struct{}{},
		`cx-phone-fetch-log`:                                         struct{}{},
		`menu_bulk_addons_button`:                                    struct{}{},
		`menu_item_info_coverage_screen_impression`:                  struct{}{},
		`reassign_order_from_lifeline`:                               struct{}{},
		`instant_order_tile_rendered`:                                struct{}{},
		`contact_details_link_warn_dismiss_tap`:                      struct{}{},
		`SDKInitCallFailure`:                                         struct{}{},
		`IdlingConfigUpdateReceived`:                                 struct{}{},
		`AppWillResignV2`:                                            struct{}{},
		`SDKAddCardNickNameEntered`:                                  struct{}{},
		`O2SuggestedResClickFromConsumer`:                            struct{}{},
		`O2CrystalContactlessV3ImpressionEvent`:                      struct{}{},
		`print_button_clicked_inside_print_modal`:                    struct{}{},
		`verify_user_tap`:                                            struct{}{},
		`O2AddAddressOpened`:                                         struct{}{},
		`trivia_percentage_video_cached`:                             struct{}{},
		`ownership-transfer/get-verification-status`:                 struct{}{},
		`O2CrystalMerchantAckSnippetTap`:                             struct{}{},
		`O2MenuCustomAdditionalInfoTapped`:                           struct{}{},
		`diningRatingSnippetTapped`:                                  struct{}{},
		`deeplink_photo`:                                             struct{}{},
		`O2MenuItemImageClicked`:                                     struct{}{},
		`markdown_style_converting_error`:                            struct{}{},
		`digital_contract_bank_details_open_success`:                 struct{}{},
		`ScratchCardCancelledSnippetImpression`:                      struct{}{},
		`menu_sub_category_toggle_off_tap`:                           struct{}{},
		`TRSlotConfirmationPageCancelBookingTapped`:                  struct{}{},
		`dominos_token_update`:                                       struct{}{},
		`ResRatingSelected`:                                          struct{}{},
		`SDKAddMoneyInWalletAmountEntered`:                           struct{}{},
		`SDKPaymentsCallInitiated`:                                   struct{}{},
		`ScratchCardCancelledSnippetTapped`:                          struct{}{},
		`GenericWebviewCTA-zpl_notavailable_20220331`:                struct{}{},
		`AddressSharingSnippetImpression`:                            struct{}{},
		`O2CrystalUserAlternateContactSnippetTappedV16`:              struct{}{},
		`ResOtherOutletsNearbyImpression`:                            struct{}{},
		`aerobar_shown`:                                              struct{}{},
		`support-ticket-notifiation-tap`:                             struct{}{},
		`opened_from_soa_mailer`:                                     struct{}{},
		`mx_dining_web_initiate_transformation_api_request_failed`:   struct{}{},
		`ViewAllBuffetPricingDetailsClicked`:                         struct{}{},
		`TranslationRequest`:                                         struct{}{},
		`O2OrderSummaryDownloadReceiptButtonTapped`:                  struct{}{},
		`PackagesBooked`:                                             struct{}{},
		`inApp_manual_update_failure`:                                struct{}{},
		`O2DropPinOpened`:                                            struct{}{},
		`add-dining-mx-res-contact`:                                  struct{}{},
		`mx_dining_web_events_auto_fill_unsaved_event_data_modal`:    struct{}{},
		`react_18_error-0`:                                           struct{}{},
		`custom-redirect-sreekanth`:                                  struct{}{},
		`SDKAddCardCallSuccess`:                                      struct{}{},
		`MenuSectionPayButtonClicked`:                                struct{}{},
		`PromoPageBackButtonTapped`:                                  struct{}{},
		`O2_HOME_REQUEST`:                                            struct{}{},
		`filter_clear_tap`:                                           struct{}{},
		`ready_tab_tap`:                                              struct{}{},
		`mark_been_there`:                                            struct{}{},
		`SDKPollingStarted`:                                          struct{}{},
		`SSIDLocationSet`:                                            struct{}{},
		`karma_dv_sdk_init`:                                          struct{}{},
		`post_order_screen_open`:                                     struct{}{},
		`ThirdPartyAPIs\KnowYourGst\KnowYourGstApiCaller_error`:      struct{}{},
		`growth_approval_action_success`:                             struct{}{},
		`gold_special_transaction_page_load_success`:                 struct{}{},
		`O2CrystalDownloadInvoiceImpression`:                         struct{}{},
		`O2CrystalOrderSharingUserSnippetImpressionEvent`:            struct{}{},
		`AnimatedSplashImpression`:                                   struct{}{},
		`PackageAddedtoCart`:                                         struct{}{},
		`ZPLGameDismissTapped`:                                       struct{}{},
		`complaint_notification_resolve_clicked`:                     struct{}{},
		`digital_contract_merchant_details_doc_added`:                struct{}{},
		`vendor_api_failure_12_39`:                                   struct{}{},
		`socket_new_order`:                                           struct{}{},
		`order_history_refund`:                                       struct{}{},
		`deeplink_goe`:                                               struct{}{},
		`USER_FLAG_HOISTED_SUCCESS`:                                  struct{}{},
		`O2CrystalRiderRatingBottomsheetImpression`:                  struct{}{},
		`ResClosingDialogImpression`:                                 struct{}{},
		`ZPLWinnerScreenOverlayAnimationTap`:                         struct{}{},
		`AddGeofenceSuccess`:                                         struct{}{},
		`O2SelectLocationOpened`:                                     struct{}{},
		`ownership-transfer/accepted-by-govid/salts-success`:         struct{}{},
		`O2CrystalHeaderSwitcherItemTapEvent`:                        struct{}{},
		`convince_screen_impression`:                                 struct{}{},
		`growth_ad_campaign_tap`:                                     struct{}{},
		`bottom_sheet_share_action_tapped`:                           struct{}{},
		`onboarding_placeholder_logout_tap`:                          struct{}{},
		`grow_your_business_popup_dismiss`:                           struct{}{},
		`rejection_v2_close_tap`:                                     struct{}{},
		`O2CartChangePaymentMethodTapped`:                            struct{}{},
		`SDKSavedPaymentMethodsSaveMethodTapped`:                     struct{}{},
		`O2MenuCustomizationMediaImpression`:                         struct{}{},
		`O2MenuMoreFiltersClosed`:                                    struct{}{},
		`O2MenuMoreFiltersImpression`:                                struct{}{},
		`O2MenuClearAllFiltersImpression`:                            struct{}{},
		`GenericWebviewCTA-legends_express_calendar_delhi_nv`:        struct{}{},
		`cover-picture-change-tap`:                                   struct{}{},
		`zero_surge_and_non_zero_mov_logs`:                           struct{}{},
		`zpay_scratch_card_impression`:                               struct{}{},
		`LanguageSelected`:                                           struct{}{},
		`language_settings_dropdown_apply_clicked`:                   struct{}{},
		`self_new_edit_tap`:                                          struct{}{},
		`search_location_fetched_from_cookie'`:                       struct{}{},
		`GoldPlanPageFAQClick`:                                       struct{}{},
		`auth_sdk_refresh_call_success`:                              struct{}{},
		`pay_cart_edit_button_tapped`:                                struct{}{},
		`JumboEnamePUFabClicked`:                                     struct{}{},
		`gift_card_order_history_view_details_tapped`:                struct{}{},
		`ownership-transfer/send-contract/contract-already-accepted`: struct{}{},
		`GenericWebviewCTA-ZPL_Appnotlive`:                           struct{}{},
		`GOLD_DEFAULT_MILESTONE_REACHED`:                             struct{}{},
		`TRSlotCrystalPageCarnivalConditionTapped`:                   struct{}{},
		`cart_tooltip_impression`:                                    struct{}{},
		`menu_page_load`:                                             struct{}{},
		`JumboEnamePUFabItemClicked`:                                 struct{}{},
		`mx_reporting_fetch_tab_data`:                                struct{}{},
		`preview_camera_tap`:                                         struct{}{},
		`mweb_native_promo_blocker_modal_impression`:                 struct{}{},
		`FCMServiceDestroyed`:                                        struct{}{},
		`JumboEnameO2PromoCodeApplied`:                               struct{}{},
		`EDIT_EMAIL_FLOW_INITIATE_EMAIL_CHANGE`:                      struct{}{},
		`kpt_delay_cancel`:                                           struct{}{},
		`gold_special_cart_zcredit_removed`:                          struct{}{},
		`O2CrystalLateServiceabilityImpression`:                      struct{}{},
		`ManagePaymentOptionsTapped`:                                 struct{}{},
		`GenericWebviewCTA-vip_mode_webview`:                         struct{}{},
		`O2MenuFabTapped`:                                            struct{}{},
		`O2MenuMoreFiltersTap`:                                       struct{}{},
		`O2MenuClearAllFiltersTap`:                                   struct{}{},
		`O2MenuMoreFiltersItemTap`:                                   struct{}{},
		`O2FreebieOfferUnlocked`:                                     struct{}{},
		`install`:                                                    struct{}{},
		`insights_tap`:                                               struct{}{},
		`instant_order_header_modal_click`:                           struct{}{},
		`O2CrystalHeaderSwitcherTapEvent`:                            struct{}{},
		`LocationPermissionGrantedEvent`:                             struct{}{},
		`CartConfirmationViewImpression`:                             struct{}{},
		`ad_alert_configured`:                                        struct{}{},
		`upload_document_click`:                                      struct{}{},
		`GeofenceSyncFailed`:                                         struct{}{},
		`O2GoldPageLoaderLostSavingsDismiss`:                         struct{}{},
		`ResCardBookmarkAction`:                                      struct{}{},
		`RiderPackagesTrack`:                                         struct{}{},
		`app_entered_foreground`:                                     struct{}{},
		`deeplink_redwebview`:                                        struct{}{},
		`menu_upload_item_image`:                                     struct{}{},
		`mx_web_banner_impression`:                                   struct{}{},
		`O2RestoredAppCachedData`:                                    struct{}{},
		`voice-instructions-deleted`:                                 struct{}{},
		`GenericWebviewCTA-GiftCardOldApp`:                           struct{}{},
		`LocationOnAddAddressEvent`:                                  struct{}{},
		`GamificationViewNotConfigured`:                              struct{}{},
		`SDKNativeOTPLoaded`:                                         struct{}{},
		`O2HomeLocationChanged`:                                      struct{}{},
		`ProCartPaymentRetryTapped`:                                  struct{}{},
		`user-id-not-found`:                                          struct{}{},
		`GenericWebviewCTA-zomato_pro`:                               struct{}{},
		`event_auto_sync_firebase`:                                   struct{}{},
		`LocationGeocodePromptShown`:                                 struct{}{},
		`add_photo_placeholder_impression`:                           struct{}{},
		`late_order_count`:                                           struct{}{},
		`webview-event-details-click-on-get-directions-btn`:          struct{}{},
		`ad_payment_fail`:                                            struct{}{},
		`mx_dining_web_menu_rearrange_api_request_success`:           struct{}{},
		`invalid-user-address`:                                       struct{}{},
		`ownership-transfer/send-contract/empty-merchant-id`:         struct{}{},
		`FailedInitialiseRewardWorkerJob`:                            struct{}{},
		`dynamic_mov_logs`:                                           struct{}{},
		`api_resend_email`:                                           struct{}{},
		`new_order_aerobar_rendered`:                                 struct{}{},
		`review_like_view_all`:                                       struct{}{},
		`mqtt_connect_success`:                                       struct{}{},
		`deeplink_c-14`:                                              struct{}{},
		`webview-getJumboInitConfig-api-failed`:                      struct{}{},
		`O2HomeLocationButtonClicked`:                                struct{}{},
		`LocationPermissionGeoCodeAPIFetched`:                        struct{}{},
		`SDKRetryPaymentScreenFailureMessage`:                        struct{}{},
		`GenericWebviewCTA-tst_aprilfools_20220329`:                  struct{}{},
		`AnimatedRollingSnippetShown`:                                struct{}{},
		`O2AddMoneyInWalletProceedTapped`:                            struct{}{},
		`decrease_volume_clicked`:                                    struct{}{},
		`reset_tap`:                                                  struct{}{},
		`go_online_tap`:                                              struct{}{},
		`locationpermissionautodetect`:                               struct{}{},
		`submit_mx_native_feedback`:                                  struct{}{},
		`AUTH_SDK_LOGOUT`:                                            struct{}{},
		`SDKSavedPaymentMethodsDeletePaymentMethod`:                  struct{}{},
		`edit_contact_details_click`:                                 struct{}{},
		`timeslots_select`:                                           struct{}{},
		`AnimationMetrics`:                                           struct{}{},
		`gold_special_cart_pay_change_pay_method`:                    struct{}{},
		`menu_meat_type_tag_un_selected_tap`:                         struct{}{},
		`TakeawayToggleTapped`:                                       struct{}{},
		`3pl-probability-updated`:                                    struct{}{},
		`ZomatoWebSignupPageLoaded`:                                  struct{}{},
		`Enter_Gst_Manually_Click`:                                   struct{}{},
		`o2_search_v14_stats`:                                        struct{}{},
		`le-transfer/profile-res-impresssion`:                        struct{}{},
		`call_customer_secondary_tap`:                                struct{}{},
		`learn_more_tour_strip_impression`:                           struct{}{},
		`growth_promo_tap`:                                           struct{}{},
		`DeviceLimitingBottomSheetButtonTap`:                         struct{}{},
		`GoldMembershipFAQTap`:                                       struct{}{},
		`ci_give_refund`:                                             struct{}{},
		`res_page_bottom_tab_click`:                                  struct{}{},
		`support_carousal_action_tap`:                                struct{}{},
		`ZomatoWebMultiAccountLoaded`:                                struct{}{},
		`continue_without_email_popup_tapped`:                        struct{}{},
		`get_pro_details`:                                            struct{}{},
		`media_player_error`:                                         struct{}{},
		`SignInFailedForGoogle`:                                      struct{}{},
		`ad_campaign_video_interested_tap`:                           struct{}{},
		`partner_signup`:                                             struct{}{},
		`aerobar_impression`:                                         struct{}{},
		`autosuggest_landmark_search`:                                struct{}{},
		`download_invoices_tap`:                                      struct{}{},
		`O2SaveNewAddress`:                                           struct{}{},
		`FeedbackMigrationBetweenExperiences`:                        struct{}{},
		`O2CartInstructionHMOImpressionEvent`:                        struct{}{},
		`GoldWelcomeConfettiImpression`:                              struct{}{},
		`ThirdPartyAPIs\KnowYourGst\KnowYourGstApiCaller_called`:     struct{}{},
		`trivia_low_internet_config_shown`:                           struct{}{},
		`share`:                                                      struct{}{},
		`ownership-transfer/user-not-eligible`:                       struct{}{},
		`voice-instructions-saved`:                                   struct{}{},
		`USER_CHANGED_LANGUAGE`:                                      struct{}{},
		`crystal_on_screen_time`:                                     struct{}{},
		`deeplink_trivia_lobby`:                                      struct{}{},
		`menu_search_tap`:                                            struct{}{},
		`remote-config-state-data`:                                   struct{}{},
		`assign_rider_screen_impression`:                             struct{}{},
		`learning_centre_search_video`:                               struct{}{},
		`ownership-transfer/accepted-by-govid`:                       struct{}{},
		`everyday_food_inventry_alert_received`:                      struct{}{},
		`SideMenuPackageBookings`:                                    struct{}{},
		`feedback_complaints_page_impression`:                        struct{}{},
		`webview-event-details-home-page-open`:                       struct{}{},
		`recommended_toggle_off_tap`:                                 struct{}{},
		`O2CrystalMapMarkerViewTap`:                                  struct{}{},
		`kpt_delay_time_selection_tap`:                               struct{}{},
		`reviews_notifications_toggle_tap`:                           struct{}{},
		`CRYSTAL_FALLBACK_RESPONSE`:                                  struct{}{},
		`JumboSDKEnameAddCardSubmitTapped`:                           struct{}{},
		`O2SearchResClicked`:                                         struct{}{},
		`Dev_clevertap_profile_force_update`:                         struct{}{},
		`EventPageCartTicketAdded`:                                   struct{}{},
		`ReferProceed`:                                               struct{}{},
		`ci_cancel_refund`:                                           struct{}{},
		`grow_your_business_row_tap`:                                 struct{}{},
		`soa_feedback_dismissed`:                                     struct{}{},
		`webview-event-details-get-events-home-pageLink-success`:     struct{}{},
		`app_visibility_changed`:                                     struct{}{},
		`ZPLLobbyTimerSnippetImpression`:                             struct{}{},
		`inventory_item_oos_toggle_pre_dialog_dismiss`:               struct{}{},
		`old-web-traffic-metrics`:                                    struct{}{},
		`O2AddItemMakeBlockerImpressionEvent`:                        struct{}{},
		`ndg_promo_generated`:                                        struct{}{},
		`gold-web-renew-plan-tile-clicked`:                           struct{}{},
		`O2CartBackButtonTapped`:                                     struct{}{},
		`O2MenuReviewTapped`:                                         struct{}{},
		`DiningMenuSearchTapped`:                                     struct{}{},
		`set-entity-info-parameters`:                                 struct{}{},
		`support_contact_selection_redirect_tap`:                     struct{}{},
		`all-pos-page-api-error`:                                     struct{}{},
		`A13_LOCALE_INITIALISED`:                                     struct{}{},
		`PhotoTappedPhotosPage`:                                      struct{}{},
		`CrawlerIPAddress`:                                           struct{}{},
		`deeplink_subscription`:                                      struct{}{},
		`res_view_rating_click`:                                      struct{}{},
		`tour_notification_impression`:                               struct{}{},
		`O2AddMoneyInWalletAmountEntered`:                            struct{}{},
		`mx_fssai_aerobar_click`:                                     struct{}{},
		`ad_campaign_billing_help_tap`:                               struct{}{},
		`ownership-transfer/transfer-to-self`:                        struct{}{},
		`SDKAddMoneyInWalletAmountTapped`:                            struct{}{},
		`res_delivery_offer_copy`:                                    struct{}{},
		`LocationReceiverNameDidEndTyping`:                           struct{}{},
		`GoldMembershipBenefitImpression`:                            struct{}{},
		`LocationActivityMetrics`:                                    struct{}{},
		`ProPlanPageLoadSuccess`:                                     struct{}{},
		`PackagesAddedtoCart`:                                        struct{}{},
		`geocode_subzone_override`:                                   struct{}{},
		`fssai_download_tap_web`:                                     struct{}{},
		`past_order_details_impression`:                              struct{}{},
		`order_history`:                                              struct{}{},
		`menu_bulk_taxes_button`:                                     struct{}{},
		`country_selected`:                                           struct{}{},
		`O2CartFullLoaderHidden`:                                     struct{}{},
		`SDKPaymentOptionsRechargeWalletTapped`:                      struct{}{},
		`O2CrystalExplorerOfferImpressionEvent`:                      struct{}{},
		`jumbo_pushed_events_tracking`:                               struct{}{},
		`reivew_close_dispute_with_reply_tapped`:                     struct{}{},
		`O2AddItemMenuFailurePopupImpressionEvent`:                   struct{}{},
		`CartOOSDialogImpression`:                                    struct{}{},
		`date_range_impression`:                                      struct{}{},
		`onboarding_view_impression`:                                 struct{}{},
		`GoldAppIconChangeNegativeTap`:                               struct{}{},
		`hyperpure_signup_tap`:                                       struct{}{},
		`SDKPaymentOptionsRenameCardTapped`:                          struct{}{},
		`gold_savings_snippet_seen`:                                  struct{}{},
		`o3ZaW1Uv`:                                                   struct{}{},
		`res_delivery_offer_imp-0`:                                   struct{}{},
		`EDIT_EMAIL_FLOW_TWO_FA_RESEND`:                              struct{}{},
		`mweb_amp_open_app_order_modal_resinfo_page_continue_click`:  struct{}{},
		`logout`:                                 struct{}{},
		`mweb_dom_painted9760844`:                struct{}{},
		`PUMenuItemImpression`:                   struct{}{},
		`home_pinging_success`:                   struct{}{},
		`inventory_category_expand_collapse_tap`: struct{}{},
		`multi_address_detection`:                struct{}{},
		`mx_dining_web_help_center_get_call_api_request_success`: struct{}{},
		`sneak_peek_video_editorial_auto_play`:                   struct{}{},
		`ScratchCardAnimationTriggered`:                          struct{}{},
		`GeofenceKillInitiated`:                                  struct{}{},
		`api_contract_breach_error`:                              struct{}{},
		`profile_add_phone_number`:                               struct{}{},
		`O2PreSearchSuggestionClicked`:                           struct{}{},
		`custom-redirect-mubina`:                                 struct{}{},
		`O2CrystalRatingImpression`:                              struct{}{},
		`disconnected-socket-event`:                              struct{}{},
		`download_statements_confirm`:                            struct{}{},
		`extra_rider_v2_dismiss`:                                 struct{}{},
		`category_tap`:                                           struct{}{},
		`O2CartSustainabilityContributionAddButton`:              struct{}{},
		`O2CrystalCookingInstructionsAddedImpression`:            struct{}{},
		`O2CartGoldDiscountApiResponse`:                          struct{}{},
		`OTGScratchCardRewardValueEvent`:                         struct{}{},
		`StoryViewCompleted`:                                     struct{}{},
		`cc-feedback-clicked`:                                    struct{}{},
		`support_impression_from_error_page`:                     struct{}{},
		`O2CrystalResRatedImpression`:                            struct{}{},
		`POST_ORDER_UPDATE`:                                      struct{}{},
		`offer_config_activate_tap`:                              struct{}{},
		`show_capture_tips_page`:                                 struct{}{},
		`AUTH_SDK_LOGIN`:                                         struct{}{},
		`O2HomePageLoaded`:                                       struct{}{},
		`EDIT_EMAIL_FLOW_VERIFY_AUTH`:                            struct{}{},
		`dc_logs`:                                                struct{}{},
		`V14HomeItemTapped`:                                      struct{}{},
		`res_page_write_review_stars_click`:                      struct{}{},
		`LoginExperiment`:                                        struct{}{},
		`EverydayMenuMastheadImpression`:                         struct{}{},
		`SDKPaymentOptionsOpenTransactionLedger`:                 struct{}{},
		`notification_centre_closed`:                             struct{}{},
		`reporting_forward_flow_tap`:                             struct{}{},
		`GiftCardCartProfileUpdated`:                             struct{}{},
		`O2PaymentMethodDetailsSaveCardCheckboxTapped`:           struct{}{},
		`ResRatingTapped`:                                        struct{}{},
		`O2CrystalRiderSafetyBannerTap`:                          struct{}{},
		`Backend_JumboEnameO2PhoneVerificationStart`:             struct{}{},
		`ResOfferSectionTapped`:                                  struct{}{},
		`SDKPromoPageBottomSheetImpression`:                      struct{}{},
		`O2CrystalDIYSupportOrderHeaderImpression`:               struct{}{},
		`ListingsPopupDismissed`:                                 struct{}{},
		`O2CrystalRiderSafetySheetBottomButtonTap`:               struct{}{},
		`cx_complaint_creation`:                                  struct{}{},
		`language_settings_dropdown_impression`:                  struct{}{},
		`mx-search-order-history`:                                struct{}{},
		`o2PusherNotifDispatched`:                                struct{}{},
		`everyday_kot_print_attempted`:                           struct{}{},
		`self_delivery_switch_on_click`:                          struct{}{},
		`O2CrystalDeliveredSnippetImpression`:                    struct{}{},
		`O2CrystalGiftingBannerV16`:                              struct{}{},
		`O2CrystalNdgScratchCardScratchEvent`:                    struct{}{},
		`IosLiveActivityStateChangeFailureEvent`:                 struct{}{},
		`Zhealthy_item_added`:                                    struct{}{},
		`mx_new_order_modal_timeout_breached`:                    struct{}{},
		`mx_req_fail_meta`:                                       struct{}{},
		`PhotoSliderScrolled`:                                    struct{}{},
		`collection_res_bookmark`:                                struct{}{},
		`review_edit_submit`:                                     struct{}{},
		`order_not_assignable`:                                   struct{}{},
		`crystalSwitchButtonClicked`:                             struct{}{},
		`O2CrystalOFSEBannerTapped`:                              struct{}{},
		`FilterModalAction`:                                      struct{}{},
		`O2_HOME_RENDERED`:                                       struct{}{},
		`manage_notifications_impression`:                        struct{}{},
		`search_location_fetched_from_cookie`:                    struct{}{},
		`res_delivery_add_address`:                               struct{}{},
		`GenericWebviewUpdate-Awards_WebviewUpdate_0417`:         struct{}{},
		`O2CrystalConciergeGoToSupportSectionButtonTapEvent`:     struct{}{},
		`about_us`:                                                     struct{}{},
		`ReferralProgramDeeplink`:                                      struct{}{},
		`res_page_similar_restaurant_impression`:                       struct{}{},
		`O2CartTipSectionButtonTapEvent`:                               struct{}{},
		`BottomButtonClicked`:                                          struct{}{},
		`empty_surge_pb`:                                               struct{}{},
		`reject_order_confirm_click`:                                   struct{}{},
		`AppScreenshotTaken`:                                           struct{}{},
		`ThirdPartyAPIs\IDFY\IDFYApiCaller_called`:                     struct{}{},
		`refresh-call-success`:                                         struct{}{},
		`res_growth_action_card_tap`:                                   struct{}{},
		`ZpayQRDetectedByCamera`:                                       struct{}{},
		`claim_gift_card_code_claim_button_tapped`:                     struct{}{},
		`gold_special_cart_offers_tapped`:                              struct{}{},
		`mac_cancellation_acceptance_confirm`:                          struct{}{},
		`diy-onboard-payment-status`:                                   struct{}{},
		`digital_contract_merchant_details_open_fail`:                  struct{}{},
		`deeplink_unified-support`:                                     struct{}{},
		`menu_inline_add_category_button`:                              struct{}{},
		`inventory_item_oos_toggle_pre_dialog_positive_btn_tap`:        struct{}{},
		`ResAwardsCategorySwitcherTapped`:                              struct{}{},
		`support_customer_instruction_impression`:                      struct{}{},
		`submit_full_menu_new`:                                         struct{}{},
		`webview-pro-click-on-checkout-your-membership`:                struct{}{},
		`O2CrystalNDGOrderNotApplicableBannerTapEvent`:                 struct{}{},
		`ResPhotoAboutSectionTapped`:                                   struct{}{},
		`skip_overdraw_click`:                                          struct{}{},
		`support_res_offline_res_turn_on_success`:                      struct{}{},
		`google_success`:                                               struct{}{},
		`order_timeline_close_tap`:                                     struct{}{},
		`res_mov_time`:                                                 struct{}{},
		`ad_payment_success`:                                           struct{}{},
		`locationSelected`:                                             struct{}{},
		`res_extra_phone_update`:                                       struct{}{},
		`ownership_transfer_m2_details`:                                struct{}{},
		`O2CrystalPayLaterButtonTapped`:                                struct{}{},
		`AppLaunchGPSLocationFetched`:                                  struct{}{},
		`Gst_Entered_Is_Different_From_Fetched`:                        struct{}{},
		`cart_item_validation_error`:                                   struct{}{},
		`order_refund_screen_impression`:                               struct{}{},
		`obpf-postpaid-payment-status`:                                 struct{}{},
		`mx_dining_web_view_customer_details_from_transaction_details`: struct{}{},
		`forgot_phone_number_tap`:                                      struct{}{},
		`O2PromoCodeApplied`:                                           struct{}{},
		`LocationHomeGPSPromptDismiss`:                                 struct{}{},
		`Call_TableReservation`:                                        struct{}{},
		`ZPLInfoTileTapped`:                                            struct{}{},
		`analytics_fetch`:                                              struct{}{},
		`review_preloaded_image_deselected`:                            struct{}{},
		`promos_update_response`:                                       struct{}{},
		`fssai_back_button_click`:                                      struct{}{},
		`mx_survey_aerobar_click`:                                      struct{}{},
		`FilterRailImpression`:                                         struct{}{},
		`SDKRetryPaymentScreenLoaded`:                                  struct{}{},
		`O2PostOrderCartHeaderSnippetImpression`:                       struct{}{},
		`PaymentTokenError`:                                            struct{}{},
		`search-suggestion`:                                            struct{}{},
		`insights_view_customer_complaints_tap`:                        struct{}{},
		`qc_image_upload_by_editor`:                                    struct{}{},
		`O2CrystalBlinkitLockedScratchCardSnippetImpressionEvent`:      struct{}{},
		`DiningHistoryTransactionDetailsClicked`:                       struct{}{},
		`order_history_res_filter_apply`:                               struct{}{},
		`outlet-location-diy-impression`:                               struct{}{},
		`mx_dining_web_payout_get_invoices_api_request`:                struct{}{},
		`O2CrystalHeaderImpression`:                                    struct{}{},
		`O2ItemLevelInstructionAdded`:                                  struct{}{},
		`O2CartCookingInstructionsSuggestionsTapped`:                   struct{}{},
		`GenericWebviewCTA-no_delay_guarantee`:                         struct{}{},
		`GoldIntroSectionTap`:                                          struct{}{},
		`ResPhotoBoxTapped`:                                            struct{}{},
		`GenericWebviewCTA-legends_express_calendar_bangalore_veg`:     struct{}{},
		`O2CrystalInstructionPayOnlineTapped`:                          struct{}{},
		`menu_score_calculating_impression`:                            struct{}{},
		`get_app_form_appstore_link_click`:                             struct{}{},
		`home_page_zomato_gold_banner_impression`:                      struct{}{},
		`O2CrystalAwardsBannerTapEvent`:                                struct{}{},
		`JumboEnameO2PhoneVerficationEnd`:                              struct{}{},
		`O2OFSEGiftingMessageImpression`:                               struct{}{},
		`PackageMenuPageLoaded`:                                        struct{}{},
		`O2RefundCollapseTapped`:                                       struct{}{},
		`imagePushNotifReceivedOnApp`:                                  struct{}{},
		`menu_item_toggle_off_tap`:                                     struct{}{},
		`mweb_open_app_header_chip_click_action`:                       struct{}{},
		`menu_excel_upload_button`:                                     struct{}{},
		`O2CrystalFooterOrderCancelImpression`:                         struct{}{},
		`menu_z_gallery_filter_tap`:                                    struct{}{},
		`O2CrystalContactlessDeliveryNoImageImpression`:                struct{}{},
		`ENAME_FIREBASE_CONFIG_HELPER_FETCH`:                           struct{}{},
		`deeplink_awards2023`:                                          struct{}{},
		`inventory_category_toggle_tap`:                                struct{}{},
		`important_guidelines_click`:                                   struct{}{},
		`mx_nps_notification_dismiss`:                                  struct{}{},
		`LocationMapScreenLocationFieldWillStartTyping`:                struct{}{},
		`api_cal_fail`:                                                 struct{}{},
		`location_prompt_backend`:                                      struct{}{},
		`mac_decline_impression`:                                       struct{}{},
		`deeplink_order_cart`:                                          struct{}{},
		`GOLD_DESKTOP_BUY_BUTTON_CLICK`:                                struct{}{},
		`zwallet_onboarding_form_submit_error`:                         struct{}{},
		`O2SubscriptionCreationFailure`:                                struct{}{},
		`DiningMenuViewed`:                                             struct{}{},
		`home_activity_background`:                                     struct{}{},
		`review_like`:                                                  struct{}{},
		`O2SavePaymentMethodsPaymentImpressions`:                       struct{}{},
		`O2SustainabilitySnippetTapped`:                                struct{}{},
		`O2CrystalZomatoAnthemImpression`:                              struct{}{},
		`ThirdPartyAPIs\Gupshup\WhatsappApiCaller_called`:              struct{}{},
		`init_sdk_wait_started`:                                        struct{}{},
		`menu_item_image_upload_fail`:                                  struct{}{},
		`deeplink_collection`:                                          struct{}{},
		`delivery_subzone_logs_1`:                                      struct{}{},
		`page_rendered'`:                                               struct{}{},
		`O2CrystalBlinkitLockedScratchCardSnippetTapped`:               struct{}{},
		`ScratchCardScratched`:                                         struct{}{},
		`PUMenuItemImageTapped`:                                        struct{}{},
		`deeplink_chat`:                                                struct{}{},
		`contact_details_view_profile_photo_tap`:                       struct{}{},
		`res_schedule_deleted`:                                         struct{}{},
		`switch_to_orders_mode`:                                        struct{}{},
		`mx_dining_web_open_understanding_insights_modal`:              struct{}{},
		`tips_bottom_sheet_failure`:                                    struct{}{},
		`mx_dining_web_events_click_on_add_or_remove_ticket`:           struct{}{},
		`O2MenuResAwardsTapped`:                                        struct{}{},
		`deeplink_c-5`:                                                 struct{}{},
		`O2CartOrderForSomeoneTapped`:                                  struct{}{},
		`mx_nps_to_feedback`:                                           struct{}{},
		`ad_campaign_auto_renew_toggle`:                                struct{}{},
		`resPageMenusTab`:                                              struct{}{},
		`GoldCartSnippetConsumptionImpression`:                         struct{}{},
		`GoldMembershipTnCTap`:                                         struct{}{},
		`ORDER_ACCEPTANCE_DELAYED_NOTIFICATION`:                        struct{}{},
		`mx-dashboard-mqtt-conx`:                                       struct{}{},
		`mx_segment_add_entity`:                                        struct{}{},
		`charge_service_calculation_mismatch`:                          struct{}{},
		`ci_popup_close_tap`:                                           struct{}{},
		`zcredits_issued`:                                              struct{}{},
		`O2AddMoneyInWalletBackTapped`:                                 struct{}{},
		`O2CrystalCarouselCXRXChatButtonTappedEvent`:                   struct{}{},
		`SDKRedirectionFromUPIAppSuccess`:                              struct{}{},
		`O2TipCustomTapped`:                                            struct{}{},
		`ad_alert_imp`:                                                 struct{}{},
		`item_description_blur`:                                        struct{}{},
		`O2CrystalHelpCenterTapped`:                                    struct{}{},
		`session_expiry_screen_impression`:                             struct{}{},
		`HelpCVPTapEvent`:                                              struct{}{},
		`vendor_api_failure_12_131`:                                    struct{}{},
		`SharedAddressSaveTapped`:                                      struct{}{},
		`O2MenuHealthyTab`:                                             struct{}{},
		`HomeSubTabPreFetchFlow`:                                       struct{}{},
		`o2_digital_contract_success`:                                  struct{}{},
		`reviews`:                                                      struct{}{},
		`delivery_subzone_logs_2`:                                      struct{}{},
		`transactions_expense_detail_tap`:                              struct{}{},
		`PollingPageBackPressed`:                                       struct{}{},
		`charge_service_fee_mismatch`:                                  struct{}{},
		`vendor_api_failure_12_293`:                                    struct{}{},
		`O2CrystalCutleryFeedbackImpression`:                           struct{}{},
		`NOTIFICATION_RECIEVED_IN_SERVICE`:                             struct{}{},
		`GoldMembershipChangeAppIconImpression`:                        struct{}{},
		`se_ticket_tapped`:                                             struct{}{},
		`O2CrystalMapSustainabilityBannerTapEvent`:                     struct{}{},
		`location_diy_view_my_location_tap`:                            struct{}{},
		`O2CrystalHOZSnippetImpressionEvent`:                           struct{}{},
		`MakeOrderCallStarted`:                                         struct{}{},
		`SDKPromoPageAddPaymentMethodOpened`:                           struct{}{},
		`O2OFSEThankyouImpression`:                                     struct{}{},
		`EditionInviteCardInfoLoad`:                                    struct{}{},
		`variant_create_tap`:                                           struct{}{},
		`GoogleSigninSuccess''`:                                        struct{}{},
		`O2CrystalCarouselRiderCallStatusImpression`:                   struct{}{},
		`O2CrystalRiderCarouselFraudTapped`:                            struct{}{},
		`API_BELOW_33_LANGUAGE_TRACKING`:                               struct{}{},
		`GenericWebviewCTA-gold_old_nonmember_beyond41`:                struct{}{},
		`PagePerformanceTracking`:                                      struct{}{},
		`PackagesSuccessPageLoaded`:                                    struct{}{},
		`O2SavedPaymentMethodsSaveMethodTapped`:                        struct{}{},
		`confirm_order_ready_tap`:                                      struct{}{},
		`insights_kpt_widget_impression`:                               struct{}{},
		`aerobar_swipe`:                                                struct{}{},
		`aerobar_scroll`:                                               struct{}{},
		`LocationProviderInfo`:                                         struct{}{},
		`post_order_tertiary_cell_interacted`:                          struct{}{},
		`deeplink_link.zomato.com`:                                     struct{}{},
		`Dev_siri_suggestions_tap`:                                     struct{}{},
		`O2MenuCustomisationHorizontalScroll`:                          struct{}{},
		`GenericWebviewCTA-gold_old_app_member`:                        struct{}{},
		`dashboard-load-status`:                                        struct{}{},
		`mx_nps_submission`:                                            struct{}{},
		`phoneVerification`:                                            struct{}{},
		`Image Shown`:                                                  struct{}{},
		`TicketAdded`:                                                  struct{}{},
		`user_ad_tracking_disabled`:                                    struct{}{},
		`ConsumerPushNotification`:                                     struct{}{},
		`SDKPaymentMethodDetailsExpiryEntered`:                         struct{}{},
		`Invalid_otp_entered`:                                          struct{}{},
		`ResFeaturedInTapped`:                                          struct{}{},
		`TicketTransactionStatusFailure`:                               struct{}{},
		`o2_digital_contract_empty_user_id`:                            struct{}{},
		`request_additional_request_tap`:                               struct{}{},
		`search_page_back_tapped`:                                      struct{}{},
		`mx_dining_web_get_stories_insights_api_request_failed`:        struct{}{},
		`O2CartDPFreeDcNA`:                                             struct{}{},
		`GoogleSigninSuccess'`:                                         struct{}{},
		`learn_more_tour_strip_click`:                                  struct{}{},
		`O2CartCutleryCheckboxImpression`:                              struct{}{},
		`ResPhotoAboutSectionImpression`:                               struct{}{},
		`ServiceBarTapped`:                                             struct{}{},
		`ad_alert_clear`:                                               struct{}{},
		`logout_tap`:                                                   struct{}{},
		`menu_add_photo_upload_tap`:                                    struct{}{},
		`mx_rush_hour_mode_activation_clicked`:                         struct{}{},
		`merchant_document_verification`:                               struct{}{},
		`verifyCreateUserRemoveEmail`:                                  struct{}{},
		`zqr_app_read`:                                                 struct{}{},
		`JumboEnameInCollectionTapped`:                                 struct{}{},
		`PUCartFullLoaderShown`:                                        struct{}{},
		`O2CrystalCarouselPayLaterImpression`:                          struct{}{},
		`EmailSelected`:                                                struct{}{},
		`google_reverse_geocode_info`:                                  struct{}{},
		`mx-triggered-refresh`:                                         struct{}{},
		`zpay_scratch_card_screen_impression`:                          struct{}{},
		`O2Menu`:                                                       struct{}{},
		`cheque_download_tap`:                                          struct{}{},
		`O2CrystalRiderAddAudioInstructionTapped`:                      struct{}{},
		`image_qc_accept_disclaimer_button`:                            struct{}{},
		`calculate_menu_score_banner_tap`:                              struct{}{},
		`GenericWebviewCTA-Trivia_LiveNow_NotUpdated`:                  struct{}{},
		`O2CrystalRiderAssignmentSnippetImpression`:                    struct{}{},
		`O2MenuCustomAdditionalInfoImpression`:                         struct{}{},
		`REQUEST_FLOW_HP_APP`:                                          struct{}{},
		`cached_lat_lng_used`:                                          struct{}{},
		`order_placed`:                                                 struct{}{},
		`photo_upload_success`:                                         struct{}{},
		`language_settings_disclaimer_impression`:                      struct{}{},
		`mx_dining_web_events_click_on_save_next_btn_add_tickets`:      struct{}{},
		`call_redirects`:                                               struct{}{},
		`extra_rider_v2_auto_dismiss`:                                  struct{}{},
		`logout-for-old-sdk`:                                           struct{}{},
		`deeplink_c-1`:                                                 struct{}{},
		`reverse_billing_abort`:                                        struct{}{},
		`web_universal_search_api_error-1`:                             struct{}{},
		`custom-redirect-pritam`:                                       struct{}{},
		`digital_contract_merchant_details_open_error`:                 struct{}{},
		`LoginConsentPageFailed`:                                       struct{}{},
		`GetPaymentStatus`:                                             struct{}{},
		`ResReportNowTapped`:                                           struct{}{},
		`SearchLocationSearchResultsTapped`:                            struct{}{},
		`IntentAppResponse`:                                            struct{}{},
		`image-change-fail`:                                            struct{}{},
		`impression_ads_budget_continue_tap`:                           struct{}{},
		`mx_nps_aerobar_click`:                                         struct{}{},
		`mweb_open_app_modal_click_action`:                             struct{}{},
		`O2WebviewSurveySubmitSuccess`:                                 struct{}{},
		`logstore_table_header_filter_option_tap`:                      struct{}{},
		`deeplink_c-253`:                                               struct{}{},
		`FeedingIndiaPaymentMethodImpression`:                          struct{}{},
		`LoginPageLoginSuccess`:                                        struct{}{},
		`O2CartDuplicateOrderPopupReturnButtonTapped`:                  struct{}{},
		`LocationGeocodePromptTapped`:                                  struct{}{},
		`otp_bottomsheet_confirm_tap`:                                  struct{}{},
		`track_offers_action_button_tap`:                               struct{}{},
		`O2_HOME_PARSEDStarted`:                                        struct{}{},
		`DynamicSplashInstantiated`:                                    struct{}{},
		`EventCardImpression`:                                          struct{}{},
		`DeviceLimitingBottomSheetImpression`:                          struct{}{},
		`PUMenuCustomizationLoaded`:                                    struct{}{},
		`ResActionsTapped`:                                             struct{}{},
		`ZPLMatchScreenPopUpImpression`:                                struct{}{},
		`ZPLVideoSoundToggleButtonTapped`:                              struct{}{},
		`StorySwipeUp`:                                                 struct{}{},
		`location_refreshed_after_timeout`:                             struct{}{},
		`LoginCancel`:                                                  struct{}{},
		`edit_cover_pic_tap`:                                           struct{}{},
		`view_fssai_details_tap`:                                       struct{}{},
		`digital_contract_rejection`:                                   struct{}{},
		`home_page_refreshed`:                                          struct{}{},
		`mx_dining_web_click_on_stories_actions`:                       struct{}{},
		`O2AudioSnippetActionTapped`:                                   struct{}{},
		`O2MenuRailDishAdded`:                                          struct{}{},
		`auth_logout_success`:                                          struct{}{},
		`deeplink_gold_faq`:                                            struct{}{},
		`geocode_subzone_doesnt_override`:                              struct{}{},
		`item_portion_size_change`:                                     struct{}{},
		`customer_location_map_tap`:                                    struct{}{},
		`GenericWebviewCTA-business_profile_how_it_works`:              struct{}{},
		`mx-login-success-new`:                                         struct{}{},
		`skip_battery_click`:                                           struct{}{},
		`order_notif_disable_cancel`:                                   struct{}{},
		`ownership-transfer/payout-less-than-outstanding`:              struct{}{},
		`order_self_delivery_toggle_tap`:                               struct{}{},
		`PhoneNoRemovedClick`:                                          struct{}{},
		`SDKPaymentMethodWebViewCancelConfirmed`:                       struct{}{},
		`ivr_order_acceptance`:                                         struct{}{},
		`entity_uuid_generation`:                                       struct{}{},
		`hp_error_retry`:                                               struct{}{},
		`notification_contact_details_tap`:                             struct{}{},
		`statement_of_account`:                                         struct{}{},
		`web_modal_filters_applied`:                                    struct{}{},
		`partner_app_fcm_event_sent`:                                   struct{}{},
		`help_centre_video_click`:                                      struct{}{},
		`variant_name_choose_tap`:                                      struct{}{},
		`valentines-dining-2023-share`:                                 struct{}{},
		`GenericWebviewCTA-ROI_0419`:                                   struct{}{},
		`O2MenuSuperOfferCopied`:                                       struct{}{},
		`O2OrderSummaryDownloadInvoiceButtonTappedV2`:                  struct{}{},
		`af_content_view`:                                              struct{}{},
		`automatic-buddy-rider-assignment`:                             struct{}{},
		`hp_checkout_page_impression`:                                  struct{}{},
		`offer_config_select_date`:                                     struct{}{},
		`delete_variant_tap`:                                           struct{}{},
		`go_offline_tap`:                                               struct{}{},
		`SponsoredAdTapped`:                                            struct{}{},
		`order_notification_off`:                                       struct{}{},
		`socket_service_on_start_command`:                              struct{}{},
		`uNlKCx8j`:                                                     struct{}{},
		`LocationMapScreenSelectorFieldTapped`:                         struct{}{},
		`SDKPaymentOptionsRemoveCardTapped`:                            struct{}{},
		`fetched_from_api_cache`:                                       struct{}{},
		`gift_card_balance_page_transaction_tapped`:                    struct{}{},
		`af_add_to_cart`:                                               struct{}{},
		`diy-next-click-success`:                                       struct{}{},
		`past_orders_tap`:                                              struct{}{},
		`stress_anomaly_data`:                                          struct{}{},
		`image_qc_auto_assign_button`:                                  struct{}{},
		`restaurant_selection_warning_dialog_impression`:               struct{}{},
		`location_selected`:                                            struct{}{},
		`O2FavTapped`:                                                  struct{}{},
		`O2CrystalRBTInfo`:                                             struct{}{},
		`AppAPIFailure`:                                                struct{}{},
		`invalid_payment_info`:                                         struct{}{},
		`res_meta_settings_event`:                                      struct{}{},
		`mx_dining_web_events_click_on_change_event_details_modal_tab`: struct{}{},
		`ad_payment_mode_radio_button`:                                 struct{}{},
		`contact_details_delete_cancel`:                                struct{}{},
		`gstin_get_new_gst_details_success`:                            struct{}{},
		`invite_accepted`:                                              struct{}{},
		`bulk-merchant-rate-card-update_v2`:                            struct{}{},
		`menu_booster_pack_purchase_response`:                          struct{}{},
		`schedule_edit_and_confirm_tap`:                                struct{}{},
		`zomatoforwork_form_submit_success`:                            struct{}{},
		`pro_plus_capping`:                                             struct{}{},
		`deeplink_scan`:                                                struct{}{},
		`logstore_api_request_fail_meta`:                               struct{}{},
		`GenericWebviewCTA-ZPL_0329`:                                   struct{}{},
		`instant_late_aerobar_order_click`:                             struct{}{},
		`past_orders_date_tap`:                                         struct{}{},
		`deeplink_upi`:                                                 struct{}{},
		`AppLocationChanged`:                                           struct{}{},
		`get-app-form-radio-type-change`:                               struct{}{},
		`support_carousel_impression`:                                  struct{}{},
		`merchant_support_feedback`:                                    struct{}{},
		`accept_order_tap_web`:                                         struct{}{},
		`print_invoice_click`:                                          struct{}{},
		`instant_expand_collapse_click`:                                struct{}{},
		`notification_add_phone_number_click`:                          struct{}{},
		`PUMenuCustomizationDismissed`:                                 struct{}{},
		`ci_no_refund_reason_pop-up`:                                   struct{}{},
		`in_app_manual_update_tap`:                                     struct{}{},
		`show_multi_address_prompt`:                                    struct{}{},
		`get_deal_details_pos.php`:                                     struct{}{},
		`sneak_peak_detail_page_away`:                                  struct{}{},
		`GOOGLE_PLAY_IN_APP_UPDATE_FAILURE`:                            struct{}{},
		`ResBookmarkButtonTapped`:                                      struct{}{},
		`convince_screen_policy_impression`:                            struct{}{},
		`restaurant_status_active_log`:                                 struct{}{},
		`webview-event-details-click-on-via-share-btn`:                 struct{}{},
		`O2CrystalAPIFailure`:                                          struct{}{},
		`O2CrystalContactlessDeliveryPromptImpression`:                 struct{}{},
		`AppFeedbackChatTapped`:                                        struct{}{},
		`EMPTY_SEARCH_RESULT`:                                          struct{}{},
		`GoldCartPaySuccess`:                                           struct{}{},
		`appRatingSnippetSeen`:                                         struct{}{},
		`mx_reporting_button`:                                          struct{}{},
		`support_view_order_ratings_tap`:                               struct{}{},
		`phone_resend_otp_failure`:                                     struct{}{},
		`O2MenuDeliveryModeTapped`:                                     struct{}{},
		`ZPLScoreCardImpression`:                                       struct{}{},
		`hp_place_order_tap`:                                           struct{}{},
		`amp_app_pitch_bottom_bar_close_click`:                         struct{}{},
		`mweb_open_universal_search`:                                   struct{}{},
		`new_order_screen_pop_up`:                                      struct{}{},
		`notification_centre_notification_count`:                       struct{}{},
		`mx_dining_web_events_click_on_save_next_btn_upload_media`:     struct{}{},
		`SavedLocationEditTapped`:                                      struct{}{},
		`vendor_api_failure_12_143`:                                    struct{}{},
		`fraud_entity_data`:                                            struct{}{},
		`o2_rider_tracking_not_updating`:                               struct{}{},
		`O2DeliveryInstructionSaveButtonTapped`:                        struct{}{},
		`LocationPermissionPromptShownEvent`:                           struct{}{},
		`ResFeaturesClosed`:                                            struct{}{},
		`AppUpdateBroadcast`:                                           struct{}{},
		`O2MenuDropOff`:                                                struct{}{},
		`O2MenuPORTapped`:                                              struct{}{},
		`GenericWebviewCTA-ZPL_17`:                                     struct{}{},
		`O2ResClickedFromConsumer`:                                     struct{}{},
		`gold_special_cart_load_failed`:                                struct{}{},
		`photo_guideline_screen_impression`:                            struct{}{},
		`web_res_view_gallery_click`:                                   struct{}{},
		`view-order-rating-notif-tap`:                                  struct{}{},
		`zwallet_addmoney_proceed_tapped`:                              struct{}{},
		`track_offers_dialog_impression`:                               struct{}{},
		`mqtt_topic_subscribe_fail`:                                    struct{}{},
		`O2MenuItemVegState`:                                           struct{}{},
		`TRSlotCartPageCarnivalConditionTapped`:                        struct{}{},
		`web-party-event-click-on-explore-now-button`:                  struct{}{},
		`AddressBookCreateSession`:                                     struct{}{},
		`SDKNativeOtpAutoRead`:                                         struct{}{},
		`SDKUpiInstalledApps`:                                          struct{}{},
		`O2PostOrderCartSupportButtonTapped`:                           struct{}{},
		`O2CrystalBottomSheetDeliveryInstructionSnippetImpression`:     struct{}{},
		`VenueOpened`:                                                  struct{}{},
		`menu_item_edit_name_tap`:                                      struct{}{},
		`update_variant_property_name`:                                 struct{}{},
		`review_preloaded_image_selected`:                              struct{}{},
		`mx_dining_web_your_customers_customer_full_details_api_success_failure`: struct{}{},
		`one-support-chat-close`:                                struct{}{},
		`mx_dining_web_events_create_event_upload_media_failed`: struct{}{},
		`sr_event_v2`:                                                   struct{}{},
		`3pl_selection_probability`:                                     struct{}{},
		`O2BackendCartValidationStatus`:                                 struct{}{},
		`O2CartItemCustomised`:                                          struct{}{},
		`SDKUpiFailure`:                                                 struct{}{},
		`O2MenuSearchResultsImpression`:                                 struct{}{},
		`feedback_page_dismissed`:                                       struct{}{},
		`location_audio_delete_tap`:                                     struct{}{},
		`postback_mismatch`:                                             struct{}{},
		`res_online_toggle_tap`:                                         struct{}{},
		`rider_map_zoomed_changed`:                                      struct{}{},
		`digital_contract_CFB_mailer`:                                   struct{}{},
		`copy_time_slot_submit`:                                         struct{}{},
		`mx_dining_web_payout_get_current_cycle_api_failed`:             struct{}{},
		`mx_doc_verification_execution_time`:                            struct{}{},
		`GoldMilestoneReachedPopupButtonTap`:                            struct{}{},
		`O2_HOME_PARSED`:                                                struct{}{},
		`RIDER_FRAUD`:                                                   struct{}{},
		`EmailPopOpened`:                                                struct{}{},
		`consumer_info_valid_coordinates`:                               struct{}{},
		`deeplink_gifts`:                                                struct{}{},
		`mx_action_accept`:                                              struct{}{},
		`support_res_offline_page_impression`:                           struct{}{},
		`mx_dining_web_discard_story`:                                   struct{}{},
		`switch_outlet_tap`:                                             struct{}{},
		`earth-day-2023-open-app-cta-click`:                             struct{}{},
		`O2ChangeAddressMenuClickOldAddress`:                            struct{}{},
		`LOCATION_DETECTION_WITH_DATA`:                                  struct{}{},
		`personal_details_impression`:                                   struct{}{},
		`ResReviewDraftNilReviewObject`:                                 struct{}{},
		`ci_view_all_options_tap`:                                       struct{}{},
		`home_page_web_view_loaded`:                                     struct{}{},
		`deeplink_zeveryday`:                                            struct{}{},
		`ShareAddressTapped`:                                            struct{}{},
		`SharedAddressOpened`:                                           struct{}{},
		`IosLiveActivityTerminalStateFailureEvent`:                      struct{}{},
		`SubtabSelectedV2`:                                              struct{}{},
		`fssai_expiry_date_selected`:                                    struct{}{},
		`renew_token_flow_triggered`:                                    struct{}{},
		`mac_chat`:                                                      struct{}{},
		`O2DishRemoved`:                                                 struct{}{},
		`ResPhotoTabLoaded`:                                             struct{}{},
		`HomeTabImpression`:                                             struct{}{},
		`distance_dc_exp`:                                               struct{}{},
		`location_selected_before_refresh`:                              struct{}{},
		`penalty_waived`:                                                struct{}{},
		`ownership-transfer/accepted-by-govid/gst-success`:              struct{}{},
		`webview-event-details-get-events-home-pageLink-failure`:        struct{}{},
		`O2CrystalBlinkitScratchCardSnippetTapped`:                      struct{}{},
		`menu_banner_impression`:                                        struct{}{},
		`mx_nps_notification_click`:                                     struct{}{},
		`cc-refund-cancel-clicked`:                                      struct{}{},
		`Sample-AppLaunchMetrics`:                                       struct{}{},
		`LanguageTourTextInteracted`:                                    struct{}{},
		`share_feedback_button_clicked`:                                 struct{}{},
		`gstin_option_save_success`:                                     struct{}{},
		`vendor_api_failure_12_248`:                                     struct{}{},
		`SDKPaymentOptionsOpenWalletLinking`:                            struct{}{},
		`SDKVerifyNowTapped`:                                            struct{}{},
		`ZPLNotifyMeImpression`:                                         struct{}{},
		`cart_payments_data`:                                            struct{}{},
		`mweb_amp_open_app_order_modal_resinfo_page_outside_click`:      struct{}{},
		`V2TabsLoaded`:                                                  struct{}{},
		`PackagesBookingsListImpression`:                                struct{}{},
		`listingResultsFetchedAndLoaded`:                                struct{}{},
		`learning_centre_language_dropdown_tap`:                         struct{}{},
		`growth-package-fetch-restaurants`:                              struct{}{},
		`location-ignored`:                                              struct{}{},
		`mx_app_menu_edit_access`:                                       struct{}{},
		`ShouldEnablePinningFlagFetchSuccess`:                           struct{}{},
		`additional_rider_request_action`:                               struct{}{},
		`address-place-id`:                                              struct{}{},
		`O2SharingCrystalOFSEBannerImpression`:                          struct{}{},
		`inventory_view_tap`:                                            struct{}{},
		`new_order_notification_impression`:                             struct{}{},
		`image_reject_button`:                                           struct{}{},
		`mx_dining_web_continue_login_with_instagram_btn_clicked`:       struct{}{},
		`O2CartViewOffersImpression`:                                    struct{}{},
		`O2OfferUnlocked`:                                               struct{}{},
		`login_exit`:                                                    struct{}{},
		`aerobar_funnel`:                                                struct{}{},
		`choose_package_tnc_click`:                                      struct{}{},
		`order_timelines_impression`:                                    struct{}{},
		`rider_notifications_toggle_tap`:                                struct{}{},
		`deeplink_goto`:                                                 struct{}{},
		`campaign_list_card_tap`:                                        struct{}{},
		`dialog_cancel_tap`:                                             struct{}{},
		`native_otp_user_consent_denied`:                                struct{}{},
		`cx_number_sharing_fetch_contract`:                              struct{}{},
		`O2CrystalStarSportsCarouselImpression`:                         struct{}{},
		`menu_customization_complete_button`:                            struct{}{},
		`tabbed_home_cache`:                                             struct{}{},
		`zfbMobileWhatsappSubscribe`:                                    struct{}{},
		`3dtouch_videos`:                                                struct{}{},
		`O2CrystalHMOV3ImpressionEvent`:                                 struct{}{},
		`HomeLocationLoaded`:                                            struct{}{},
		`SSL_PINNING_TRACKER`:                                           struct{}{},
		`O2MenuCustomizationLoaded`:                                     struct{}{},
		`ForegroundNewPushReceived`:                                     struct{}{},
		`ad_alert_received`:                                             struct{}{},
		`cached_lat_lng_not_used`:                                       struct{}{},
		`aerobar_contact_details_impression`:                            struct{}{},
		`aerobar_edit_email_tap`:                                        struct{}{},
		`webview-dining-tr-restrictions-page-load`:                      struct{}{},
		`webview-event-details-click-on-view-restaurant-btn`:            struct{}{},
		`O2CrystalOrderSharingShareNowButtonTapped`:                     struct{}{},
		`SDKBankOptionsBankSelected`:                                    struct{}{},
		`api_manual_otp_login_success`:                                  struct{}{},
		`hp_cart_checkout_tap`:                                          struct{}{},
		`hp_product_listing_impression`:                                 struct{}{},
		`GenericWebviewCTA-Awards_WebviewUpdate_0417`:                   struct{}{},
		`review_search_bar_tag_selected`:                                struct{}{},
		`mx_dining_web_help_center_click_on_get_call_card`:              struct{}{},
		`O2RatingPageImpression`:                                        struct{}{},
		`O2CrystalRiderContactBottomSheetPrimaryCallButtonTapped`:       struct{}{},
		`ci_order_card_impression`:                                      struct{}{},
		`O2LocationSearchResultsShown`:                                  struct{}{},
		`TRSlotBookingSuccess`:                                          struct{}{},
		`mx_dining_web_events_update_contracts_api`:                     struct{}{},
		`cart_api_response`:                                             struct{}{},
		`track_offers_card_impression`:                                  struct{}{},
		`mx_dining_web_delete_story_cancel_clicked`:                     struct{}{},
		`ecofriendly_fetch_user_data`:                                   struct{}{},
		`LocationPermissionManualLocationTapped`:                        struct{}{},
		`O2MenuSearchTapped`:                                            struct{}{},
		`StoryMovedNext`:                                                struct{}{},
		`deeplink_resolve_link`:                                         struct{}{},
		`hp_cart_image_tap`:                                             struct{}{},
		`PUMenuFabItemClicked`:                                          struct{}{},
		`brand_page_res_tap`:                                            struct{}{},
		`O2CartCheckoutButtonTapped`:                                    struct{}{},
		`O2MenuSuperOfferViewed`:                                        struct{}{},
		`ad_campaign_details_help_tap`:                                  struct{}{},
		`support_item_click_web`:                                        struct{}{},
		`O2CrystalResFraudTapped`:                                       struct{}{},
		`O2CrystalLovedByDpTapped`:                                      struct{}{},
		`PackagesMenuCustomizationLoaded`:                               struct{}{},
		`SplashLocationSetEvent`:                                        struct{}{},
		`consumer_info_entity`:                                          struct{}{},
		`menu_click`:                                                    struct{}{},
		`zpay_payment_status`:                                           struct{}{},
		`le-transfer/contract-form-send-click`:                          struct{}{},
		`GenericWebviewCTA-Awards_11`:                                   struct{}{},
		`deeplink_history`:                                              struct{}{},
		`ZomatoWebLoginPageTap`:                                         struct{}{},
		`mx_dining_web_upload_photo_or_video_from_browser`:              struct{}{},
		`winback_signup_click`:                                          struct{}{},
		`1'`:                                                            struct{}{},
		`FeedingIndiaDonateAmountSelected`:                              struct{}{},
		`O2CrystalDIYHeaderImpression`:                                  struct{}{},
		`SDKAddCardOtherTapped`:                                         struct{}{},
		`gift_card_balance_page_impression`:                             struct{}{},
		`event_app_launch`:                                              struct{}{},
		`gccustom_click`:                                                struct{}{},
		`menu_sub_category_toggle_on_tap`:                               struct{}{},
		`schedule_confirm_tap`:                                          struct{}{},
		`init_sdk_completed_call_finished`:                              struct{}{},
		`O2CrystalResCutleryImpression`:                                 struct{}{},
		`O2CachedLocationUsed`:                                          struct{}{},
		`SDKPaymentMethodWebViewLoadSuccess`:                            struct{}{},
		`event_menu_add_on_view_all_tap`:                                struct{}{},
		`push notification received`:                                    struct{}{},
		`reject_order_confirm`:                                          struct{}{},
		`phone_send_otp_success`:                                        struct{}{},
		`menu_charges_dropdown`:                                         struct{}{},
		`LongDistanceWarningPopupImpression`:                            struct{}{},
		`O2RequestDeliveryEventImpression`:                              struct{}{},
		`phone_login_single_res_mapped`:                                 struct{}{},
		`web-party-event-click-on-download-app-button`:                  struct{}{},
		`phone_res_search_invalid_res`:                                  struct{}{},
		`PROMO_USER_APPLY_ERROR_LOG_FOR_SERVICE_CASHLESS`:               struct{}{},
		`O2HomeSearchResultsShown`:                                      struct{}{},
		`geocode_fetched_from_10m_cache`:                                struct{}{},
		`tapped_facebook`:                                               struct{}{},
		`crm_updates`:                                                   struct{}{},
		`webview-dining-venue-picker-recommend-page-load`:               struct{}{},
		`SDKVerifiedSuccessLoaded`:                                      struct{}{},
		`ReferralBannerImpression`:                                      struct{}{},
		`OrderSuccessAnimationShown`:                                    struct{}{},
		`genjs_js_list`:                                                 struct{}{},
		`custom_offers_data_preview_click_error`:                        struct{}{},
		`mx_rush_hour_mode_skip_clicked`:                                struct{}{},
		`PUMenuRepeatLastCustomizationLoaded`:                           struct{}{},
		`O2CrystalCarouselPayLaterTapEvent`:                             struct{}{},
		`O2CrystalGrofersAdBannerTapped`:                                struct{}{},
		`ci_confirm_no_refund`:                                          struct{}{},
		`rejection_education_screen_deeplink_tap`:                       struct{}{},
		`Restaurant.trackTabClicks`:                                     struct{}{},
		`Backend_OrderPlaced`:                                           struct{}{},
		`O2CrystalIntercityBannerTapped`:                                struct{}{},
		`Dev_swipe_to_dismiss_tried_on_autosuggestion_page`:             struct{}{},
		`ThirdPartyAPIs\NSDL\NSDLApiCaller_called`:                      struct{}{},
		`duration_radio_button_tap`:                                     struct{}{},
		`udl_x_address_logging`:                                         struct{}{},
		`otp_bottomsheet_sent`:                                          struct{}{},
		`image_qc_auto_assign_page_load`:                                struct{}{},
		`CitySelected`:                                                  struct{}{},
		`MicPermissionImpression`:                                       struct{}{},
		`O2AddItemMenuFailurePopupTapEvent`:                             struct{}{},
		`add_photo_manage_option_tapped`:                                struct{}{},
		`ButtonGenerateCardDetailsOtpTapEvent`:                          struct{}{},
		`location_autodetect_result`:                                    struct{}{},
		`menu_image_score_response`:                                     struct{}{},
		`get_image_scores_failed`:                                       struct{}{},
		`dining_tr_whatsapp_message_sent`:                               struct{}{},
		`mweb_amp_open_app_modal_v2_open_click`:                         struct{}{},
		`outlet-location-diy-raise-ticket`:                              struct{}{},
		`logout-new-error`:                                              struct{}{},
		`home_page_loaded`:                                              struct{}{},
		`JumboEnameViewMap`:                                             struct{}{},
		`O2MenuCollapsibleButtonTapped`:                                 struct{}{},
		`deeplink_webview`:                                              struct{}{},
		`mx_reporting_filter_button_tap`:                                struct{}{},
		`reporting_widget_info_tap`:                                     struct{}{},
		`button_raise_ticket_tapped`:                                    struct{}{},
		`cc_multiple_complaint_popup_opened`:                            struct{}{},
		`O2GoldPageLoaderViewed`:                                        struct{}{},
		`O2MenuCategoriesPresent`:                                       struct{}{},
		`ListingsDidFetchObject`:                                        struct{}{},
		`O2RefundSnippetImpression`:                                     struct{}{},
		`PromoPagePromoImpression`:                                      struct{}{},
		`zwallet_addmoney_click`:                                        struct{}{},
		`learning_centre_locale_tap`:                                    struct{}{},
		`FiltersApplied`:                                                struct{}{},
		`z_got_your_birthday_page_mount`:                                struct{}{},
		`O2CrystalOrderSharingResDetailsTapped`:                         struct{}{},
		`aerobar_contact_details_tap`:                                   struct{}{},
		`init_sdk_started`:                                              struct{}{},
		`notification_edit_phone_impression`:                            struct{}{},
		`reject_order_tap`:                                              struct{}{},
		`web_universal_location_modify_query`:                           struct{}{},
		`ResReviewsTapped`:                                              struct{}{},
		`explorer triggered`:                                            struct{}{},
		`welcome_code_of_conduct_tap`:                                   struct{}{},
		`pro_apply_extension_code_tapped`:                               struct{}{},
		`O2RRNCopyTapped`:                                               struct{}{},
		`fssai_upload_attachment_tap`:                                   struct{}{},
		`landing_page_dining_bills_benefit_impression`:                  struct{}{},
		`price_parity_price_change_request`:                             struct{}{},
		`tour_callback_strip_click`:                                     struct{}{},
		`zfb-banner`:                                                    struct{}{},
		`review_comment_clicked`:                                        struct{}{},
		`ReferralCouponUnusablePageTap`:                                 struct{}{},
		`mx_survey_impression`:                                          struct{}{},
		`@@0aVGe`:                                                       struct{}{},
		`res_page_single_review_user_image_click''`:                     struct{}{},
		`O2CrystalHelpAndSupportSnippetImpression`:                      struct{}{},
		`FAQ_SECTION_OPEN`:                                              struct{}{},
		`O2MenuItemsImpression`:                                         struct{}{},
		`deeplink_reorder`:                                              struct{}{},
		`high_ddt_blocker_sheet`:                                        struct{}{},
		`add_photo`:                                                     struct{}{},
		`ci_issue_strip_click`:                                          struct{}{},
		`hp_partner_show_login_success`:                                 struct{}{},
		`O2SelectExistingAddress`:                                       struct{}{},
		`3pl_partner_called`:                                            struct{}{},
		`TLS_VERSION`:                                                   struct{}{},
		`StoryShared`:                                                   struct{}{},
		`ListingsTrackPageLoadedV2`:                                     struct{}{},
		`insights_card_impression`:                                      struct{}{},
		`mx_dining_web_story_publish_successfully`:                      struct{}{},
		`SDKAddPaymentMethodsAddPaymentTapped`:                          struct{}{},
		`O2CrystalRefreshButtonTapped`:                                  struct{}{},
		`AppLaunchLocation`:                                             struct{}{},
		`custom_offers_screen_data_fetch_success`:                       struct{}{},
		`merchant_soa_data`:                                             struct{}{},
		`menu_item_delete_button`:                                       struct{}{},
		`STORAGE_FAILED`:                                                struct{}{},
		`order_cancellation_mail_failure`:                               struct{}{},
		`payments_token_after_logout`:                                   struct{}{},
		`aerobar_assets_impression`:                                     struct{}{},
		`SDKWalletLinkingConfirmOTP`:                                    struct{}{},
		`NUReferralCollectionErrorPage`:                                 struct{}{},
		`digital_contract_sign_doc_added`:                               struct{}{},
		`pickedup_tab_tap`:                                              struct{}{},
		`deeplink_order-history-detail`:                                 struct{}{},
		`tax_invoice_tap`:                                               struct{}{},
		`LoginLocationSetEvent`:                                         struct{}{},
		`SDKNativeOtpSubmitTapped`:                                      struct{}{},
		`TRANSITION_TO_API33`:                                           struct{}{},
		`bottom_bar_use_app_click`:                                      struct{}{},
		`deeplink_gold`:                                                 struct{}{},
		`menu_list_submit`:                                              struct{}{},
		`support_rider_call_tap`:                                        struct{}{},
		`dashboard-load-failed`:                                         struct{}{},
		`res_delivery_close_customisation_modal`:                        struct{}{},
		`GPSLocationFetchFailedWithInvalidLocations`:                    struct{}{},
		`business_report_tap`:                                           struct{}{},
		`menu_timing_info_tap`:                                          struct{}{},
		`ZomatoWebSignupPageTap`:                                        struct{}{},
		`dining-business-partner-app-contact-touch`:                     struct{}{},
		`dish_banner_impression`:                                        struct{}{},
		`O2SearchSuggestionResClicked`:                                  struct{}{},
		`mov_bypass_restaurants`:                                        struct{}{},
		`deeplink_c-59`:                                                 struct{}{},
		`O2MenuNDGTap`:                                                  struct{}{},
		`LocationMapPagePromptImpression`:                               struct{}{},
		`menu_item_edit_description_tap`:                                struct{}{},
		`contact_details_link_alert_confirm_tap`:                        struct{}{},
		`GOLD_DESKTOP_PLAN_PAGE_TC_CLICK`:                               struct{}{},
		`res_user_mappings_changes`:                                     struct{}{},
		`vendor_api_failure_12_169`:                                     struct{}{},
		`outlet_info_contact_details_search_input_close`:                struct{}{},
		`FeedingIndiaCartCheckoutButtonTapped`:                          struct{}{},
		`nps_pos_mailer`:                                                struct{}{},
		`O2CrystalNDGDeliveredSnippetClaimButtonTapped`:                 struct{}{},
		`O2CartCookingInstructionsTapped`:                               struct{}{},
		`LanguageButtonShimmerShown`:                                    struct{}{},
		`mweb_amp_open_app_order_modal_resinfo_page_open_click`:         struct{}{},
		`PaymentSelectionPopupActionClicked`:                            struct{}{},
		`hamburger_menu_closed_home_page`:                               struct{}{},
		`LOCATION_DETECTION_MANUAL`:                                     struct{}{},
		`feedback_page_open_success`:                                    struct{}{},
		`signup_link_email`:                                             struct{}{},
		`phone_auto_verify_request`:                                     struct{}{},
		`O2CrystalContactlessDominosImpression`:                         struct{}{},
		`O2MenuOutletSwitcherTapped`:                                    struct{}{},
		`Fetched_GST_clicked`:                                           struct{}{},
		`PUMenuSuperOfferViewed`:                                        struct{}{},
		`O2DropPinSaveAddress`:                                          struct{}{},
		`menu_item_copy_button`:                                         struct{}{},
		`media_repo_unmap_images`:                                       struct{}{},
		`O2CrystalViewOrderDetailsTapped`:                               struct{}{},
		`GamificationDidBecomeActiveNotification`:                       struct{}{},
		`DiningMenuCustomizationLoaded`:                                 struct{}{},
		`aws_weather_data`:                                              struct{}{},
		`soa_filter_tap`:                                                struct{}{},
		`schedule_edit_and_proceed_tap`:                                 struct{}{},
		`LocationMapScreenBackButtonTapped`:                             struct{}{},
		`MQTT_PROCESS_STOPPED`:                                          struct{}{},
		`O2CrystalCNRDeliveryMissedTapped`:                              struct{}{},
		`SDKNativeOtpResendTapped`:                                      struct{}{},
		`SdkNoCVVAnimation`:                                             struct{}{},
		`ad_campaign_create_tap`:                                        struct{}{},
		`mx_dining_web_pageview`:                                        struct{}{},
		`V2HomeLocationLoaded`:                                          struct{}{},
		`navbar_moderation_page_button`:                                 struct{}{},
		`ratings_meta_tapped`:                                           struct{}{},
		`O2TipAmountRemoved`:                                            struct{}{},
		`TabSelected`:                                                   struct{}{},
		`custom_offers_data_preview_click`:                              struct{}{},
		`fetched_from_geocode_cache`:                                    struct{}{},
		`save_tip_redis_key_change`:                                     struct{}{},
		`web_universal_search_result_click`:                             struct{}{},
		`contact_details_edit_name`:                                     struct{}{},
		`mx_dining_web_help_center_click_on_cancel_btn`:                 struct{}{},
		`O2AddAddressDeliveryInstructionsType`:                          struct{}{},
		`menu_catalogue_approve_button`:                                 struct{}{},
		`O2MenuFabOnboardingImpression`:                                 struct{}{},
		`O2MenuPriceParityWarning`:                                      struct{}{},
		`PUHomePageLoaded`:                                              struct{}{},
		`AppLaunchFinalDetectedGPSLocation`:                             struct{}{},
		`GenericWebviewCTA-dining_gold_upgrade_newapp`:                  struct{}{},
		`order_history_snippet_impression`:                              struct{}{},
		`post_order_secondary_cell_expanded_collapsed`:                  struct{}{},
		`SDKVSCDisableAccepted`:                                         struct{}{},
		`sales_ops_action`:                                              struct{}{},
		`update-dining-mx-res-contact`:                                  struct{}{},
		`navbar_menu_page_button`:                                       struct{}{},
		`remote_config_fetch_success`:                                   struct{}{},
		`photo_update_tap`:                                              struct{}{},
		`menu_excel_upload_done_button`:                                 struct{}{},
		`NOTIFICATION_PERMISSION`:                                       struct{}{},
		`LocationPrecisePromptTapped`:                                   struct{}{},
		`documents-ready-ack-click`:                                     struct{}{},
		`res_status_socket_v2`:                                          struct{}{},
		`se_account_manager_opened`:                                     struct{}{},
		`ad_campaign_create_budget_help_tap`:                            struct{}{},
		`web_res_scroll'`:                                               struct{}{},
		`SDKPromoPagePaymentMethodAutoSelected`:                         struct{}{},
		`O2CrystalInstructionAudioChangeTapped`:                         struct{}{},
		`O2CrystalAnnouncementCardImpression`:                           struct{}{},
		`order-sound-not-ringing`:                                       struct{}{},
		`O2PaymentMethodWebViewLoaded`:                                  struct{}{},
		`collection_expanded_impression_homepage`:                       struct{}{},
		`vendor_api_failure_12_309`:                                     struct{}{},
		`GoldIntroResCardsTap`:                                          struct{}{},
		`OrderStatusAppFailure`:                                         struct{}{},
		`StorySwipedLeft`:                                               struct{}{},
		`TicketCartPageLoaded`:                                          struct{}{},
		`gold_special_cart_load_success`:                                struct{}{},
		`owner_hub_home_shown`:                                          struct{}{},
		`web_current_device_location_block`:                             struct{}{},
		`res_offline_on_time_close_tap`:                                 struct{}{},
		`ad_payment_pg_cancel`:                                          struct{}{},
		`update_delivery_area`:                                          struct{}{},
		`ad_campaign_details_view_trends_tap`:                           struct{}{},
		`api_timeout_error`:                                             struct{}{},
		`feedback_recommended_info_checkbox_tapped`:                     struct{}{},
		`le-transfer/request-form-impression`:                           struct{}{},
		`mx_feedback`:                                                   struct{}{},
		`ownership_transfer_delete_image`:                               struct{}{},
		`vendor_api_failure_12_8`:                                       struct{}{},
		`BrandPageOrderNowClicked`:                                      struct{}{},
		`AnimatedRollingSnippetTapped`:                                  struct{}{},
		`location_config_on_app_launch`:                                 struct{}{},
		`more_tap`:                                                      struct{}{},
		`raise_ticket_tap`:                                              struct{}{},
		`rejection_v2_modal_impression`:                                 struct{}{},
		`O2HomeVideoCardTapped`:                                         struct{}{},
		`menu_create_variant_tap`:                                       struct{}{},
		`O2MQTTPollData`:                                                struct{}{},
		`LoginPageFeaturesTapped`:                                       struct{}{},
		`SDKVSCOptionUnchecked`:                                         struct{}{},
		`EverydayHomeCookRailItemTap`:                                   struct{}{},
		`amp_app_pitch_bottom_bar_use_app_click`:                        struct{}{},
		`inventory_menu_score_banner_impression`:                        struct{}{},
		`ssid_cached_location_exists`:                                   struct{}{},
		`O2MenuVideoTap`:                                                struct{}{},
		`instant_order_id_copied`:                                       struct{}{},
		`mx_action_mark_As_delivered`:                                   struct{}{},
		`fetched_from_api`:                                              struct{}{},
		`in_app_manual_update_impression`:                               struct{}{},
		`tr_phone_verification_skipped`:                                 struct{}{},
		`ProCartRemovePromoTapped`:                                      struct{}{},
		`O2CartTimeSnippetImpression`:                                   struct{}{},
		`deeplink_zfeedback`:                                            struct{}{},
		`catalogue_header_delete_cancel`:                                struct{}{},
		`init_sdk_lifecycle`:                                            struct{}{},
		`V14HomePageLoaded`:                                             struct{}{},
		`digital_contract_sign_creation_fetch_pan_success`:              struct{}{},
		`send_contract_tutorial_click`:                                  struct{}{},
		`O2FeedingIndiaSnippetTapped`:                                   struct{}{},
		`ListingParseCuration2ItemsParsed`:                              struct{}{},
		`gclanding_click`:                                               struct{}{},
		`serviceability_call_from_cart`:                                 struct{}{},
		`JumboEnameO2ChangeAddressMenuClickOldAddress`:                  struct{}{},
		`diy-duplicate-res-caution`:                                     struct{}{},
		`O2DropPinBackButtonTapped`:                                     struct{}{},
		`AppWillTerminatedV2`:                                           struct{}{},
		`O2CrystalConciergeChatWithUsButtonTapEvent`:                    struct{}{},
		`le_get_verification_status`:                                    struct{}{},
		`O2CrystalNDGDeliveredSnippetImpression`:                        struct{}{},
		`JumboEnameO2ChangePersonalDetails`:                             struct{}{},
		`branch_deeplink_received`:                                      struct{}{},
		`deeplink_healthy`:                                              struct{}{},
		`menu_booster_banner_impression`:                                struct{}{},
		`res_offline_reasons_close_tap`:                                 struct{}{},
		`menu_abrupt_price_change`:                                      struct{}{},
		`feedback_screen`:                                               struct{}{},
		`backgrounds_download_zoom_click`:                               struct{}{},
		`search_location_fetched_from_cookie-1`:                         struct{}{},
		`AccessTokenRemoveSource`:                                       struct{}{},
		`O2CrystalOfflineRetryButtonTapEvent`:                           struct{}{},
		`O2CrystalContactlessOptInTapped`:                               struct{}{},
		`order_id_copy_tap`:                                             struct{}{},
		`web_rail_filters_applied`:                                      struct{}{},
		`pre_dc_calc_logs`:                                              struct{}{},
		`digital_contract_merchant_details_open_success`:                struct{}{},
		`restaurant_page_viewed`:                                        struct{}{},
		`WebViewBackButtonTapped`:                                       struct{}{},
		`mweb_search_bar_click_amp_nonamp_redirect`:                     struct{}{},
		`rider_rating_submit_click`:                                     struct{}{},
		`upload_photo_page_opened`:                                      struct{}{},
		`TRRestaurantDetailsSectionCallRestaurantTapped`:                struct{}{},
		`serviceability_reminder_notification_v2`:                       struct{}{},
		`O2CrystalCarouselIntercityImpression`:                          struct{}{},
		`O2MenuItemImageTapped`:                                         struct{}{},
		`WEBVIEW_OPENED`:                                                struct{}{},
		`res_page_similar_res_card_click`:                               struct{}{},
		`zfb_review_page_open`:                                          struct{}{},
		`SDKUpiGpayHealthCheckStatusFailed`:                             struct{}{},
		`restaurant_page`:                                               struct{}{},
		`FiltersQuickFiltersSelected`:                                   struct{}{},
		`O2OfferSnackbarImpression`:                                     struct{}{},
		`contact_details_delete_tap`:                                    struct{}{},
		`deeplink_goout_events`:                                         struct{}{},
		`location_error`:                                                struct{}{},
		`AerobarTapped`:                                                 struct{}{},
		`PlaceOrderFailedOnCart`:                                        struct{}{},
		`O2PromoCodeBecameFirstResponder`:                               struct{}{},
		`bulk_order_kpt_change`:                                         struct{}{},
		`filter_apply_tap`:                                              struct{}{},
		`fetch_item_stock_status`:                                       struct{}{},
		`O2_ORDER_SERVICEABILITY''`:                                     struct{}{},
		`EmailLoginFeedBlinkitInstallViewed`:                            struct{}{},
		`LocationHomeGPSPromptTapped`:                                   struct{}{},
		`3dtouch_share`:                                                 struct{}{},
		`notif_ring_volume_change`:                                      struct{}{},
		`photo_upload_min_aspect_ratio_error`:                           struct{}{},
		`reject_order_click`:                                            struct{}{},
		`GoldMembershipDeviceLimitingBannerTap`:                         struct{}{},
		`O2CrystalConciergeSnippetImpressionEvent`:                      struct{}{},
		`bottom_bar_impression`:                                         struct{}{},
		`japi_tracking`:                                                 struct{}{},
		`menu_disclaimer_acceptance_button`:                             struct{}{},
		`mx-dashboard-navigatorConnectivity`:                            struct{}{},
		`resend_otp_tapped`:                                             struct{}{},
		`JumboEnameO2OrderPlaceConfirmShown`:                            struct{}{},
		`download_gstin_doc_tap`:                                        struct{}{},
		`O2CartItemCustomiseButtonTapped`:                               struct{}{},
		`RES_FRAUD`:                                                     struct{}{},
		`refund_orders_tracking`:                                        struct{}{},
		`O2AddItemMakeBlockerTapEvent`:                                  struct{}{},
		`pro_tnc_tapped`:                                                struct{}{},
		`PaymentSelectionPopupShown`:                                    struct{}{},
		`NO_CONTENT_VIEW_RETRY_CLICKED`:                                 struct{}{},
		`SDKAddVpaSuccess`:                                              struct{}{},
		`O2MenuPostOrderImpression`:                                     struct{}{},
		`menu_image_replaced`:                                           struct{}{},
		`HomeWebViewLoadStarted`:                                        struct{}{},
		`digital_contract_sign_creation_error`:                          struct{}{},
		`socket_service_started`:                                        struct{}{},
		`O2CartInstructionAudioChangeTapped`:                            struct{}{},
		`live_activities_removed`:                                       struct{}{},
		`order_delay_state_change`:                                      struct{}{},
		`ListingParseCuration2HeadersParsed`:                            struct{}{},
		`SDKADCBErrorInTouchPointsAPI`:                                  struct{}{},
		`mx_dining_web_your_customers_click_on_add_edit_number`:         struct{}{},
		`web_rail_filters_applied'`:                                     struct{}{},
		`CRYSTAL_STOP_FALLBACK`:                                         struct{}{},
		`ResCallButtonTapped`:                                           struct{}{},
		`UserPrefSubmitTapped`:                                          struct{}{},
		`EDIT_EMAIL_FLOW_ONE_FA_RESEND`:                                 struct{}{},
		`ResPageSlotOfferSectionImpression`:                             struct{}{},
		`ResSimilarRestaurantsImpression`:                               struct{}{},
		`kpt_delay_tap`:                                                 struct{}{},
		`event_search_page_impression`:                                  struct{}{},
		`notif_not_enabled`:                                             struct{}{},
		`ZomatoWebSigninSuccess`:                                        struct{}{},
		`brand_page_back_tap`:                                           struct{}{},
		`SDKPaymentOptionsOpenVPAAddition`:                              struct{}{},
		`ad_campaign_dialog_impression`:                                 struct{}{},
		`growth_card_tap`:                                               struct{}{},
		`contract_failure`:                                              struct{}{},
		`order_share_rider_call_click`:                                  struct{}{},
		`mx_image_loading_failed`:                                       struct{}{},
		`menu_taxes_dropdown`:                                           struct{}{},
		`SDKVSCDisableClicked`:                                          struct{}{},
		`ZPLCrystalPageBannerImpression`:                                struct{}{},
		`digital_contract_open_error`:                                   struct{}{},
		`rejected_order_trends_bar_impression`:                          struct{}{},
		`deeplink_c-57`:                                                 struct{}{},
		`LocationOSPromptTapped`:                                        struct{}{},
		`hp_tnc_agree_tap`:                                              struct{}{},
		`order_history_listing_tap`:                                     struct{}{},
		`ownership-transfer/user-not-owner`:                             struct{}{},
		`V2ShimmerSeenTime`:                                             struct{}{},
		`api_deeplink_login_failed`:                                     struct{}{},
		`gstin_new_gstin_number_save_tap`:                               struct{}{},
		`food_quality_tag_safe_oil_only_tap`:                            struct{}{},
		`LocationOpenWithZomatoSuccess`:                                 struct{}{},
		`ResOBPImpression`:                                              struct{}{},
		`ReviewPageAddMediaTapped`:                                      struct{}{},
		`O2_HOME_REQUESTV2`:                                             struct{}{},
		`ownership-transfer/accepted-by-govid/rate-card-accepted`:       struct{}{},
		`O2FabItemClicked`:                                              struct{}{},
		`deeplink_otp_login`:                                            struct{}{},
		`mx_dining_web_payout_get_current_cycle_api_request_success`:    struct{}{},
		`with-entity-info-parameters`:                                   struct{}{},
		`edition_tsp_checks_timeout`:                                    struct{}{},
		`O2CrystalNDGOrderDelayedBanner`:                                struct{}{},
		`O2CrystalNdgScratchCardImpressionEvent`:                        struct{}{},
		`SDKNoCVVToOTPNotTrigger`:                                       struct{}{},
		`SdkMakePaymentCallFailure`:                                     struct{}{},
		`O2OfferItemSelectionSheetLoaded`:                               struct{}{},
		`refresh-call-init`:                                             struct{}{},
		`ticket_reply_send`:                                             struct{}{},
		`menu_item_oos_toggle_pre_dialog_positive_btn_tap`:              struct{}{},
		`allowpushnotificationpermission_allow`:                         struct{}{},
		`mx_bot_flow_connect_to_agent`:                                  struct{}{},
		`ListingsPopupLoaded`:                                           struct{}{},
		`SDKPaymentOptionsRemoveBankTapped`:                             struct{}{},
		`res_delivery_offer_imp-1`:                                      struct{}{},
		`order_blocker_impression''`:                                    struct{}{},
		`feedback_order_rating_submit_tapped`:                           struct{}{},
		`GenericWebviewCTA-UAE_0421`:                                    struct{}{},
		`all-pos-page-impression`:                                       struct{}{},
		`O2CrystalCarouselBlinkitTapped`:                                struct{}{},
		`O2OfferToastImpression`:                                        struct{}{},
		`deeplink_events`:                                               struct{}{},
		`in_app_review_launch`:                                          struct{}{},
		`PUMenuItemsImpression`:                                         struct{}{},
		`sound_notification_off`:                                        struct{}{},
		`vendor_api_failure_12_203`:                                     struct{}{},
		`O2CrystalNDGOrderNotAcceptedBanner`:                            struct{}{},
		`hp_firebase_token_register`:                                    struct{}{},
		`hp_home_screen_impression`:                                     struct{}{},
		`get-app-form-send-link-success-tracking`:                       struct{}{},
		`menu_excel_download_button`:                                    struct{}{},
		`O2CrystalSustainabilityComponentTapEvent`:                      struct{}{},
		`O2CrystalUserSnippetSharingSnippetTapped`:                      struct{}{},
		`menu_image_score_ok_clicked`:                                   struct{}{},
		`all-pos-page-api-success`:                                      struct{}{},
		`image-change-modal-impression`:                                 struct{}{},
		`deeplink_bookingRating`:                                        struct{}{},
		`pickedup_tab_click`:                                            struct{}{},
		`prep_tab_tap`:                                                  struct{}{},
		`menu_z_gallery_items_selection_map_tap`:                        struct{}{},
		`logout_from_all_devices_tap`:                                   struct{}{},
		`gcorder_support`:                                               struct{}{},
		`res_delivery_menu_category''`:                                  struct{}{},
		`CrystalRiderAnimation`:                                         struct{}{},
		`EverydayHomeCookVideoViewed`:                                   struct{}{},
		`edit_email_tapped`:                                             struct{}{},
		`mx_fssai_notification_display`:                                 struct{}{},
		`O2GoldPageLoaderLostSavingsAddGold`:                            struct{}{},
		`CART_PARSED`:                                                   struct{}{},
		`ThirdPartyAPIs\IDFY\IDFYApiCaller_success`:                     struct{}{},
		`V14HomePageEmptyList`:                                          struct{}{},
		`O2CrystalChatButtonTapped`:                                     struct{}{},
		`res_offline_reasons_option_tap`:                                struct{}{},
		`otp_request_failed_phone_verification`:                         struct{}{},
		`MakeOrderCancelDialogV2`:                                       struct{}{},
		`LatLngCacheUpdated`:                                            struct{}{},
		`WEBVIEW_FAILED`:                                                struct{}{},
		`deeplink_pay`:                                                  struct{}{},
		`deeplink_red_cart`:                                             struct{}{},
		`menu_create_variant_impression`:                                struct{}{},
		`promos`:                                                        struct{}{},
		`learning_centre_search_input`:                                  struct{}{},
		`moderation_submit_approval_button`:                             struct{}{},
		`media_repo_menu_image_map_images`:                              struct{}{},
		`Dev_login_state`:                                               struct{}{},
		`O2OFSESenderInfoImpression`:                                    struct{}{},
		`duplicate_ticket_click`:                                        struct{}{},
		`ListingParseCuration2Ends`:                                     struct{}{},
		`outlet_info_tap`:                                               struct{}{},
		`manage_outlet_info`:                                            struct{}{},
		`FRAUD_MERCHANT_DETECTION`:                                      struct{}{},
		`SDKTokenRefreshCompleted`:                                      struct{}{},
		`notification_edit_email_impression`:                            struct{}{},
		`ad_campaign_reset_slider_tap`:                                  struct{}{},
		`MicTapped`:                                                     struct{}{},
		`GlowTap`:                                                       struct{}{},
		`offer_config_success_dialog_impression`:                        struct{}{},
		`menu_edit_category_click`:                                      struct{}{},
		`O2CartSaltOfferImpression`:                                     struct{}{},
		`O2GoldPageLoaderLostSavingsViewed`:                             struct{}{},
		`LoginPageLoaded`:                                               struct{}{},
		`digital_contract_approval`:                                     struct{}{},
		`deeplink_bookmark`:                                             struct{}{},
		`O2CartOfferItemEditTap`:                                        struct{}{},
		`mx_dining_web_payout_get_past_cycles_api_request_success`:      struct{}{},
		`JumboEnameO2SaveNewAddress`:                                    struct{}{},
		`socket_event_disabled`:                                         struct{}{},
		`mx_dining_web_your_customers_click_on_update_number`:           struct{}{},
		`Image Clicked`:                                                 struct{}{},
		`HowItWorksPageLoaded`:                                          struct{}{},
		`ZPLShareTapped`:                                                struct{}{},
		`remote_config_fetch_failed`:                                    struct{}{},
		`rider_map_closed`:                                              struct{}{},
		`V2HomePageLoaded`:                                              struct{}{},
		`cuisine_successfull_upload`:                                    struct{}{},
		`view_map_search`:                                               struct{}{},
		`vendor_api_failure_12_321`:                                     struct{}{},
		`O2MenuSuperOfferImpression`:                                    struct{}{},
		`serviceability_reminder_process_user`:                          struct{}{},
		`res_logs_toggle_events`:                                        struct{}{},
		`zgyb_success_page_mount`:                                       struct{}{},
		`prefered-pos-page-api-error`:                                   struct{}{},
		`xFHBSIGv`:                                                      struct{}{},
		`GPSLocationFetchFailed`:                                        struct{}{},
		`IntentData`:                                                    struct{}{},
		`pip_buffer_not_completed`:                                      struct{}{},
		`tape_order_tap`:                                                struct{}{},
		`show_image_guideline`:                                          struct{}{},
		`O2CartAbandonImpression`:                                       struct{}{},
		`EMPTY_SEARCH_RESULTS`:                                          struct{}{},
		`O2MenuViewCart`:                                                struct{}{},
		`O2PostOrderCartAddressSnippetTapped`:                           struct{}{},
		`TEMP_ERROR_SCREEN_DETAIL`:                                      struct{}{},
		`PhoneNoSelected`:                                               struct{}{},
		`res_page_mobile_tabs_click`:                                    struct{}{},
		`menu-boosterpackage-payment-status`:                            struct{}{},
		`socket_event_emitted`:                                          struct{}{},
		`TRSlotCancellationPopUpGoBackButtonTapped`:                     struct{}{},
		`O2CrystalResFraudImpression`:                                   struct{}{},
		`O2CrystalLovedByDpImpression`:                                  struct{}{},
		`SDKBankOptionsPageLoaded`:                                      struct{}{},
		`O2CrystalInstructionChangeTapped`:                              struct{}{},
		`hp_fcm_msg_received`:                                           struct{}{},
		`PUMenuSuperOfferCopied`:                                        struct{}{},
		`confirm_price_bottom_sheet_tap`:                                struct{}{},
		`mov_config_run`:                                                struct{}{},
		`O2FeedingIndiaSnippetImpression`:                               struct{}{},
		`SDKEnterCVVLoaded`:                                             struct{}{},
		`api_call_success`:                                              struct{}{},
		`current_photo_permission`:                                      struct{}{},
		`upi_response`:                                                  struct{}{},
		`webview-dining-tr-restrictions-get-all-api-success`:            struct{}{},
		`post_order_secondary_cell_interacted`:                          struct{}{},
		`Dev_auto_logout`:                                               struct{}{},
		`SDKPaymentMethodWebViewBackTapped`:                             struct{}{},
		`SDKTokenizeSuccess`:                                            struct{}{},
		`WriteReviewPageXTapped`:                                        struct{}{},
		`growth_offer_card_tap`:                                         struct{}{},
		`item_not_live_details_tap`:                                     struct{}{},
		`poi-geocode-optimization`:                                      struct{}{},
		`send_otp_tapped`:                                               struct{}{},
		`karma_score`:                                                   struct{}{},
		`dedupe_high_confidence`:                                        struct{}{},
		`collection_click`:                                              struct{}{},
		`delivery_restaurant_status_scheduler_archive`:                  struct{}{},
		`payment_failed_data`:                                           struct{}{},
		`socket_event_received`:                                         struct{}{},
		`O2MenuHeaderVideoImpression`:                                   struct{}{},
		`ad_renew_select_extra_budget`:                                  struct{}{},
		`TRSlotCancellationPopUpLoaded`:                                 struct{}{},
		`order_search_input_pasted`:                                     struct{}{},
		`init_sdk_foreground`:                                           struct{}{},
		`review_search_tapped`:                                          struct{}{},
		`O2CrystalOrderSharingButtonTapped`:                             struct{}{},
		`event_impression`:                                              struct{}{},
		`item_delivery_status`:                                          struct{}{},
		`res_delivery_veg_select`:                                       struct{}{},
		`RefundExpandButtonTapped`:                                      struct{}{},
		`O2MenuViewOtherRestaurantsShown`:                               struct{}{},
		`ResDetailsCuisineTapped`:                                       struct{}{},
		`search-location-change`:                                        struct{}{},
		`ads_help_question_tap`:                                         struct{}{},
		`deeplink_grocery`:                                              struct{}{},
		`ResAwardsWinnerCardImpression`:                                 struct{}{},
		`O2MenuSearchClicked`:                                           struct{}{},
		`link_create_account_popup_tapped`:                              struct{}{},
		`prefered-pos-page-api-success`:                                 struct{}{},
		`GoldPlanPageChatShown`:                                         struct{}{},
		`AppUpdatePromptImpression`:                                     struct{}{},
		`GoldIntroCardButtonTap`:                                        struct{}{},
		`O2MenuClearAllItemsTapped`:                                     struct{}{},
		`O2RestaurantCallScreen`:                                        struct{}{},
		`menu_cancel_item_image_upload`:                                 struct{}{},
		`gccart_click`:                                                  struct{}{},
		`ownership-transfer/get-contract-page`:                          struct{}{},
		`MenuSearchIconTapped`:                                          struct{}{},
		`MenuSearchTyped`:                                               struct{}{},
		`SDKNativeOtpUserConsentTimeout`:                                struct{}{},
		`res_offline_view_details_impression`:                           struct{}{},
		`zauth_login_success`:                                           struct{}{},
		`reporting_outlet_breakdown_filter_applied`:                     struct{}{},
		`photo_impression`:                                              struct{}{},
		`map_existing_items_save`:                                       struct{}{},
		`O2PaymentButtonClicked`:                                        struct{}{},
		`aerobar_edit_phone_impression`:                                 struct{}{},
		`O2OFSEResContactImpression`:                                    struct{}{},
		`O2CartSuperaddonImpression`:                                    struct{}{},
		`O2ChangeAddressFromCart`:                                       struct{}{},
		`O2MenuFooterAdRailToggle`:                                      struct{}{},
		`add-more-menu-dishes-click`:                                    struct{}{},
		`order-online`:                                                  struct{}{},
		`menu_edit_sub_category_click`:                                  struct{}{},
		`moderation_open_menu_button`:                                   struct{}{},
		`review_bar_post_retry`:                                         struct{}{},
		`O2CrystalUserAlternateContactImpression`:                       struct{}{},
		`orderStatusTrackingData`:                                       struct{}{},
		`searched_result_tapped`:                                        struct{}{},
		`toggle-time-sorting-mode`:                                      struct{}{},
		`SDKWalletLinkingOTPPageLoaded`:                                 struct{}{},
		`ZPLLowInternetConfigTriggered`:                                 struct{}{},
		`adid`:                                                          struct{}{},
		`make_lp_id_switch`:                                             struct{}{},
		`JumboEnameinCollectionTapped`:                                  struct{}{},
		`mx_dashboard_env_meta`:                                         struct{}{},
		`mx_web_cancel_tap`:                                             struct{}{},
		`search_bar_tapped`:                                             struct{}{},
		`SDKAddCardCVVEntered`:                                          struct{}{},
		`support_time_slot_submission`:                                  struct{}{},
		`meals_page_load`:                                               struct{}{},
		`deeplink_apps.apple.com`:                                       struct{}{},
		`MFsDVaGA`:                                                      struct{}{},
		`TRACK_ITEM_NULL_EVENT`:                                         struct{}{},
		`O2CrystalThirdPartyVendorTrackingV15`:                          struct{}{},
		`O2CrystalTopTrackerImpression`:                                 struct{}{},
		`O2PaymentMethodDetailsNameEntered`:                             struct{}{},
		`O2ChangePersonalDetails`:                                       struct{}{},
		`O2PreSearchSuggestionImpression`:                               struct{}{},
		`O2CartPhoneNoChangeTapped`:                                     struct{}{},
		`GPSLocationFetchFailedWithStaleLocations`:                      struct{}{},
		`ad_cmapign_success_screen_open`:                                struct{}{},
		`O2MenuFarawayOutletPopupTap`:                                   struct{}{},
		`bill_details_tap`:                                              struct{}{},
		`fetch_order_from_cache`:                                        struct{}{},
		`mx_app_item_edit_access`:                                       struct{}{},
		`O2ExtrasPaymentMethodChangeClicked`:                            struct{}{},
		`ratings_notifications_toggle_tap`:                              struct{}{},
		`O2CrystalRainBannerV16Impression`:                              struct{}{},
		`AnimatedSplashMediaFailureEvent`:                               struct{}{},
		`GoldMembershipHeaderTap`:                                       struct{}{},
		`riderTracking`:                                                 struct{}{},
		`order_history_snippet_rate_now_tapped`:                         struct{}{},
		`download_invoices_confirm`:                                     struct{}{},
		`mx_dining_web_payout_apply_filter_past_cycles`:                 struct{}{},
		`rain_status_override_logs`:                                     struct{}{},
		`mx_dining_web_add_story_api_failed`:                            struct{}{},
		`GenericWebviewCTA-max_safety_dining_2022_know_more`:            struct{}{},
		`GenericWebviewCTA-legends_express_calendar_bangalore_nv`:       struct{}{},
		`O2TipSectionButtonTapEvent`:                                    struct{}{},
		`event_feedback_tab_switch`:                                     struct{}{},
		`rider_map_my_location_tapped`:                                  struct{}{},
		`CRYSTAL_FALLBACK_BG_TO_FG_CALLED`:                              struct{}{},
		`CartUpdateLocationButtonImpression`:                            struct{}{},
		`EVENT_TICKET_CONFIRMATION_WHATSAPP_MSG_SENT`:                   struct{}{},
		`call_rider_click`:                                              struct{}{},
		`new_order_notification_deleted`:                                struct{}{},
		`runnr_request_menu_categories_mismatch`:                        struct{}{},
		`online_ordering_web_click_on_go_to_dining_button`:              struct{}{},
		`topic_event_subscribe_success`:                                 struct{}{},
		`mx_dining_web_menu_click_on_preview_menu`:                      struct{}{},
		`event_trouble_shoot_page_tapped`:                               struct{}{},
		`tapped_apple`:                                                  struct{}{},
		`order_search_cross_click`:                                      struct{}{},
		`O2CrystalAppRatingBlockerPositiveTapEvent`:                     struct{}{},
		`O2HomeSearchCancel`:                                            struct{}{},
		`LocationMapScreenSearchBarTapped`:                              struct{}{},
		`ZPLPrevGameStatsButtonTapped`:                                  struct{}{},
		`cc-card-impression`:                                            struct{}{},
		`current-location-api-success`:                                  struct{}{},
		`res_page_carousel_arrow_click`:                                 struct{}{},
		`tour_exit_tour`:                                                struct{}{},
		`O2CrystalRefreshButtonImpression`:                              struct{}{},
		`hp_order_status_impression`:                                    struct{}{},
		`O2CartDPFreeDc`:                                                struct{}{},
		`on_load_modal_dismissed`:                                       struct{}{},
		`mx_dining_web_events_get_event_details_auto_fill_api`:          struct{}{},
		`recommended_search_event`:                                      struct{}{},
		`LocationRadioButtonTapped`:                                     struct{}{},
		`SDKInitCallSuccess`:                                            struct{}{},
		`price_parity_button_continue`:                                  struct{}{},
		`brand_page_loaded`:                                             struct{}{},
		`moderation_rejected_images`:                                    struct{}{},
		`O2PostOrderCartBackToOrderButtonImpression`:                    struct{}{},
		`gold_special_cart_pay_tap`:                                     struct{}{},
		`instruction_post_acceptance`:                                   struct{}{},
		`mx_dashboard_load_source`:                                      struct{}{},
		`gstin_option_save_tap`:                                         struct{}{},
		`init_sdk_timeout`:                                              struct{}{},
		`gstin_document_uploaded`:                                       struct{}{},
		`webview-event-details-click-on-retry-again`:                    struct{}{},
		`O2DeliveryInstructionSaveCheckboxTapped`:                       struct{}{},
		`MENU_REQUEST`:                                                  struct{}{},
		`MenuDropOff`:                                                   struct{}{},
		`hp-page-view-per-res`:                                          struct{}{},
		`timeout_handled`:                                               struct{}{},
		`time_interval_tap`:                                             struct{}{},
		`redirect_deeplink_tracking`:                                    struct{}{},
		`video_ads_preview_tap`:                                         struct{}{},
		`LoginConfigAPISuccess`:                                         struct{}{},
		`ChatBundleInitialized`:                                         struct{}{},
		`zpay_chat_button_tapped`:                                       struct{}{},
		`bookEmailCTA`:                                                  struct{}{},
		`CrystalPayLaterPaymentMethodSelected`:                          struct{}{},
		`GOLD_DESKTOP_CART_POPUP_SHOWN`:                                 struct{}{},
		`home_pinging_started`:                                          struct{}{},
		`deeplink_`:                                                     struct{}{},
		`merchant_rider_rbt_data`:                                       struct{}{},
		`offer_config_tnc_tap`:                                          struct{}{},
		`mx_dining_web_events_get_event_listing_api`:                    struct{}{},
		`O2MenuSimilarResBannerShown`:                                   struct{}{},
		`res_growth_refresh_tap`:                                        struct{}{},
		`O2CartSuperaddonDishTapped`:                                    struct{}{},
		`O2CrystalTimelineImpression`:                                   struct{}{},
		`ResReviewBoxTapped`:                                            struct{}{},
		`auth_sdk_refresh_call_failure`:                                 struct{}{},
		`flink_queued_res`:                                              struct{}{},
		`item_stock_status_update`:                                      struct{}{},
		`pro_promo_cfo_logs`:                                            struct{}{},
		`reviews_tab_tapped`:                                            struct{}{},
		`deeplink_takeaway`:                                             struct{}{},
		`SDKNativeOtpUserConsentDenied`:                                 struct{}{},
		`O2MenuViewed`:                                                  struct{}{},
		`O2DropPinPinMoved`:                                             struct{}{},
		`save_timings_click`:                                            struct{}{},
		`voice-instructions-loaded`:                                     struct{}{},
		`location_audio_save_tap`:                                       struct{}{},
		`Sneakpeek_campaign`:                                            struct{}{},
		`learning_centre_video_tap`:                                     struct{}{},
		`VPN_FAILED`:                                                    struct{}{},
		`SDKAddVpaFailure`:                                              struct{}{},
		`SearchLocationSearchClearTapped`:                               struct{}{},
		`TableReservation`:                                              struct{}{},
		`menu_item_edit_click`:                                          struct{}{},
		`menu_score_intro_calculate_later_tap`:                          struct{}{},
		`support_res_offline_action_tap`:                                struct{}{},
		`fetch_order_by_states_params`:                                  struct{}{},
		`res_delivery_add_item`:                                         struct{}{},
		`web_universal_search_query''`:                                  struct{}{},
		`GoldSearchPageViewed`:                                          struct{}{},
		`O2CrystalCookingInstructionsImpression`:                        struct{}{},
		`menu_enhanced_tap`:                                             struct{}{},
		`network_kit_retry_exhausted`:                                   struct{}{},
		`custom_offers_data_submit_success`:                             struct{}{},
		`mx_dining_web_menu_upload_menu_api_request_failure`:            struct{}{},
		`mx_dining_web_transaction_cancel_refund_clicked`:               struct{}{},
		`mx_dining_web_events_download_guest_list_api`:                  struct{}{},
		`investor-relations-email-subscription`:                         struct{}{},
		`O2CrystalResAddItemTapped`:                                     struct{}{},
		`SavedAddressEditTapped`:                                        struct{}{},
		`StoryAutoScrolled`:                                             struct{}{},
		`download_settlement_confirm`:                                   struct{}{},
		`review_bar_post_success_view`:                                  struct{}{},
		`mx_dining_web_events_click_on_save_next_btn_basic_information`: struct{}{},
		`google_login_success`:                                          struct{}{},
		`RedAccountPagePlanTapped`:                                      struct{}{},
		`webview-dining-tr-restrictions-get-all-api-failed`:             struct{}{},
		`react_18_error'`:                                               struct{}{},
		`O2CrystalDiningEventBannerTapped`:                              struct{}{},
		`SDKPaymentMethodWebViewLoadFailure`:                            struct{}{},
		`activate_billing_account`:                                      struct{}{},
		`location_diy_view_on_google_maps_tap`:                          struct{}{},
		`custom_offers_dialog_impression`:                               struct{}{},
		`helpful_submit_tap`:                                            struct{}{},
		`web_res_scroll`:                                                struct{}{},
		`res_page_single_review_user_image_click`:                       struct{}{},
		`review_comment_view_all`:                                       struct{}{},
		`O2PaymentMethodDetailsContinueTapped`:                          struct{}{},
		`O2MenuFooterAdImpression`:                                      struct{}{},
		`GenericWebviewCTA-zomato_gold_dining_carnival_home`:            struct{}{},
		`complaint_notification_dismiss_clicked`:                        struct{}{},
		`menu_image_replace_clicked`:                                    struct{}{},
		`NewUserLocationSaveAddressButtonTapped`:                        struct{}{},
		`O2CrystalDIYSupportChatButtonTapped`:                           struct{}{},
		`GOLD_INTERMEDIATE_MILESTONE_REACHED`:                           struct{}{},
		`o2_restaurant_visibility_status`:                               struct{}{},
		`offers_tab_switch`:                                             struct{}{},
		`O2CrystalRiderAddAudioInstructionImpression`:                   struct{}{},
		`video_thumbnail_click`:                                         struct{}{},
		`ProPlanChatTap`:                                                struct{}{},
		`SDKCancelPaymentInitiated`:                                     struct{}{},
		`O2OfferItemSelectionAction`:                                    struct{}{},
		`FilterModalSearchTapped`:                                       struct{}{},
		`ProPlanPageLoadFailed`:                                         struct{}{},
		`logout-view-displayed`:                                         struct{}{},
		`reporting_tab_tap`:                                             struct{}{},
		`support_carousal_impression`:                                   struct{}{},
		`tour_back_click`:                                               struct{}{},
		`FiltersSortBySelected`:                                         struct{}{},
		`AddDeviceCallSuccess`:                                          struct{}{},
		`MQTT_PROCESS_RESUMED`:                                          struct{}{},
		`ZomatoWebOTPLoaded`:                                            struct{}{},
		`image-change-success`:                                          struct{}{},
		`menu_item_delete_confirm`:                                      struct{}{},
		`profile_tap`:                                                   struct{}{},
		`reporting_tap`:                                                 struct{}{},
		`tour_exit_confirm`:                                             struct{}{},
		`O2CrystalContactlessFAQTapped`:                                 struct{}{},
		`gift_card_cart_update_personal_details_button_impression`:      struct{}{},
		`deeplink_rate`:                                                 struct{}{},
		`O2CartPromoRemoved`:                                            struct{}{},
		`ResMapDirectionTapped`:                                         struct{}{},
		`EDIT_EMAIL_FLOW_ENTER_EMAIL_MODAL_IMPRESSION`:                  struct{}{},
		`bill_details_click`:                                            struct{}{},
		`learn_more_permissions_tap`:                                    struct{}{},
		`ownership-transfer/payout-greater-than-outstanding`:            struct{}{},
		`O2CrystalLWGV3ImpressionEvent`:                                 struct{}{},
		`instant_late_order_aerobar_rendered`:                           struct{}{},
		`menu_booster_pack_banner_tap`:                                  struct{}{},
		`GoldBottomSheetAddAddressTap`:                                  struct{}{},
		`mweb_amp_open_app_review_modal_resinfo_page_open_click`:        struct{}{},
		`O2CrystalChatSnippetImpression`:                                struct{}{},
		`O2CrystalGroferAdBannerImpression`:                             struct{}{},
		`ResPageTagTapped`:                                              struct{}{},
		`SDKPaymentMethodInvalidBinEntered`:                             struct{}{},
		`notification_centre_edit_phone_tap`:                            struct{}{},
		`ownership-transfer/accepted-by-govid/rate-card-declined`:       struct{}{},
		`restaurant_coordinates_updation_via_worker`:                    struct{}{},
		`digital_contract_create_sign_open_error`:                       struct{}{},
		`O2CrystalRiderCoordinatesImpression`:                           struct{}{},
		`O2PostOrderCartConfirmAddressChangeButtonTapped`:               struct{}{},
		`ResClosingDialogButtonTapped`:                                  struct{}{},
		`O2CrystalTimelineTapEvent`:                                     struct{}{},
		`le-transfer/contract-form-back-click`:                          struct{}{},
		`mx_dining_web_story_publish_failed_okay_try_again_clicked`:     struct{}{},
		`PROMO_USER_APPLY_ERROR_LOG`:                                    struct{}{},
		`RedPaymentPageLoaded`:                                          struct{}{},
		`ad_campaign_edit_budget_tap`:                                   struct{}{},
		`GenericWebviewCTA-healthy_v2_info_page`:                        struct{}{},
		`inventory_search_tap`:                                          struct{}{},
		`O2CrystalExplorerOfferBottomsheetImpressionEvent`:              struct{}{},
		`ProActivationCodeApplyTap`:                                     struct{}{},
		`O2CrystalStarSportsCarouselTapped`:                             struct{}{},
		`O2CrystalBlinkitCokeCarouselTapped`:                            struct{}{},
		`O2CrystalGiftCardCarouselTapped`:                               struct{}{},
		`O2CrystalVideoPlayPauseTap`:                                    struct{}{},
		`O2CrystalVideoSoundTap`:                                        struct{}{},
		`O2CrystalVideoFullscreenTap`:                                   struct{}{},
		`O2CrystalVideoPlayDuration`:                                    struct{}{},
		`O2CrystalAnywhereOrderShareButtonTapped`:                       struct{}{},
		`O2CrystalAnywhereBannerTapEvent`:                               struct{}{},
		`O2CrystalAnywhereBannerImpressionEvent`:                        struct{}{},
		`O2CrystalAnywhereBanner`:                                       struct{}{},
		`O2CrystalCouponsInSafeAreaEvent`:                               struct{}{},
		`toggle_header_on_map_search`:                                   struct{}{},
		`GoldIntroBenefitsImpression`:                                   struct{}{},
		`aerobar_api_called`:                                            struct{}{},
		`cc_dismiss_clicked`:                                            struct{}{},
		`menu_detail_page_click`:                                        struct{}{},
		`review_action_sheet_engaged`:                                   struct{}{},
		`custom-redirect-facebook`:                                      struct{}{},
		`media_repo_menu_tab_change`:                                    struct{}{},
		`res_dsz_unmapping`:                                             struct{}{},
		`O2CrystalResDetailsImpression`:                                 struct{}{},
		`O2OfferSnackbarTap`:                                            struct{}{},
		`ListingParseCuration2Starts`:                                   struct{}{},
		`mx_diy_onboarding_default_res_timings_creation`:                struct{}{},
		`O2CrystalRiderInfoImpression`:                                  struct{}{},
		`MakeOrderCancelDialog`:                                         struct{}{},
		`TakeawayToggleImpression`:                                      struct{}{},
		`PhotoDelete`:                                                   struct{}{},
		`api_ssl_error`:                                                 struct{}{},
		`catalogue_header_delete_click`:                                 struct{}{},
		`instant_tracking_edt`:                                          struct{}{},
		`menu_service_serviceability_rpc_tracking`:                      struct{}{},
		`image_rejected_tag_clicked`:                                    struct{}{},
		`cuisine-change-confirm`:                                        struct{}{},
		`payments_token_call_success`:                                   struct{}{},
		`le-transfer/contract-form-impression`:                          struct{}{},
		`ResPageBookSlotTapped`:                                         struct{}{},
		`notification_ticket_dismiss`:                                   struct{}{},
		`CRYSTAL_FALLBACK_REQUEST_LIMIT_REACHED`:                        struct{}{},
		`gchistory_enter`:                                               struct{}{},
		`BrandPageResImpression`:                                        struct{}{},
		`otp_request_status_phone_verification`:                         struct{}{},
		`ad_payment_screen_opened`:                                      struct{}{},
		`ViewMenu`:                                                      struct{}{},
		`outlet_visibility_details`:                                     struct{}{},
		`media_repo_add_new_images_button_click`:                        struct{}{},
		`O2CrystalMapBannerImpression`:                                  struct{}{},
		`DidUpdateLanguageBundle`:                                       struct{}{},
		`O2TipAmountTapped`:                                             struct{}{},
		`PushNotificationClicked`:                                       struct{}{},
		`ad_campaign_history_menu_tap`:                                  struct{}{},
		`ad_postpaid_unavailable_know_more_tap`:                         struct{}{},
		`zomaland_website_banner`:                                       struct{}{},
		`O2CrystalUserActionHeaderImpression`:                           struct{}{},
		`price_parity_button_proceed`:                                   struct{}{},
		`pubnub_message`:                                                struct{}{},
		`REVIEW_DELETION_MAIL_SENT`:                                     struct{}{},
		`InstantAddonsSkipped`:                                          struct{}{},
		`GoogleSigninFailed`:                                            struct{}{},
		`ListingParseCuration2HeadersFiltersParsed`:                     struct{}{},
		`support_time_slot_impression`:                                  struct{}{},
		`O2CrystalExplorerOfferBottomsheetButtonTapEvent`:               struct{}{},
		`restaurant_selection_warning_dialog_action`:                    struct{}{},
		`SDKUpiGpayPaymentCancelled`:                                    struct{}{},
		`ShouldEnablePinningFlagFetchFailed`:                            struct{}{},
		`order_blocker_impression`:                                      struct{}{},
		`SDKAPICallFailure`:                                             struct{}{},
		`GoldMembershipFAQImpression`:                                   struct{}{},
		`O2MenuPORImpression`:                                           struct{}{},
		`gold_special_cart_pay_success`:                                 struct{}{},
		`ownership-transfer/create-ticket`:                              struct{}{},
		`start_service_error`:                                           struct{}{},
		`appsTracking_v2`:                                               struct{}{},
		`JumboEnameO2DropPinSensor`:                                     struct{}{},
		`language_settings_dropdown_tap`:                                struct{}{},
		`ZTRENDS_PAGE_LOADED`:                                           struct{}{},
		`deeplink_tr`:                                                   struct{}{},
		`LocationOFSEReceiverDetailsRemoved`:                            struct{}{},
		`ThirdPartyAPIs\NSDL\NSDLApiCaller_success`:                     struct{}{},
		`item_serving_info_change`:                                      struct{}{},
		`redirected_to_new_city_page`:                                   struct{}{},
		`review_write_toggle_tap`:                                       struct{}{},
		`visit_youtube_channel_click`:                                   struct{}{},
		`GenericWebviewCTA-dining_gold_upgrade_oldapp`:                  struct{}{},
		`GoldBottomSheetAddAddressImpression`:                           struct{}{},
		`GoldSharePageViewed`:                                           struct{}{},
		`ResBuffetItemClicked`:                                          struct{}{},
		`hp_notification_create`:                                        struct{}{},
		`kpt_minus_click`:                                               struct{}{},
		`reviews_send_reply_tapped`:                                     struct{}{},
		`cuisine-change-success`:                                        struct{}{},
		`RestaurantPagePhotoOpened`:                                     struct{}{},
		`mx_bot_flow_issue_describe`:                                    struct{}{},
		`zfb`:                                                           struct{}{},
		`AppUpdate`:                                                     struct{}{},
		`Backend_opened_app`:                                            struct{}{},
		`GoldDefaultMilestonePopupImpression`:                           struct{}{},
		`SDKUpiBackPressed`:                                             struct{}{},
		`ProPlanPageSwitched`:                                           struct{}{},
		`orderStatusDelayData`:                                          struct{}{},
		`mx_rush_hour_mode_deactivation_clicked`:                        struct{}{},
		`karma_worker_exit`:                                             struct{}{},
		`insights_order_ready_tap`:                                      struct{}{},
		`active_order_error_view_seen`:                                  struct{}{},
		`ResReferralPageImpression`:                                     struct{}{},
		`review_web_filter_applied`:                                     struct{}{},
		`mqtt_connect_fail`:                                             struct{}{},
		`O2CartOptOutBillImpression`:                                    struct{}{},
		`O2CartOptOutBillTap`:                                           struct{}{},
		`PromoPagePromoCodeClicked`:                                     struct{}{},
		`google_click`:                                                  struct{}{},
		`LocationNewUserAddAddressTapped`:                               struct{}{},
		`custom_offers_data_submit_error`:                               struct{}{},
		`error-add-dispute-review`:                                      struct{}{},
		`review_impression`:                                             struct{}{},
		`ownership-transfer/accepted-by-govid/hp-success`:               struct{}{},
		`SDKAddCardSubmitTapped`:                                        struct{}{},
		`ResPageSlotOfferSectionTapped`:                                 struct{}{},
		`AnimatedSplashNavigationEvent`:                                 struct{}{},
		`new_cart_failed_response`:                                      struct{}{},
		`SDKRemoveWalletfailure`:                                        struct{}{},
		`logstore_api_request_meta`:                                     struct{}{},
		`mx_segment_remove_entity`:                                      struct{}{},
		`rider_tickets_events`:                                          struct{}{},
		`mx_dining_web_get_stories_api_request_code_break`:              struct{}{},
		`SSLHandshakeFailure`:                                           struct{}{},
		`ad_campaign_details_expand_tap`:                                struct{}{},
		`cart_service_logs`:                                             struct{}{},
		`menu_delete_sub_category_confirm_button`:                       struct{}{},
		`notification_ticket_impression`:                                struct{}{},
		`reporting_filter_applied`:                                      struct{}{},
		`mx_dining_web_insights_revenue_and_transaction_trend_api_success_failure`: struct{}{},
		`delivery_charge_key`:                                        struct{}{},
		`mx_dining_web_get_transformation_progress_failed`:           struct{}{},
		`mx_dining_web_menu_delete_menu_api_request_failure`:         struct{}{},
		`GoldIntermediateMilestoneReachedCrystalPopupImpression`:     struct{}{},
		`SideMenuButtonTapped`:                                       struct{}{},
		`deeplink_login_consent`:                                     struct{}{},
		`gold_special_cart_polling_finished`:                         struct{}{},
		`phone_login_multi_res_mapped`:                               struct{}{},
		`mweb_amp_open_app_review_modal_resinfo_page_continue_click`: struct{}{},
		`mx_dining_web_events_create_event_upload_media_success`:     struct{}{},
		`O2MenuFabItemClicked`:                                       struct{}{},
		`O2PhoneVerficationStart`:                                    struct{}{},
		`O2TrendingBrandsTileImpression`:                             struct{}{},
		`fssai_update_tap`:                                           struct{}{},
		`menu_meat_type_tag_selected_tap`:                            struct{}{},
		`allowpushnotificationpermission_deny`:                       struct{}{},
		`ownership-transfer/is-loan-account`:                         struct{}{},
		`everyday_print_button_clicked`:                              struct{}{},
		`review_bar_post_cancelled`:                                  struct{}{},
		`SDKEnterOTPResendTapped`:                                    struct{}{},
		`SharedAddressCurrentLocationTapped`:                         struct{}{},
		`O2TipShown`:                                                 struct{}{},
		`ZPLButtonLoaderTapped`:                                      struct{}{},
		`ad_campaign_create_tab`:                                     struct{}{},
		`auth_renew_token_init`:                                      struct{}{},
		`appsflyer_deeplink_error`:                                   struct{}{},
		`promo_confirm_tap`:                                          struct{}{},
		`update_gstin_tap`:                                           struct{}{},
		`image_download_qc_button`:                                   struct{}{},
		`ownership-transfer/otp-verification-failed`:                 struct{}{},
		`veg_prep_remove_tag_popup`:                                  struct{}{},
		`O2CrystalHOZVideoTapEvent`:                                  struct{}{},
		`instruction_accept_confirm_click`:                           struct{}{},
		`fssai_license_error`:                                        struct{}{},
		`menu_item_image_expand_collapse_tap`:                        struct{}{},
		`send_otp_to_new_phone`:                                      struct{}{},
		`deeplink_stories`:                                           struct{}{},
		`MicImpression`:                                              struct{}{},
		`REVIEW_DRAFT_MIGRATION_EVENT`:                               struct{}{},
		`make_order_ndg_applicable`:                                  struct{}{},
		`aerocard_added`:                                             struct{}{},
		`O2CrystalExplorerOfferClaimButtonTapEvent`:                  struct{}{},
		`MakeOrderCallFailed`:                                        struct{}{},
		`EVENT_TICKET_INVOICE_SENT`:                                  struct{}{},
		`O2MenuNDGImpression`:                                        struct{}{},
		`O2MenuSubtabItemImpression`:                                 struct{}{},
		`gold_special_cart_pro_added`:                                struct{}{},
		`menu_save_timings_tap`:                                      struct{}{},
		`image_qc_moderation_action`:                                 struct{}{},
		`O2MenuFabOnboardingButtonTap`:                               struct{}{},
		`gstin_status_refresh_tap`:                                   struct{}{},
		`gold_special_cart_feeding_india_tapped`:                     struct{}{},
		`send_contract_click`:                                        struct{}{},
		`SDKPaymentMethodDetailsPhoneNumberEntered`:                  struct{}{},
		`AppMissingLocalization`:                                     struct{}{},
		`deeplink_bookingdetail`:                                     struct{}{},
		`deeplink_qr`:                                                struct{}{},
		`O2CrystalRiderUpdateAudioInstructionImpression`:             struct{}{},
		`ad_campaign_retry_payment`:                                  struct{}{},
		`insights_kpt_learn_more_tap`:                                struct{}{},
		`ownership-transfer/empty-zipcode`:                           struct{}{},
		`O2ChangeAddressMenuClickNewAddress`:                         struct{}{},
		`O2CrystalNDGOrderGeofenceBanner`:                            struct{}{},
		`DealsResBannerTapped`:                                       struct{}{},
		`ADD_TOKEN`:                                                  struct{}{},
		`ListingParseParsingStarts`:                                  struct{}{},
		`ResAwardsWinnerSectionSwitchTapped`:                         struct{}{},
		`O2FreebieSnackbarImpression`:                                struct{}{},
		`send_otp_to_new_email`:                                      struct{}{},
		`UnregisteredNotificationChannelId`:                          struct{}{},
		`O2AddCardCallSuccess`:                                       struct{}{},
		`everyday_food_inventory_alert_clicked`:                      struct{}{},
		`O2FreebieAdded`:                                             struct{}{},
		`LocationPermissionGeoCodeAPIFailed`:                         struct{}{},
		`O2AddMorePaymentMethodClicked`:                              struct{}{},
		`ad_are_you_sure_popup_impression`:                           struct{}{},
		`mqtt_connection_lost`:                                       struct{}{},
		`soa_feedback_opened`:                                        struct{}{},
		`rating_tapped_modal`:                                        struct{}{},
		`EmailOTPPageLoaded`:                                         struct{}{},
		`ReviewPageBackButtonTapped`:                                 struct{}{},
		`SDKInitCallError`:                                           struct{}{},
		`SDKVSCDisableDeclined`:                                      struct{}{},
		`app_background`:                                             struct{}{},
		`gold_special_cart_promo_removed`:                            struct{}{},
		`menu_score_know_more_continue_tap`:                          struct{}{},
		`order_alert_mute_click`:                                     struct{}{},
		`past_orders_filter_tap`:                                     struct{}{},
		`AODAlarmReceived`:                                           struct{}{},
		`AppLaunchAddressChange`:                                     struct{}{},
		`LocationHomePromptImpression`:                               struct{}{},
		`ad_campaign_details_performance_tap`:                        struct{}{},
		`PUMenuViewed`:                                               struct{}{},
		`res_delivery_menu_category`:                                 struct{}{},
		`logstore_dashboard_launch`:                                  struct{}{},
		`webview-dining-venue-picker-recommend-recommend-res-ids-api-success`: struct{}{},
		`new_web_performance_metric-1`:                                        struct{}{},
		`MemoryMetrics`:                                                       struct{}{},
		`O2CrystalAddItemDelayBanner`:                                         struct{}{},
		`user_res_count_z4`:                                                   struct{}{},
		`order-timeline-tap`:                                                  struct{}{},
		`outlet_info_contact_details_search_input`:                            struct{}{},
		`viewedPhoneGoldSpecialTxnHisDash`:                                    struct{}{},
		`gcorder_shown`:                                                       struct{}{},
		`preferred-pos-collapse-click`:                                        struct{}{},
		`rejection_reason_trends_bar_impression`:                              struct{}{},
		`deeplink_sneakpeek`:                                                  struct{}{},
		`O2PaymentMethodDetailsNickNameEntered`:                               struct{}{},
		`OSM_ORDER_HISTORY_RESPONSE`:                                          struct{}{},
		`contact_details_link_warn_confirm_tap`:                               struct{}{},
		`mx_dining_web_payout_get_rate_card_api_request`:                      struct{}{},
		`O2CrystalResCallImpression`:                                          struct{}{},
		`LoginConsentPageOpen`:                                                struct{}{},
		`O2MenuFilterChecked`:                                                 struct{}{},
		`PromoPagePromoDetailsClicked`:                                        struct{}{},
		`merchant_order_service_calls`:                                        struct{}{},
		`detail_page_close`:                                                   struct{}{},
		`GenericWebviewCTA-menu_max_safety`:                                   struct{}{},
		`dos_reported`:                                                        struct{}{},
		`O2CrystalPreOrderBannerImpression`:                                   struct{}{},
		`AppleMapsDeeplink`:                                                   struct{}{},
		`OAuthValidationFail`:                                                 struct{}{},
		`gold_special_cart_pills_tapped`:                                      struct{}{},
		`menu_bulk_charges_button`:                                            struct{}{},
		`custom-redirect-anjanasreekanth`:                                     struct{}{},
		`support_carousel_click`:                                              struct{}{},
		`ResReferralDefaultErrorPage`:                                         struct{}{},
		`AppPageFirstVisibleCalled`:                                           struct{}{},
		`all_reviews_time_spent`:                                              struct{}{},
		`PhotoModerationDashboardModerateMultiplePhotos`:                      struct{}{},
		`mweb_dom_painted9659653`:                                             struct{}{},
		`phone_res_search_success`:                                            struct{}{},
		`AddressSharingSnippetDismissTapped`:                                  struct{}{},
		`ReviewPageTagTapped`:                                                 struct{}{},
		`enhanced_fi_donation`:                                                struct{}{},
		`O2ExtrasPaymentMethodDefault`:                                        struct{}{},
		`mx_dining_web_transaction_load_more_clicked`:                         struct{}{},
		`cc_expiring_popup_clicked`:                                           struct{}{},
		`res_delivery_selected_address`:                                       struct{}{},
		`review_dispute_remove_button_tapped`:                                 struct{}{},
		`ResOtherOutletsResImpression`:                                        struct{}{},
		`SDKPaymentOptionsOpenCardAddition`:                                   struct{}{},
		`GamificationVideoFailedToDownloadOnLobby`:                            struct{}{},
		`O2CrystalViewOrderDetails`:                                           struct{}{},
		`se_ticket_submit_clicked`:                                            struct{}{},
		`Backend_O2CartPromoTapped`:                                           struct{}{},
		`LanguageButtonRedDotShown`:                                           struct{}{},
		`PLAY_INTEGRITY_ERROR`:                                                struct{}{},
		`RemoveGeofenceSuccess`:                                               struct{}{},
		`custom_offers_action_button_tap`:                                     struct{}{},
		`ResLocationCopied`:                                                   struct{}{},
		`ad_campaign_video_pause_tap`:                                         struct{}{},
		`O2CrystalDropInstructionsTapped`:                                     struct{}{},
		`mweb_amp_open_app_modal_v2_continue_click`:                           struct{}{},
		`ndg_edt_breach`:                                                      struct{}{},
		`SDKPaymentOptionsShrinkSection`:                                      struct{}{},
		`logstore_perma_link_click`:                                           struct{}{},
		`O2CrystalCallButtonTappedV15`:                                        struct{}{},
		`chat_support_closed`:                                                 struct{}{},
		`rejection_policy_v2`:                                                 struct{}{},
		`order_summary_mail_success`:                                          struct{}{},
		`O2CrystalIntercityTopBannerImpression`:                               struct{}{},
		`LocationAddressScreenImpression`:                                     struct{}{},
		`PWA_PAGES`:                                                           struct{}{},
		`track_offers_filter_tap`:                                             struct{}{},
		`O2PaymentSelectionPageShown`:                                         struct{}{},
		`PUMenuPromoBarImpression`:                                            struct{}{},
		`GenericWebviewCTA-ROI_Web_20230312`:                                  struct{}{},
		`MakeOrderCallSuccess`:                                                struct{}{},
		`GoldMembershipDeviceLimitingBannerImpression`:                        struct{}{},
		`ListingsSnippetViewMoreTapped`:                                       struct{}{},
		`SubTabSelected`:                                                      struct{}{},
		`dc_latency`:                                                          struct{}{},
		`image_qc_log_modal_open`:                                             struct{}{},
		`menu_add_modifier_button`:                                            struct{}{},
		`mx_reporting_filter_apply`:                                           struct{}{},
		`SDKPaymentMethodDetailsContinueTapped`:                               struct{}{},
		`ownership-transfer/send-contract/create-new-merchant`:                struct{}{},
		`AppleLoginDisconnect`:                                                struct{}{},
		`O2CrystalAwardsBannerImpressionEvent`:                                struct{}{},
		`ZDeepLinkHandlerDeepLink`:                                            struct{}{},
		`LocationMapScreenGPSPromptImpression`:                                struct{}{},
		`PackagesCartModifyQuantity`:                                          struct{}{},
		`ZPLMatchScreenLoaded`:                                                struct{}{},
		`deeplink_generic_tabs`:                                               struct{}{},
		`res_cuisines_update`:                                                 struct{}{},
		`OtpFail`:                                                             struct{}{},
		`redeem_deal_pos.php`:                                                 struct{}{},
		`AddDeviceCallStarted`:                                                struct{}{},
		`PromoPagePromoTyped`:                                                 struct{}{},
		`O2AddPaymentMethodsLoaded`:                                           struct{}{},
		`one-support-chat-payload-token-set`:                                  struct{}{},
		`customer_details_share_tap`:                                          struct{}{},
		`feedback_collapse_section_button_tapped`:                             struct{}{},
		`voice-instructions-time-limit-consumed`:                              struct{}{},
		`MWEB_SEARCH_RESULTS_BANNER_CLICK`:                                    struct{}{},
		`ReviewPageTagXTapped`:                                                struct{}{},
		`CRYSTAL_RECONNECT_CLIENT`:                                            struct{}{},
		`GOLD_ENTRY_POINT_CLICKED`:                                            struct{}{},
		`res_dc_logs`:                                                         struct{}{},
		`collection_save`:                                                     struct{}{},
		`view_transactions_tap`:                                               struct{}{},
		`mx_fssai_aerobar_display`:                                            struct{}{},
		`sneak_peek_video_editorial_auto_pause`:                               struct{}{},
		`O2CrystalRateAppImpression`:                                          struct{}{},
		`O2CartOfferItemSelectionSnippetTap`:                                  struct{}{},
		`review_filter_pill_deselected`:                                       struct{}{},
		`EditorialReviewBackButtonPress`:                                      struct{}{},
		`feedback_add_photo_button_tapped`:                                    struct{}{},
		`O2AddAddressCompleteAddressDone`:                                     struct{}{},
		`add_subcategory_cancel`:                                              struct{}{},
		`variant_deleted_impression`:                                          struct{}{},
		`rider-location-call-tap`:                                             struct{}{},
		`welcome_login_tap`:                                                   struct{}{},
		`O2MenuVideoHeaderAction`:                                             struct{}{},
		`O2MenuSimilarResBannerTap`:                                           struct{}{},
		`O2CartTipSectionImpressionEvent`:                                     struct{}{},
		`DishAddButtonTapped`:                                                 struct{}{},
		`GoldMembershipTnCImpression`:                                         struct{}{},
		`TagInitializeException`:                                              struct{}{},
		`PackagesTCSViewed`:                                                   struct{}{},
		`rider-location-tap`:                                                  struct{}{},
		`welcome_terms_tap`:                                                   struct{}{},
		`DateChanged`:                                                         struct{}{},
		`SDKUPIApps`:                                                          struct{}{},
		`O2MenuSeeMoreItemsTapped`:                                            struct{}{},
		`GenericWebviewCTA-gold_old_nonmember_emerging`:                       struct{}{},
		`OTOFStatusOnOrderPlaced`:                                             struct{}{},
		`cc_give_refund_clicked`:                                              struct{}{},
		`know_more_tap`:                                                       struct{}{},
		`payments_token_saved`:                                                struct{}{},
		`promo_edit_tap`:                                                      struct{}{},
		`vendor_api_failure_12_51`:                                            struct{}{},
		`SDKADCBSubmitClicked`:                                                struct{}{},
		`ads_exp_mealtime_irrelevance`:                                        struct{}{},
		`CartPaymentFailureViewButtonImpression`:                              struct{}{},
		`notification_centre_edit_email_tap`:                                  struct{}{},
		`ad_payment_cancel_popup_yes_tap`:                                     struct{}{},
		`youtube_video_play_tap`:                                              struct{}{},
		`print_requested_to_desktop_app`:                                      struct{}{},
		`o2_new_place_search`:                                                 struct{}{},
		`contact_details_edit_email_success`:                                  struct{}{},
		`reject_oos_screen_close_tap`:                                         struct{}{},
		`O2PopularCuisinesTileImpression`:                                     struct{}{},
		`O2OrderSummaryDownloadReceiptButtonTappedV2`:                         struct{}{},
		`Pan_Gst_Api_Called`:                                                  struct{}{},
		`dp_order_tile_text_impression`:                                       struct{}{},
		`deeplink_medio`:                                                      struct{}{},
		`zomato_call_helpline_tap`:                                            struct{}{},
		`mx_rush_hour_mode_close_clicked`:                                     struct{}{},
		`zfbMobileWhatsappStartHere`:                                          struct{}{},
		`FeedPostImpression`:                                                  struct{}{},
		`order_alert_app_open`:                                                struct{}{},
		`aerobar_order_impression`:                                            struct{}{},
		`multi-package-backward-flow-upgrade-package-click`:                   struct{}{},
		`owner_hub_tour_start`:                                                struct{}{},
		`order_delivered_tap`:                                                 struct{}{},
		`O2OfferItemSelectionSheetUpdated`:                                    struct{}{},
		`collection_res_impression`:                                           struct{}{},
		`header_get_app_link_click`:                                           struct{}{},
		`microphone-permission-unavailable`:                                   struct{}{},
		`non-upgrade-flow-redirect`:                                           struct{}{},
		`location_diy_open_google_maps_tap`:                                   struct{}{},
		`HomeVideoImpression`:                                                 struct{}{},
		`OAuthFail`:                                                           struct{}{},
		`failed-poi-geocode-optimization`:                                     struct{}{},
		`mqtt_topic_subscribe_success`:                                        struct{}{},
		`review_comment_updated`:                                              struct{}{},
		`tour_callback_strip_dismiss`:                                         struct{}{},
		`JumboEnameO2ChangeAddressMenuClickNewAddress`:                        struct{}{},
		`gold_special_res_page_card_tap`:                                      struct{}{},
		`user-address-confidence-dump`:                                        struct{}{},
		`deeplink_salt`:                                                       struct{}{},
		`pro_selling_snippet_seen`:                                            struct{}{},
		`ring_in_silent_toggle`:                                               struct{}{},
		`contact_details_link_alert_dismiss_tap`:                              struct{}{},
		`O2MenuCustomizationViewMoreTapped`:                                   struct{}{},
		`ci_customer_pic_tap`:                                                 struct{}{},
		`whatsapp_consent_tracking`:                                           struct{}{},
		`promo_cancel_confirm_tap`:                                            struct{}{},
		`hp_fcm_msg_clicked`:                                                  struct{}{},
		`zfb_advanced_filters_expanded`:                                       struct{}{},
		`menu_add_timings_submit`:                                             struct{}{},
		`PUMenuCustomizationViewMoreTapped`:                                   struct{}{},
		`add_variant_item`:                                                    struct{}{},
		`menu_z_gallery_map_tap`:                                              struct{}{},
		`mx_discarded_order_details_fetch`:                                    struct{}{},
		`res_offline_homescreen_go_online_tap`:                                struct{}{},
		`merchant_new_soa_data`:                                               struct{}{},
		`se_unhappy_with_am_clicked`:                                          struct{}{},
		`fb_login`:                                                            struct{}{},
		`cache_cleared_on_auto_logout`:                                        struct{}{},
		`email_signup_initiate`:                                               struct{}{},
		`vendor_api_failure_12_243`:                                           struct{}{},
		`3dtouch_orderonline`:                                                 struct{}{},
		`mac_final_action_tap`:                                                struct{}{},
		`O2CartCheckoutActionClicked`:                                         struct{}{},
		`invite_link_clicked`:                                                 struct{}{},
		`valentines-dining-2023`:                                              struct{}{},
		`O2CartDuplicateOrderPopupMakeButtonTapped`:                           struct{}{},
		`O2RunnrTipCustomTapped`:                                              struct{}{},
		`increase_volume_clicked`:                                             struct{}{},
		`pay_later_polling_response`:                                          struct{}{},
		`ResPageCallButtonTapped`:                                             struct{}{},
		`web_universal_search_query-0`:                                        struct{}{},
		`SDKAddCardNumberEntered`:                                             struct{}{},
		`ad_campaign_budget_go_back_dialog_yes_tap`:                           struct{}{},
		`call_customer_click`:                                                 struct{}{},
		`free_dc_logs`:                                                        struct{}{},
		`location_refreshed_after_timeout_success`:                            struct{}{},
		`slow_cart`:                                                           struct{}{},
		`update_gstin_screen_impression`:                                      struct{}{},
		`review_dispute_sucessful`:                                            struct{}{},
		`O2CrystalRiderDifferentlyAbledComponentImpressionEvent`:              struct{}{},
		`PackagesStatusPopupLoaded`:                                           struct{}{},
		`single_collection_click_action`:                                      struct{}{},
		`ResAwardsInactiveCitiesBannerViewed`:                                 struct{}{},
		`voice-instructions-played`:                                           struct{}{},
		`zfbMobileMappingContinueClick`:                                       struct{}{},
		`GoldMembershipPageSavingsImpression`:                                 struct{}{},
		`IosLiveActivityTerminalStateSuccessEvent`:                            struct{}{},
		`add_saved_cart_to_aerobar`:                                           struct{}{},
		`compress_photo_tap`:                                                  struct{}{},
		`api_login_intent`:                                                    struct{}{},
		`menu_copy_catalogue_button`:                                          struct{}{},
		`res_offline_toggle_tap`:                                              struct{}{},
		`chng_commission_view_details`:                                        struct{}{},
		`learning_centre_tap`:                                                 struct{}{},
		`mx_dining_web_delete_story_api_request_success`:                      struct{}{},
		`instruction_dismiss`:                                                 struct{}{},
		`O2CrystalCovidMerchantWHOImpression`:                                 struct{}{},
		`menu_inline_add_item_button`:                                         struct{}{},
		`O2CrystalExplorerOfferOrderNowButtonTapEvent`:                        struct{}{},
		`troubleshoot_item_tap`:                                               struct{}{},
		`SDKRecacheFailed`:                                                    struct{}{},
		`invalid-size-top-dish-image`:                                         struct{}{},
		`O2DeliveryInstructionAudioInstructionStartTapped`:                    struct{}{},
		`ViewYourRewardsClicked`:                                              struct{}{},
		`FailedUpdateGameWorkerJob`:                                           struct{}{},
		`mac_popup_impression`:                                                struct{}{},
		`prefered-pos-main-api-success`:                                       struct{}{},
		`ci_filter_apply_tap`:                                                 struct{}{},
		`O2PaymentMethodDetailsEmailTapped`:                                   struct{}{},
		`bottom_tabs_fallback`:                                                struct{}{},
		`growth-package-fetch-res-data`:                                       struct{}{},
		`SDKPromoPageBottomSheetDismissed`:                                    struct{}{},
		`GoldBottomSheetAddGoldTapped`:                                        struct{}{},
		`ThirdPartyAPIs\KnowYourGst\KnowYourGstApiCaller_success`:             struct{}{},
		`ZPLLobbyLoaded`:                                                      struct{}{},
		`aerobar_right_action`:                                                struct{}{},
		`JumboEnameO2TaxesAndChargesPopUpShown`:                               struct{}{},
		`O2_HOME_RENDEREDV2`:                                                  struct{}{},
		`reopen_ticket_click`:                                                 struct{}{},
		`ProActivationFailure`:                                                struct{}{},
		`kpt_add_click`:                                                       struct{}{},
		`map_existing_items_click`:                                            struct{}{},
		`phone_accessibility_settings`:                                        struct{}{},
		`PollingPageBackConfirmed`:                                            struct{}{},
		`O2CrystalDiningEventBannerImpression`:                                struct{}{},
		`delivered_orders_cards_impression`:                                   struct{}{},
		`digital_contract_login_page_open`:                                    struct{}{},
		`hp_login_confirm_tap`:                                                struct{}{},
		`order_support_click`:                                                 struct{}{},
		`menu_score_know_more_close_tap`:                                      struct{}{},
		`expand_additional_rider_image_tap`:                                   struct{}{},
		`ownership_transfer_delete`:                                           struct{}{},
		`mweb_open_app_modal_v2_impression`:                                   struct{}{},
		`Earliest Promise TimeWidget V2 Shown`:                                struct{}{},
		`HomeSetPreciseLocationActionClicked`:                                 struct{}{},
		`edition_card_tap`:                                                    struct{}{},
		`item_on_hold_details_tap`:                                            struct{}{},
		`ZPLPredictionSnippetViewed`:                                          struct{}{},
		`chat_support_opened`:                                                 struct{}{},
		`image_loading_failed`:                                                struct{}{},
		`ad_campaign_renew_impression`:                                        struct{}{},
		`notification_contact_details_dismiss`:                                struct{}{},
		`zpp_welcome_offer`:                                                   struct{}{},
		`logstore_fields_search_usage`:                                        struct{}{},
		`promo_cancel_tap`:                                                    struct{}{},
		`customer_complaint_item_impression`:                                  struct{}{},
		`cx_complaint_action`:                                                 struct{}{},
		`menu_item_autosuggestion_selected`:                                   struct{}{},
		`TRBookingHistoryImpression`:                                          struct{}{},
		`click`:                                                               struct{}{},
		`delete-menu-item-click`:                                              struct{}{},
		`email_otp_request_is_successful`:                                     struct{}{},
		`logout-click`:                                                        struct{}{},
		`toggle_flash_tap`:                                                    struct{}{},
		`zpay_status_page_pro_details_tap`:                                    struct{}{},
		`Static Banner Shown`:                                                 struct{}{},
		`sendEmailPrefLink`:                                                   struct{}{},
		`LocationMapScreenNextTapped`:                                         struct{}{},
		`SDKAddWalletFailure`:                                                 struct{}{},
		`GPSLocationFetchedFailed`:                                            struct{}{},
		`PARSING_NETWORK_EXCEPTION`:                                           struct{}{},
		`mweb_support_feedback`:                                               struct{}{},
		`order_alert_unmute_click`:                                            struct{}{},
		`menu_bulk_tags_button`:                                               struct{}{},
		`ad_campaign_faq_menu_tap`:                                            struct{}{},
		`ownership-transfer/user-eligible`:                                    struct{}{},
		`mark_all_items_ready`:                                                struct{}{},
		`ownership-transfer/accepted-by-govid/cbs-error`:                      struct{}{},
		`gold_plan_page_load_success`:                                         struct{}{},
		`O2CrystalOFSEBannerImpression`:                                       struct{}{},
		`ChannelIdNotAssigned`:                                                struct{}{},
		`O2MenuMoreInfoTapped`:                                                struct{}{},
		`deeplink_apple-map-host`:                                             struct{}{},
		`email_login_success`:                                                 struct{}{},
		`mx-newOrder-rendered`:                                                struct{}{},
		`DiningMenuFilterChecked`:                                             struct{}{},
		`search_links_click_action`:                                           struct{}{},
		`logstore_filter_on_query_fields_tap`:                                 struct{}{},
		`GDCPaymentSuccess`:                                                   struct{}{},
		`buy_gift_card_view_code_clicked`:                                     struct{}{},
		`PUMenuCollapsibleButtonTapped`:                                       struct{}{},
		`GoldIntroNotifyMeButtonImpression`:                                   struct{}{},
		`QuizhourLottieImpression`:                                            struct{}{},
		`edition_tsp_api`:                                                     struct{}{},
		`hp_logout`:                                                           struct{}{},
		`is_dont_keep_activities_enabled`:                                     struct{}{},
		`ResSeeAllTapped`:                                                     struct{}{},
		`GenericWebviewCTA-gold_old_app_non_member`:                           struct{}{},
		`order_notif_disable_confirm`:                                         struct{}{},
		`ListingsDidFail`:                                                     struct{}{},
		`add_more_variant_tap`:                                                struct{}{},
		`cuisine_rejected_tag_clicked`:                                        struct{}{},
		`O2CrystalDIYSupportRiderCallImpression`:                              struct{}{},
		`O2CrystalOrderSharingRiderSnippet`:                                   struct{}{},
		`OTP_autofill`:                                                        struct{}{},
		`menu_search_input`:                                                   struct{}{},
		`delivered_order_trends_bar_impression`:                               struct{}{},
		`se_am_phone_tapped`:                                                  struct{}{},
		`res_page_similiar_restaurant_tapped`:                                 struct{}{},
		`SDKNativeOtpFailure`:                                                 struct{}{},
		`SharedAddressTagsFieldTapped`:                                        struct{}{},
		`Progress_Bar_Click`:                                                  struct{}{},
		`mx_order_history_web`:                                                struct{}{},
		`ads_campaign_carry`:                                                  struct{}{},
		`hp_partner_show_onboarding_success`:                                  struct{}{},
		`sr_sa_inactive`:                                                      struct{}{},
		`PackagesRailViewed`:                                                  struct{}{},
		`phone_login_no_res_mapped`:                                           struct{}{},
		`remote-trigger-key-log`:                                              struct{}{},
		`zpay_scratch_card_tapped`:                                            struct{}{},
		`bottom_navigation_bar_v2_select`:                                     struct{}{},
		`1b45da4lO`:                                                           struct{}{},
		`ialogV2Data(popupData=`:                                              struct{}{},
		`EmailManualOTPLoginTapped`:                                           struct{}{},
		`StorySwipeDownClose`:                                                 struct{}{},
		`call_failed_for_order_accepted`:                                      struct{}{},
		`SeeAllOutletsNearbyTapped`:                                           struct{}{},
		`SDKTokenizeFailure`:                                                  struct{}{},
		`SavedAddressDeleteTapped`:                                            struct{}{},
		`O2MenuSubtabItemClicked`:                                             struct{}{},
		`deeplink_af`:                                                         struct{}{},
		`order_history_search_started`:                                        struct{}{},
		`ReferralCouponUnusablePageImpression`:                                struct{}{},
		`ZGDCTopSnippetsImpressionTracking`:                                   struct{}{},
		`O2CrystalDIYRiderHeaderImpression`:                                   struct{}{},
		`delivery_settings_params`:                                            struct{}{},
		`inventory_sub_category_toggle_tap`:                                   struct{}{},
		`order_history_click`:                                                 struct{}{},
		`impression_ads_billing_duration_tap`:                                 struct{}{},
		`PUMenuSearchTapped`:                                                  struct{}{},
		`socket_init`:                                                         struct{}{},
		`O2CrystalStarSportsBannerImpression`:                                 struct{}{},
		`O2CrystalBlinkitCokeImpression`:                                      struct{}{},
		`O2CrystalGiftCardImpression`:                                         struct{}{},
		`O2CrystalVideoImpression`:                                            struct{}{},
		`DeliveryMenuButtonTapped`:                                            struct{}{},
		`GoldO2CartSnippetImpression`:                                         struct{}{},
		`O2RiderFraudCheckEvent`:                                              struct{}{},
		`O2AutoDetectLocationClicked`:                                         struct{}{},
		`ThirdPartyAPIs\PierianPANGST\PierianApiCaller_called`:                struct{}{},
		`ad_campaign_end_date_tap`:                                            struct{}{},
		`bottomsheet_select`:                                                  struct{}{},
		`web_scroll_to_top_button_visible`:                                    struct{}{},
		`mx_dining_web_payout_download_statement_request_past_cycle`:          struct{}{},
		`mx_dining_web_view_transaction_details`:                              struct{}{},
		`order_notif_toggle`:                                                  struct{}{},
		`gcorder_send`:                                                        struct{}{},
		`shareSnapshot`:                                                       struct{}{},
		`new_web_performance_metric'`:                                         struct{}{},
		`start_service`:                                                       struct{}{},
		`mx_dining_web_upload_from_zomato_library`:                            struct{}{},
		`crystalSwitchDismissButtonShown`:                                     struct{}{},
		`O2CrystalSustainabilityComponentImpressionEvent`:                     struct{}{},
		`SDKUpiGpayPaymentSuccess`:                                            struct{}{},
		`EventPageBottomButtonClicked`:                                        struct{}{},
		`GiftCardCTATapped`:                                                   struct{}{},
		`ThirdPartyAPIs\PierianPANGST\PierianApiCaller_success`:               struct{}{},
		`gold_add_plan_button_tapped`:                                         struct{}{},
		`cod_blocked`:                                                         struct{}{},
		`add_gstin_option_save_tap`:                                           struct{}{},
		`vendor_api_failure_12_84`:                                            struct{}{},
		`AerobarImpression`:                                                   struct{}{},
		`menu_z_gallery_tap`:                                                  struct{}{},
		`collection_res_outlet`:                                               struct{}{},
		`O2FreebieRemoved`:                                                    struct{}{},
		`SDKEnterCVVBackTapped`:                                               struct{}{},
		`SDKLinkWalletFailure`:                                                struct{}{},
		`GenericWebviewCTA-legends_express_calendar_mumbai_nv`:                struct{}{},
		`invalid_deeplink_opened`:                                             struct{}{},
		`login_response`:                                                      struct{}{},
		`menu_category_fab_tap`:                                               struct{}{},
		`res_photo_update`:                                                    struct{}{},
		`zpay_status_page_res_info_tap`:                                       struct{}{},
		`CART_RENDERED`:                                                       struct{}{},
		`GenericWebviewPageTracking`:                                          struct{}{},
		`O2MenuItemCarouselTap`:                                               struct{}{},
		`menu_variant_out_of_stock_click`:                                     struct{}{},
		`ViewCartTapped`:                                                      struct{}{},
		`O2CartPageSavings`:                                                   struct{}{},
		`O2KonotorUserHistoryTracking`:                                        struct{}{},
		`order-details-api-failed`:                                            struct{}{},
		`webview-dining-tr-restrictions-open-add-restriction-modal`:           struct{}{},
		`ownership-transfer/update-form-data`:                                 struct{}{},
		`media_repo_apply_map_images_click`:                                   struct{}{},
		`O2CrystalCallButtonTappedV16`:                                        struct{}{},
		`PackagesCounterTapped`:                                               struct{}{},
		`LocationReceiverPhoneBookTapped`:                                     struct{}{},
		`trivia_video_caching`:                                                struct{}{},
		`call_additional_rider_tap`:                                           struct{}{},
		`digital_contract_bank_details_save_fail`:                             struct{}{},
		`PUMenuDropOff`:                                                       struct{}{},
		`instant_store_toggle_click`:                                          struct{}{},
		`MENU_PARSED_OLD`:                                                     struct{}{},
		`ZPLPrevGameStatsSnippetImpression`:                                   struct{}{},
		`ad_tnc_checkbox_click`:                                               struct{}{},
		`EDIT_EMAIL_FLOW_VERIFY_NEW_ACCOUNT_MODAL_IMPRESSION`:                 struct{}{},
		`ZomatoWebPhoneSignupPageTap`:                                         struct{}{},
		`res_cart_continue_clicked`:                                           struct{}{},
		`AllPlacesSearchSuggestionClick`:                                      struct{}{},
		`O2CrystalIntercityTopBannerTapped`:                                   struct{}{},
		`PinningKeyConversionFailure`:                                         struct{}{},
		`StoryImpression`:                                                     struct{}{},
		`O2OFSESendGiftTapped`:                                                struct{}{},
		`duplicate_order_logs`:                                                struct{}{},
		`duplicate_order_logs_v2`:                                             struct{}{},
		`new_order_socket_publish`:                                            struct{}{},
		`mx_fetch_multi_outlet_breakdown`:                                     struct{}{},
		`web_accept_cookie_action`:                                            struct{}{},
		`O2CrystalHeaderSwitcherImpressionEvent`:                              struct{}{},
		`O2CrystalPayLaterImpression`:                                         struct{}{},
		`SDKVerifiedFailLoaded`:                                               struct{}{},
		`EDIT_EMAIL_FLOW_INITIATE_AUTH`:                                       struct{}{},
		`ORDER_HOME_ON_EMPTY_STATE`:                                           struct{}{},
		`api_req_meta_config`:                                                 struct{}{},
		`gdpr_page_tracking`:                                                  struct{}{},
		`search_order_submit`:                                                 struct{}{},
		`le-transfer/request-action`:                                          struct{}{},
		`otp_by_call_submit_tapped`:                                           struct{}{},
		`CRYSTAL_SCREEN_RESPONSIVE`:                                           struct{}{},
		`O2MenuRepeatLastCustomizationLoaded`:                                 struct{}{},
		`allowpushnotificationpermission_display`:                             struct{}{},
		`climacell_weather_data`:                                              struct{}{},
		`is_user_phone_present`:                                               struct{}{},
		`order_clicked_from_error_page`:                                       struct{}{},
		`post_notifications_disabled`:                                         struct{}{},
		`mx_dining_web_menu_click_on_next_pre_menu_guidelines_button`:         struct{}{},
		`SDKRetryRecommendedPaymentOptionShown`:                               struct{}{},
		`O2AddItemMenuAddressBlockerTapEvent`:                                 struct{}{},
		`PayAgainClicked`:                                                     struct{}{},
		`new-order-socket-event`:                                              struct{}{},
		`JumboEnameO2PaymentButtonClicked`:                                    struct{}{},
		`PLAN_PAGE_FULLY_LOADED_TEST`:                                         struct{}{},
		`CartPaymentPageSuccess`:                                              struct{}{},
		`GiftCardPopUpLoaded`:                                                 struct{}{},
		`zPlatformPusherNotifDispatched`:                                      struct{}{},
		`O2LocationSearchStartedTyping`:                                       struct{}{},
		`learn_more_tour_strip_dismiss`:                                       struct{}{},
		`webview-dining-tr-restrictions-add-slot-api-failed`:                  struct{}{},
		`AppFeedbackSubmitted`:                                                struct{}{},
		`O2AddAddressNotDeliveringSubzoneEntered`:                             struct{}{},
		`O2PaymentMethodDetailsPhoneNumberEntered`:                            struct{}{},
		`gold_special_cart_feeding_india_bottom_button_tapped`:                struct{}{},
		`mx_onboarding_callback_requested`:                                    struct{}{},
		`share_feedback`:                                                      struct{}{},
		`Product Added`:                                                       struct{}{},
		`O2CrystalFooterImpression`:                                           struct{}{},
		`menu_item_not_live_know_more_tap`:                                    struct{}{},
		`vendor_api_failure_1_0`:                                              struct{}{},
		`rejection_reason_distribution_impression`:                            struct{}{},
		`GoogleLoginStart`:                                                    struct{}{},
		`add_time_slot_click`:                                                 struct{}{},
		`delivery_menu_tabs_change`:                                           struct{}{},
		`event_menu_add_on_oos_tap`:                                           struct{}{},
		`fcm_token_reg_failed`:                                                struct{}{},
		`delivered_orders_home_impression`:                                    struct{}{},
		`hp_user_tnc_impression`:                                              struct{}{},
		`STATUS_DEADLINE_EXCEEDED`:                                            struct{}{},
		`instruction_confirm`:                                                 struct{}{},
		`O2PreviousOrderTileImpression`:                                       struct{}{},
		`cancel_order_ready_tap`:                                              struct{}{},
		`fssai_doc_download_tap`:                                              struct{}{},
		`menu_booster_pack_purchase_screen_impression`:                        struct{}{},
		`webview-event-details-page-open`:                                     struct{}{},
		`current-location-api-failure`:                                        struct{}{},
		`rate_card_update_event`:                                              struct{}{},
		`mx_dining_web_menu_click_on_menu_guidelines`:                         struct{}{},
		`webview-dining-venue-picker-restaurant-info-api-success`:             struct{}{},
		`rand_page_location_button_clicked`:                                   struct{}{},
		`SDKCancelPaymentFailed`:                                              struct{}{},
		`DeepLinkNotSupported`:                                                struct{}{},
		`O2OFSEDeleteContactTapped`:                                           struct{}{},
		`fetch_using_lat_lng`:                                                 struct{}{},
		`notification_contact_details_impression`:                             struct{}{},
		`preparing_tab_click`:                                                 struct{}{},
		`web_universal_lm_close_with_outside_click`:                           struct{}{},
		`review_bottom_sheet_filter_tapped`:                                   struct{}{},
		`ResBuffetSheetCTAClicked`:                                            struct{}{},
		`voice-instructions-recorded`:                                         struct{}{},
		`DEEPLINK_OPEN_USER_BLOCKER_VIEW`:                                     struct{}{},
		`JumboEnameO2MenuHeartClicked`:                                        struct{}{},
		`O2CrystalContactlessDeliveryMissedTapped`:                            struct{}{},
		`LocationPageViewed`:                                                  struct{}{},
		`SDKPaymentOptionsSelected`:                                           struct{}{},
		`RedWebViewLoadFinished`:                                              struct{}{},
		`O2_invalid_promo_msg`:                                                struct{}{},
		`ds_fee_cart`:                                                         struct{}{},
		`header_investor_relations_click`:                                     struct{}{},
		`ticket_notification_centre_tap`:                                      struct{}{},
		`menu_item_delete_image_tap`:                                          struct{}{},
		`O2OFSECallSenderTapped`:                                              struct{}{},
		`mx_action_move_to_delivery`:                                          struct{}{},
		`zwallet_transaction_click`:                                           struct{}{},
		`ownership-transfer/accepted-by-govid/promo-success`:                  struct{}{},
		`video_ads_billing_duration_tap`:                                      struct{}{},
		`GamificationVideoDownloadSucceeds`:                                   struct{}{},
		`GoldCartPaymentStatus`:                                               struct{}{},
		`inventory_screen_impression`:                                         struct{}{},
		`O2CrystalMapVoiceDirectionTapped`:                                    struct{}{},
		`webview-dining-venue-picker-suggestion-page-load`:                    struct{}{},
		`O2CrystalMapMoved`:                                                   struct{}{},
		`O2MenuResDetailsImpression`:                                          struct{}{},
		`O2MenuAnywhereSnippetImpression`:                                     struct{}{},
		`O2MenuExplorerServed`:                                                struct{}{},
		`O2MenuExplorerImpression`:                                            struct{}{},
		`O2MenuTopSearchesServed`:                                             struct{}{},
		`O2MenuTopSearchesImpression`:                                         struct{}{},
		`O2MenuTopSearchesTap`:                                                struct{}{},
		`StoryMovedBack`:                                                      struct{}{},
		`high_ddt_add_item_pill`:                                              struct{}{},
		`order_timeline_screen_impression`:                                    struct{}{},
		`res_property_tags_click`:                                             struct{}{},
		`SearchResultRendered`:                                                struct{}{},
		`HighlightedReviewsTapped`:                                            struct{}{},
		`digital_contract_auth_sdk_initialise_success`:                        struct{}{},
		`RedHomePlanBannerTapped`:                                             struct{}{},
		`O2FeedingIndiaLearnMoreTapped`:                                       struct{}{},
		`HomeVideoMaxMinTapped`:                                               struct{}{},
		`Order Acknowledged`:                                                  struct{}{},
		`User_Accepted_O2_Contract`:                                           struct{}{},
		`cart_builder_oos_error`:                                              struct{}{},
		`contact_details_add_email`:                                           struct{}{},
		`track_menu_parsing`:                                                  struct{}{},
		`tour_callback_strip_impression`:                                      struct{}{},
		`GOLD_PAGE_VIEWS`:                                                     struct{}{},
		`O2SearchPageRestaurant`:                                              struct{}{},
		`O2CrystalContactlessPromptImpression`:                                struct{}{},
		`LocationMapScreenLocationFieldDidEndTyping`:                          struct{}{},
		`edit_cuisine_tap`:                                                    struct{}{},
		`gccustom_shown`:                                                      struct{}{},
		`order_card_impression`:                                               struct{}{},
		`customer_location_map_click`:                                         struct{}{},
		`mx_dining_web_events_guest_list_click_on_button`:                     struct{}{},
		`gold_plan_page_load_failed`:                                          struct{}{},
		`SDKAppAPIFailure`:                                                    struct{}{},
		`LocationFetchStarted`:                                                struct{}{},
		`ProCartPromoApplied`:                                                 struct{}{},
		`cc_order_details_clicked`:                                            struct{}{},
		`mx_reporting_shown`:                                                  struct{}{},
		`rider_phone_tap`:                                                     struct{}{},
		`tour_dismiss_notification_centre`:                                    struct{}{},
		`print_kot_tap`:                                                       struct{}{},
		`res_delivery_egg_unselect`:                                           struct{}{},
		`O2MenuRepeatLastCustomizationTapped`:                                 struct{}{},
		`KARMA_DEVICE_INFO_SUCCESS`:                                           struct{}{},
		`OrderSummaryPageShown`:                                               struct{}{},
		`menu_item_oos_toggle_pre_dialog_dismiss`:                             struct{}{},
		`rider_pre_assignment_impression`:                                     struct{}{},
		`mweb_amp_open_app_review_modal_resinfo_page_outside_click`:           struct{}{},
		`ListingsLoadedWithError`:                                             struct{}{},
		`login_dialog_shown`:                                                  struct{}{},
		`menu_image_score_upload_timeout`:                                     struct{}{},
		`logstore_error_logs`:                                                 struct{}{},
		`@@cmOAc`:                                                             struct{}{},
		`review_tap`:                                                          struct{}{},
		`HomePageLoaded`:                                                      struct{}{},
		`ZomatoWebAccountLinkPageTap`:                                         struct{}{},
		`post_order_review_screen_disappeared`:                                struct{}{},
		`ProPlanSeeAllRes`:                                                    struct{}{},
		`mx_dining_web_payout_download_statement_request_current_cycle`:       struct{}{},
		`ENAME_FIREBASE_CONFIG_HELPER_INIT`:                                   struct{}{},
		`UserPrefSheetImpression`:                                             struct{}{},
		`O2CrystalFooterNonLogsDeliveredImpression`:                           struct{}{},
		`mx_report_web_vitals`:                                                struct{}{},
		`ticket_alert_socket_event`:                                           struct{}{},
		`vendor_api_failure_12_97`:                                            struct{}{},
		`edition_tsp_root_detection`:                                          struct{}{},
		`microphone-permission-requested`:                                     struct{}{},
		`webview-event-details-native-share-via-error`:                        struct{}{},
		`GoldIntroPageSuccessImpression`:                                      struct{}{},
		`BookTicketsClicked`:                                                  struct{}{},
		`GoldMembershipRenewButtonImpression`:                                 struct{}{},
		`PROMO_SWITCH_POPUP_DISMISSED`:                                        struct{}{},
		`ci_image_expand_click`:                                               struct{}{},
		`O2AddCardScreenOpened`:                                               struct{}{},
		`SDKPaymentsAppAPISuccess`:                                            struct{}{},
		`cc_refund_cancel_clicked`:                                            struct{}{},
		`on_load_modal_acknowledge`:                                           struct{}{},
		`GoldIntroBottomSnippetLostSavingsImpression`:                         struct{}{},
		`moderation_auto_assign_page_load`:                                    struct{}{},
		`mx_dining_web_transaction_search_clicked`:                            struct{}{},
		`digital_contract_bank_details_save_error`:                            struct{}{},
		`AddDeviceCallFailure`:                                                struct{}{},
		`O2CrystalWhatsappOptInSnippetImpression`:                             struct{}{},
		`O2CartSustainabilityContributionPopup`:                               struct{}{},
		`O2CartCancellationPolicyImpression`:                                  struct{}{},
		`mweb_ldcrail_show_more_action`:                                       struct{}{},
		`mx_nps_dismiss_click`:                                                struct{}{},
		`gstin_get_new_gst_details_error`:                                     struct{}{},
		`ownership_transfer_m1_details`:                                       struct{}{},
		`photo_bar_post_success_view`:                                         struct{}{},
		`O2MenuFarawayOutletPopup`:                                            struct{}{},
		`O2CrystalBlinkitScratchCardScratched`:                                struct{}{},
		`O2CrystalZPLPredictWin2023CarouselImpression`:                        struct{}{},
		`O2MenuUnserviceableBannerAction`:                                     struct{}{},
		`best_in_category_menu_items`:                                         struct{}{},
		`rider_goto_map_tapped`:                                               struct{}{},
		`PackagesMenuRepeatButtonTapped`:                                      struct{}{},
		`O2CartSustBannerViewed`:                                              struct{}{},
		`Dev_app_exception`:                                                   struct{}{},
		`SDKAddCardPersonalTapped`:                                            struct{}{},
		`start_now_click`:                                                     struct{}{},
		`ad_campaign_billing_edit_tap`:                                        struct{}{},
		`ReviewPageSubmitTapped`:                                              struct{}{},
		`O2CrystalPipCloseTapEvent`:                                           struct{}{},
		`DirectionTappedEventPage`:                                            struct{}{},
		`deeplink_open`:                                                       struct{}{},
		`res_delivery_offer_imp`:                                              struct{}{},
		`ResReviewsReviewTapped`:                                              struct{}{},
		`O2CartZCreditsTapped`:                                                struct{}{},
		`O2OFSECallbackSupportImpression`:                                     struct{}{},
		`auth_status`:                                                         struct{}{},
		`cookie_added`:                                                        struct{}{},
		`menu_z_gallery_delete_tap`:                                           struct{}{},
		`order_mark_as_favorite_tapped`:                                       struct{}{},
		`zomato_places_dedupe`:                                                struct{}{},
		`submit_bank_details_tap`:                                             struct{}{},
		`PUMenuFetched`:                                                       struct{}{},
		`badges_no_data_banner_viewed`:                                        struct{}{},
		`O2CrystalStarSportsBannerTapped`:                                     struct{}{},
		`O2PaymentMethodDetailsCVVEntered`:                                    struct{}{},
		`TRSlotCartPageChangeOfferTapped`:                                     struct{}{},
		`gift_cart_confirmation_view_impression`:                              struct{}{},
		`ZomatoWebSignupSuccess''`:                                            struct{}{},
		`O2MenuSimilarResRailItemShown`:                                       struct{}{},
		`O2AddAddressDeliveryInstructionTapped`:                               struct{}{},
		`O2RunnrTipAmountTapped`:                                              struct{}{},
		`PackageRemovedFromCart`:                                              struct{}{},
		`ThirdPartyAPIs\NSDL\NSDLApiCaller_error`:                             struct{}{},
		`CPU_DETECT_FAILED`:                                                   struct{}{},
		`using_login_location`:                                                struct{}{},
		`ResTabSwitched`:                                                      struct{}{},
		`mweb_amp_open_app_menu_modal_menu_page_open_click`:                   struct{}{},
		`bookmark_toggle`:                                                     struct{}{},
		`O2SelectLocationClosed`:                                              struct{}{},
		`navbar_image_qc_button`:                                              struct{}{},
		`SDKEnterOTPSubmitTapped`:                                             struct{}{},
		`InvalidLocationEvent`:                                                struct{}{},
		`ad_campaign_budget_back_tap`:                                         struct{}{},
		`ZomatoWebLoginPageLoaded`:                                            struct{}{},
		`mac_action_tap`:                                                      struct{}{},
		`ZomatoWebPhoneSignupPageLoaded`:                                      struct{}{},
		`custom-redirect---sanitized--`:                                       struct{}{},
		`sneak_peak_video_lag`:                                                struct{}{},
		`JumboEnameO2PhoneVerficationStart`:                                   struct{}{},
		`gold_subscription_rail_seen`:                                         struct{}{},
		`TRSlotBookingPageOffersSectionTapped`:                                struct{}{},
		`mqtt`:                                                                struct{}{},
		`otp_page_loaded_phone_verification`:                                  struct{}{},
		`res_off_reason_new_screen_impression`:                                struct{}{},
		`custom-redirect-mamatha`:                                             struct{}{},
		`O2AddButtonTap`:                                                      struct{}{},
		`GlowImpression`:                                                      struct{}{},
		`ON_TAB_SELECTED`:                                                     struct{}{},
		`tableRes`:                                                            struct{}{},
		`mx_dining_web_transaction_refund_btn_clicked`:                        struct{}{},
		`self_delivery_toggle_on_tap`:                                         struct{}{},
		`onb_ad_creation_failed`:                                              struct{}{},
		`LoginConsentGranted`:                                                 struct{}{},
		`StoryClosed`:                                                         struct{}{},
		`menu_variant_in_stock_click`:                                         struct{}{},
		`order_blocker_click_tracking`:                                        struct{}{},
		`statement_of_account_click`:                                          struct{}{},
		`ringtone_option_clicked`:                                             struct{}{},
		`PUMenuFilterChecked`:                                                 struct{}{},
		`restaurant_pickup_coordinates_updation_aerial_distance_rejection`:    struct{}{},
		`ZPayQRScanScreenLoaded`:                                              struct{}{},
		`menu_banner_tap`:                                                     struct{}{},
		`clientDashboardCallButtonTapped`:                                     struct{}{},
		`SignUpFail`:                                                          struct{}{},
		`gift_card_change_payment_method_tapped`:                              struct{}{},
		`O2CrystalOrderDetailsImpression`:                                     struct{}{},
		`O2AddAddressDeliveryInstructionDone`:                                 struct{}{},
		`mx_web_banner_tap`:                                                   struct{}{},
		`crop_fail`:                                                           struct{}{},
		`dashboard_launch`:                                                    struct{}{},
		`CardAdded`:                                                           struct{}{},
		`day_of_week_select`:                                                  struct{}{},
		`gift_card_buy_card_button_impression`:                                struct{}{},
		`O2MenuTopBannerClicked`:                                              struct{}{},
		`gold_history_view_transaction_tap`:                                   struct{}{},
		`logstore_date_time_filter_tap`:                                       struct{}{},
		`O2CrystalCutleryFeedbackButtonTapped`:                                struct{}{},
		`O2CrystalRiderChatButtonImpressionV16`:                               struct{}{},
		`CartConfirmationViewCompleted`:                                       struct{}{},
		`order_history_search_clear`:                                          struct{}{},
		`res_page_see_all_outlets_click`:                                      struct{}{},
		`O2CrystalZvsZVideoBannerImpression`:                                  struct{}{},
		`SDKPollingPageBackDeclined`:                                          struct{}{},
		`BlinkitTagTap`:                                                       struct{}{},
		`deeplink_zpl`:                                                        struct{}{},
		`country_selector_tapped`:                                             struct{}{},
		`menu_subcategory_toggle_stock`:                                       struct{}{},
		`skip_auto_start_click`:                                               struct{}{},
		`menu_booster_pack_purchase_dialog_action_tap`:                        struct{}{},
		`mx_dining_web_menu_click_on_cancel_upload_menu`:                      struct{}{},
		`promo_impressions`:                                                   struct{}{},
		`OpenEmailAppTapped`:                                                  struct{}{},
		`PromoPageLoaded`:                                                     struct{}{},
		`ButtonCardControlApplyChangesTapEvent`:                               struct{}{},
		`event_onboarding_token`:                                              struct{}{},
		`verify_phone_with_otp`:                                               struct{}{},
		`support_date_tap`:                                                    struct{}{},
		`signup_account_link_social`:                                          struct{}{},
		`order_refund_confirm_tap`:                                            struct{}{},
		`gccustom_amt`:                                                        struct{}{},
		`copy_categories_to_restaurants_submit`:                               struct{}{},
		`rain_ddt_blocker_sheet`:                                              struct{}{},
		`res_page_our_sponsors_card_click`:                                    struct{}{},
		`order_summary_mail_failure`:                                          struct{}{},
		`menu_bulk_tags_submit_button`:                                        struct{}{},
		`expiring_complaints_notification_impression`:                         struct{}{},
		`self_delivery_toggle_off_tap`:                                        struct{}{},
		`otp_resend_click`:                                                    struct{}{},
		`TRRestaurantDetailsSectionCopyLocationTapped`:                        struct{}{},
		`Sample-AppResponsivenessMetrics`:                                     struct{}{},
		`mx_dining_web_transaction_list_scrolled`:                             struct{}{},
		`LocationMapScreenTitleEmpty`:                                         struct{}{},
		`winback_banner_click`:                                                struct{}{},
		`O2PaymentMethodDetailsBackTapped`:                                    struct{}{},
		`gclanding_support`:                                                   struct{}{},
		`EditorialReviewPageLoad`:                                             struct{}{},
		`O2CartZCreditsShown`:                                                 struct{}{},
		`O2CrystalMerchantAckSnippetImpression`:                               struct{}{},
		`MakeOrderPaymentMethodMisMatch`:                                      struct{}{},
		`O2MenuCustomizationTabViewed`:                                        struct{}{},
		`JumboEnameO2DropPinOpened`:                                           struct{}{},
		`gold_special_cart_continue_tapped`:                                   struct{}{},
		`inAppUpdate`:                                                         struct{}{},
		`ad_campaign_deeplink_tap`:                                            struct{}{},
		`contact_details_resend_invite_tap`:                                   struct{}{},
		`reporting_date_selected`:                                             struct{}{},
		`zfb_answered_unanswered_tapped`:                                      struct{}{},
		`manage_your_app`:                                                     struct{}{},
		`outlet-location-diy-help-tap`:                                        struct{}{},
		`calculate_menu_score_banner_impression`:                              struct{}{},
		`ci_no_refund_reason_screen`:                                          struct{}{},
		`mx_nps_aerobar_display`:                                              struct{}{},
		`rain_ddt_pill`:                                                       struct{}{},
		`O2CrystalRiderRatingImpression`:                                      struct{}{},
		`MicPermissionGrant`:                                                  struct{}{},
		`media_repo_button_click_filter`:                                      struct{}{},
		`O2CrystalCookingInstructionsTapped`:                                  struct{}{},
		`callerid_sync_started`:                                               struct{}{},
		`review_progress_bar_shown`:                                           struct{}{},
		`login_intent`:                                                        struct{}{},
		`le-transfer/next-click`:                                              struct{}{},
		`O2CrystalRiderValetSnippetCallButtonImpression`:                      struct{}{},
		`digital_contract_sign_creation_fetch_pan_fail`:                       struct{}{},
		`SSL_COROUTINE_EXCEPTION_HANDLER`:                                     struct{}{},
		`O2CrystalCarouselBlinkitImpression`:                                  struct{}{},
		`O2CrystalCNRDeliveryPromptImpression`:                                struct{}{},
		`GetNotificationPreferencesFailed`:                                    struct{}{},
		`O2CartSustBannerTapped`:                                              struct{}{},
		`O2EnterCVVLoaded`:                                                    struct{}{},
		`3pl-order-details`:                                                   struct{}{},
		`ScratchCardRewardSnippetImpression`:                                  struct{}{},
		`helpful_widget_tap`:                                                  struct{}{},
		`otp_by_call_login_using_other_methods`:                               struct{}{},
		`service_res_user_count_consistency`:                                  struct{}{},
		`PUMenuViewCart`:                                                      struct{}{},
		`variant_price_update`:                                                struct{}{},
		`event_deeplink_button_tapped`:                                        struct{}{},
		`delete_timings_click`:                                                struct{}{},
		`deeplink_verify_email_mobile_login`:                                  struct{}{},
		`O2CrystalNDGOrderAddressUpdatedBanner`:                               struct{}{},
		`O2CrystalUserSnippetImpression`:                                      struct{}{},
		`O2MenuPromoBarImpression`:                                            struct{}{},
		`deeplink_zpl_home`:                                                   struct{}{},
		`TRSlotCancellationPageLoaded`:                                        struct{}{},
		`DeviceLocationFetcherEvent`:                                          struct{}{},
		`DiningHistoryResClicked`:                                             struct{}{},
		`GoldBottomSheetChangeAddressTap`:                                     struct{}{},
		`reject_refund_order_log`:                                             struct{}{},
		`mx_bot_flow_why_my_res_offline`:                                      struct{}{},
		`O2CrystalPipCrystalOpenTapEvent`:                                     struct{}{},
		`LocationHomePromptTapped`:                                            struct{}{},
		`ad_alert_sent_push`:                                                  struct{}{},
		`add_new_property_group`:                                              struct{}{},
		`menu_item_edit_price_tap`:                                            struct{}{},
		`GOLD_DESKTOP_LOGIN_MODAL_SHOWN`:                                      struct{}{},
		`menu_item_recommended`:                                               struct{}{},
		`ViewYourPaymentsRatingSnippetClicked`:                                struct{}{},
		`se_am_email_tapped`:                                                  struct{}{},
		`webview-pro-click-on-add-contact-cta`:                                struct{}{},
		`EventCardTapped`:                                                     struct{}{},
		`ZPLVideoQuizStarted`:                                                 struct{}{},
		`CX_RX_CHAT_UNREAD_COUNT_CRYSTAL_PUSH`:                                struct{}{},
		`O2CrystalCarouselDCPackImpression`:                                   struct{}{},
		`mx_dining_web_menu_upload_menu_api_request_success`:                  struct{}{},
		`BlinkitTagImpression`:                                                struct{}{},
		`SDKAddMoneyInWalletLoaded`:                                           struct{}{},
		`SharedAddressSaveAPISuccess`:                                         struct{}{},
		`edit_phone_tapped`:                                                   struct{}{},
		`o2_digital_contract_initiated`:                                       struct{}{},
		`O2PaymentMethodWebViewLoadSuccess`:                                   struct{}{},
		`logstore_fetch_logs_data`:                                            struct{}{},
		`moderation_approve_log_button`:                                       struct{}{},
		`ProPlanPageActivationSheetProceedTapped`:                             struct{}{},
		`ResFeaturesInTapped`:                                                 struct{}{},
		`ownership-transfer/contract-auto-triggered`:                          struct{}{},
		`react_18_error''`:                                                    struct{}{},
		`deeplink_max_safety`:                                                 struct{}{},
		`O2CartCookingInstructionsRemoved`:                                    struct{}{},
		`O2PostOrderCartSupportButtonImpression`:                              struct{}{},
		`X-PAYMENTS-UPI-APPS`:                                                 struct{}{},
		`home_trigger_pinning`:                                                struct{}{},
		`mx-dispute-review-modal-open`:                                        struct{}{},
		`restaurant_selection_warning_dialog_tap`:                             struct{}{},
		`CRYSTAL_FRAGMENT_STOPPED`:                                            struct{}{},
		`O2AddPaymentMethodsAddPaymentTapped`:                                 struct{}{},
		`ad_alert_off_confirm`:                                                struct{}{},
		`Pan_Gst_Api_Returned_Gst`:                                            struct{}{},
		`high_ddt_blocker_impression`:                                         struct{}{},
		`onboarding_placeholder_action_tap`:                                   struct{}{},
		`rejected_order_alert_click`:                                          struct{}{},
		`res_offline_on_time_option_tap`:                                      struct{}{},
		`max_safety_declaration_update`:                                       struct{}{},
		`invoices_tap`:                                                        struct{}{},
		`ownership-transfer/accepted-by-govid/fssai-success`:                  struct{}{},
		`ProMemberDOResPageViewedPro`:                                         struct{}{},
		`O2ArrivingSoonNotifTriggered`:                                        struct{}{},
		`BmCollectionsAction`:                                                 struct{}{},
		`TabsLoaded`:                                                          struct{}{},
		`alert_notification_impression`:                                       struct{}{},
		`gold_special_cart_promo_applied`:                                     struct{}{},
		`tdpa_page_tracking`:                                                  struct{}{},
		`tour_exit_cancel`:                                                    struct{}{},
		`digital_contract_create_sign_open_fail`:                              struct{}{},
		`order_refund_no_refund_tap`:                                          struct{}{},
		`merchant_winback_programme`:                                          struct{}{},
		`web_universal_search_api_error''`:                                    struct{}{},
		`SearchLocationSearchSuccess`:                                         struct{}{},
		`base_whatsapp_notification`:                                          struct{}{},
		`V2TAB_PARSED`:                                                        struct{}{},
		`universe_mars_rider_apk_download_click`:                              struct{}{},
		`otp_request_successful_phone_verification`:                           struct{}{},
		`AppLaunchGPSLocationRetry`:                                           struct{}{},
		`O2MenuCustomizationGroupImpression`:                                  struct{}{},
		`StoryLoaderShown`:                                                    struct{}{},
		`menu_delete_catalogue_confirm_button`:                                struct{}{},
		`TRSlotBookingPageNonGoldSheetGotItButtonTapped`:                      struct{}{},
		`zpay_status_page_cta_tap`:                                            struct{}{},
		`O2CartPageLoaded`:                                                    struct{}{},
		`ResScannedMenusTapped`:                                               struct{}{},
		`NoneSelected`:                                                        struct{}{},
		`O2CrystalNotificationCardTapped`:                                     struct{}{},
		`custom_offers_screen_data_fetch_initiated`:                           struct{}{},
		`past_orders_listing_tap`:                                             struct{}{},
		`wrong_address_rejection_deletion`:                                    struct{}{},
		`Call_OrderHistory`:                                                   struct{}{},
		`SDKCardConsentTapped`:                                                struct{}{},
		`LocationAddAddressAPIFailed`:                                         struct{}{},
		`SnippetViewCacheHelper`:                                              struct{}{},
		`youtube_video_screen_impression`:                                     struct{}{},
		`logstore_date_time_shift_operations`:                                 struct{}{},
		`O2CrystalContactlessPayOnlineTapped`:                                 struct{}{},
		`web_delete_account_failed`:                                           struct{}{},
		`@@ldRZa`:                                                             struct{}{},
		`O2CartDcStickyBannerImpression`:                                      struct{}{},
		`SideMenuFeaturesTapped`:                                              struct{}{},
		`Backend_JumboEnameO2PhoneVerificationEnd`:                            struct{}{},
		`Dev_empty_top_vc`:                                                    struct{}{},
		`O2MenuMiniCartDismissed`:                                             struct{}{},
		`O2CartInstructionChangeTapped`:                                       struct{}{},
		`O2AutoDetectLocationFetched`:                                         struct{}{},
		`ad_alert_on_tap`:                                                     struct{}{},
		`dedup_location_search`:                                               struct{}{},
		`feature_toggle_edit`:                                                 struct{}{},
		`switch_to_owner_hub`:                                                 struct{}{},
		`deeplink_zra`:                                                        struct{}{},
		`phone_login_singe_res_mapped`:                                        struct{}{},
		`CounterTapped`:                                                       struct{}{},
		`ZomatoWebSignupPageLoaded'`:                                          struct{}{},
		`deeplink_commonwebview`:                                              struct{}{},
		`O2OfferItemSelectionSheetCheckoutTap`:                                struct{}{},
		`deeplink_uc`:                                                         struct{}{},
		`order_placement_funnel`:                                              struct{}{},
		`preview_photo_tap`:                                                   struct{}{},
		`V2TAB_RENDERED`:                                                      struct{}{},
		`merchant_office_failure`:                                             struct{}{},
		`O2CartSustainabilitySnippetImpression`:                               struct{}{},
		`LocationMapScreenCurrentLocationTapped`:                              struct{}{},
		`LocationMapScreenPinMovedByUser`:                                     struct{}{},
		`ResOtherDiningRecommendationsImpression`:                             struct{}{},
		`HomeItemTapped`:                                                      struct{}{},
		`GoldPlanPageChatClick`:                                               struct{}{},
		`SeeAllOutletsCityTapped`:                                             struct{}{},
		`karma_vpn_check`:                                                     struct{}{},
		`new-order-in-selector`:                                               struct{}{},
		`reviews_detail_screen_impression`:                                    struct{}{},
		`side_bar_tab_impression`:                                             struct{}{},
		`web_delete_account_success`:                                          struct{}{},
		`earth-day-2023`:                                                      struct{}{},
		`SDKWalletLinkingSuccess`:                                             struct{}{},
		`O2MenuSimilarResRailItemTap`:                                         struct{}{},
		`ZPLLobbyTimerSnippetTap`:                                             struct{}{},
		`ad_campaign_tracking_tab`:                                            struct{}{},
		`cached_location_gson_parsing_time`:                                   struct{}{},
		`ci_filter_tap`:                                                       struct{}{},
		`GOLD_DESKTOP_PAGE_VIEWS`:                                             struct{}{},
		`ci_listing_cards`:                                                    struct{}{},
		`o2SmsSentAfterTracks`:                                                struct{}{},
		`ci_listing_date_range_filter`:                                        struct{}{},
		`review_dispute_show_bottom_sheet`:                                    struct{}{},
		`save_photo_fail`:                                                     struct{}{},
		`SDKAddCardBusinessTapped`:                                            struct{}{},
		`Popular_Dishes_Scrolled`:                                             struct{}{},
		`V2TABS_FAILED`:                                                       struct{}{},
		`hp_start_buying_tap`:                                                 struct{}{},
		`menu_item_edit_save_confirm`:                                         struct{}{},
		`O2CrystalEnterExplorerButtonTapEvent`:                                struct{}{},
		`@@pwVXH`:                                                             struct{}{},
		`AODDataParams`:                                                       struct{}{},
		`O2CrystalTipBottomSheetButtonTapEvent`:                               struct{}{},
		`contact_details_delete_confirm`:                                      struct{}{},
		`order_ready_click`:                                                   struct{}{},
		`reviews_filter_menu_tapped`:                                          struct{}{},
		`tour_go_online_click`:                                                struct{}{},
		`review_tag_selected`:                                                 struct{}{},
		`cc_service_recovery_null_error`:                                      struct{}{},
		`mx_dining_web_transaction_get_api_request_failed`:                    struct{}{},
		`DashboardBannerCTATapEvent`:                                          struct{}{},
		`deeplink_bv`:                                                         struct{}{},
		`CartLocationEntityTracking`:                                          struct{}{},
		`deeplink_collections`:                                                struct{}{},
		`ownership-transfer/send-contract/create-office`:                      struct{}{},
		`faq`:                                                       struct{}{},
		`rejection_reason_trends_tab_click`:                         struct{}{},
		`buy_gift_card_hide_code_clicked`:                           struct{}{},
		`support_customer_instruction_action_tap`:                   struct{}{},
		`O2MenuBrandSwitcherImpression`:                             struct{}{},
		`adpa_page_tracking`:                                        struct{}{},
		`otp_bottomsheet_sent_status`:                               struct{}{},
		`mx_dining_web_initiate_transformation_api_request_success`: struct{}{},
		`order_flow_sidemenu_tap`:                                   struct{}{},
		`O2CrystalDIYCombinedBottomSheetImpressionEvent`:            struct{}{},
		`HomePageEmptyList`:                                         struct{}{},
		`res_delivery_offer_imp''`:                                  struct{}{},
		`ResWriteReviewTapped`:                                      struct{}{},
		`PaymentSDKTokenError`:                                      struct{}{},
		`contact_details_edit_tap`:                                  struct{}{},
		`custom_offers_ui_element_click`:                            struct{}{},
		`geocode_invalid_lat_lon`:                                   struct{}{},
		`rejection_v2_go_back_tap`:                                  struct{}{},
		`res_takeaway_timing_update`:                                struct{}{},
		`retry_requested`:                                           struct{}{},
		`review_write_page_success`:                                 struct{}{},
		`RedWebViewError`:                                           struct{}{},
		`UserReviewsTapped`:                                         struct{}{},
		`custom-redirect-mansiverma`:                                struct{}{},
		`LOCATION_DETECTION_NEW_INSTALL`:                            struct{}{},
		`CX_RX_CHAT_UNREAD_COUNT_AEROBAR_PUSH`:                      struct{}{},
		`menu_catalgue_toggle_stock`:                                struct{}{},
		`returning_tab_click`:                                       struct{}{},
		`order_timelines_toggle`:                                    struct{}{},
		`O2CrystalPayOnlineImpression`:                              struct{}{},
		`O2DeliveryInstructionRailTapped`:                           struct{}{},
		`GoldShareBannerTap`:                                        struct{}{},
		`User_Moved_to_O2_Form_Create_Your_Res`:                     struct{}{},
		`app_start_sequence_check`:                                  struct{}{},
		`ci_order_card_expand`:                                      struct{}{},
		`moderation_auto_assign_button`:                             struct{}{},
		`O2CrystalThirdPartyVendorTracking`:                         struct{}{},
		`RedPlanPageLoaded`:                                         struct{}{},
		`mx_dining_web_cancel_login_with_instagram_clicked`:         struct{}{},
		`SDKAddPaymentMethodsLoaded`:                                struct{}{},
		`SDKNativeEnterOtpLoaded`:                                   struct{}{},
		`O2MenuRailAction`:                                          struct{}{},
		`call_customer_accept_tap`:                                  struct{}{},
		`O2CartRunnrTipShown`:                                       struct{}{},
		`discarded_order_details_fetch`:                             struct{}{},
		`fssai_update`:                                              struct{}{},
		`SDKRemoveWalletFailure`:                                    struct{}{},
		`assign_rider_blank_screen_impression`:                      struct{}{},
		`O2CrystalResQualityCardImpression`:                         struct{}{},
		`V2SearchResultRendered`:                                    struct{}{},
		`deeplink_u`:                                                struct{}{},
		`delivered_order_trends_impression`:                         struct{}{},
		`mx_dining_web_your_customers_customer_listing_api_success_failure`: struct{}{},
		`new_web_performance_metric`:                                        struct{}{},
		`JumboEnameO2AddNewAddressClicked`:                                  struct{}{},
		`PhoneNumberFieldTapped`:                                            struct{}{},
		`appsflyer_app_open_attribution_map`:                                struct{}{},
		`feedback_order_rating_tapped`:                                      struct{}{},
		`offers_growth_card_v1_snippet_click`:                               struct{}{},
		`settlements_tap`:                                                   struct{}{},
		`mx_dining_web_click_on_send_for_review`:                            struct{}{},
		`resolved_complaints_dispute_feedback`:                              struct{}{},
		`LocationMapScreenAPISuccess`:                                       struct{}{},
		`SDKRetryPaymentFooterButtonTapped`:                                 struct{}{},
		`AppLegacyIconCode`:                                                 struct{}{},
		`home_activity_foreground`:                                          struct{}{},
		`ad_campaign_billing_expand_collapse_tap`:                           struct{}{},
		`menu_filter_button`:                                                struct{}{},
		`App Launch`:                                                        struct{}{},
		`crash_boundary_capture`:                                            struct{}{},
		`instant_get_kitchens_api_failed`:                                   struct{}{},
		`custom-redirect-whatsapp`:                                          struct{}{},
		`SDKAddCardSuccess`:                                                 struct{}{},
		`O2MenuOfferSelectionSnippetTap`:                                    struct{}{},
		`SDKADCBPageOpened`:                                                 struct{}{},
		`res_delivery_offer_tap`:                                            struct{}{},
		`ownership-transfer/send-contract/contract-expired`:                 struct{}{},
		`O2CrystalChatHeaderSnippetImpression`:                              struct{}{},
		`EventPageOverviewLoaded`:                                           struct{}{},
		`O2MenuOutletSwitcherOptionTapped`:                                  struct{}{},
		`menu_image_upload`:                                                 struct{}{},
		`mweb_amp_open_app_menu_modal_menu_page_continue_click`:             struct{}{},
		`sidebar_tap`:                                                       struct{}{},
		`UpdateNotificationPreferencesFailed`:                               struct{}{},
		`cuisine_tapped`:                                                    struct{}{},
		`home_page_load_failed`:                                             struct{}{},
		`create_promo_tap`:                                                  struct{}{},
		`O2CrystalRiderTipImpression`:                                       struct{}{},
		`O2CrystalUserAddressModificationSnippetTappedV16`:                  struct{}{},
		`O2CartCookingInstructionSuggestionsImpression`:                     struct{}{},
		`GoldMembershipRenewButtonTap`:                                      struct{}{},
		`GPSLocationFetchFailedWithNoLocations`:                             struct{}{},
		`UserPrefSheetDismissed`:                                            struct{}{},
		`landing_page_order_online_benefit_impression`:                      struct{}{},
		`cc_reasons_clicked`:                                                struct{}{},
		`location_diy_view_link_on_google_maps_tap`:                         struct{}{},
		`SaveO2DeliveryRatingFailed`:                                        struct{}{},
		`dc_mismatch`:                                                       struct{}{},
		`menu_image_deleted`:                                                struct{}{},
		`mweb_open_app_modal_v3_impression`:                                 struct{}{},
		`GenericWebviewCTA-2_anniversary_push`:                              struct{}{},
		`webview-april-fools-2022-button-check-fake-crystal-click`:          struct{}{},
		`SDKAddMoneyInWalletBackTapped`:                                     struct{}{},
		`DRAFT_MIGRATION_EVENT`:                                             struct{}{},
		`asset-banner-clicked`:                                              struct{}{},
		`menu_booster_pack_selected`:                                        struct{}{},
		`O2CartDuplicateOrderPopupImpression`:                               struct{}{},
		`ORP_Load`:                                                          struct{}{},
		`TabbedHomePreFetchFlow`:                                            struct{}{},
		`inAppUpdate_aerobar`:                                               struct{}{},
		`learning_centre_screen_impression`:                                 struct{}{},
		`o2EmailCTA`:                                                        struct{}{},
		`recent_location_click`:                                             struct{}{},
		`feedback_expand_section_button_tapped`:                             struct{}{},
		`reload_due_to_1020`:                                                struct{}{},
		`continue_without_email_popup_impression`:                           struct{}{},
		`PUCrystalPageViewed`:                                               struct{}{},
		`CityInitiatedButNotLive`:                                           struct{}{},
		`GenericWebviewCTA-customer_rating`:                                 struct{}{},
		`ad_campaign_impressions`:                                           struct{}{},
		`diningRatingSnippetSeen`:                                           struct{}{},
		`invalid_content_type`:                                              struct{}{},
		`mweb_amp_open_app_modal_v2_impression`:                             struct{}{},
		`SDKRecacheSuccess`:                                                 struct{}{},
		`mx_fssai_notification_centre_click`:                                struct{}{},
		`navbar_charges_page_button`:                                        struct{}{},
		`deeplink_zchat`:                                                    struct{}{},
		`edition_tsp_token`:                                                 struct{}{},
		`redirect-to-outlet-location-diy`:                                   struct{}{},
		`ResOBPTapped`:                                                      struct{}{},
		`reassignToRunnr`:                                                   struct{}{},
		`ownership-transfer/accepted-by-govid/contract-success`:             struct{}{},
		`merchant_penalty_policy_v2`:                                        struct{}{},
		`JumboEnameZMTUserAgent`:                                            struct{}{},
		`add_review_tapped`:                                                 struct{}{},
		`home_banner_view`:                                                  struct{}{},
		`ResAddPhotoTapped`:                                                 struct{}{},
		`O2AddNewAddressClicked`:                                            struct{}{},
		`PromoPopupImpression`:                                              struct{}{},
		`order_history_snippet_tapped`:                                      struct{}{},
		`mweb_dom_painted9860022`:                                           struct{}{},
		`O2CrystalMembershipSnippetViewed`:                                  struct{}{},
		`SDKSavedPaymentMethodsLoaded`:                                      struct{}{},
		`Dev_no_content_error_view_seen`:                                    struct{}{},
		`allow_overdraw_click`:                                              struct{}{},
		`CartPaymentFailureViewButtonTap`:                                   struct{}{},
		`mobile_number_start_editing`:                                       struct{}{},
		`O2PaymentMethodWebViewBackTapped`:                                  struct{}{},
		`le_update_verification_status`:                                     struct{}{},
		`sound_notification_on`:                                             struct{}{},
		`O2HomeViewed`:                                                      struct{}{},
		`ScratchCardSnippetImpression`:                                      struct{}{},
		`GenericWebviewCTA-legends_x_gold_updateapp`:                        struct{}{},
		`OffersSectionPayBillTapped`:                                        struct{}{},
		`geocode_subzone_detection`:                                         struct{}{},
		`get-app-form-send-link-click`:                                      struct{}{},
		`new_order_strip_click`:                                             struct{}{},
		`on_load_modal_clicked`:                                             struct{}{},
		`order_reassign_rejected`:                                           struct{}{},
		`ListingsDelegateNilParseStart`:                                     struct{}{},
		`FeedingIndiaPanDetailsBottomButtonTapped`:                          struct{}{},
		`Pro_Brand_Rail_Tile_Tapped`:                                        struct{}{},
		`branch_deeplink_error`:                                             struct{}{},
		`digital_contract_login_success`:                                    struct{}{},
		`tapped_login_link`:                                                 struct{}{},
		`web_universal_location_modify_tap`:                                 struct{}{},
		`ads_home_card_tap`:                                                 struct{}{},
		`ci_issue_insights_tips_tap`:                                        struct{}{},
		`mac_cancellation_acceptance_tap`:                                   struct{}{},
		`O2CrystalRateAppClicked`:                                           struct{}{},
		`ZomatoOrganizationAppTracking`:                                     struct{}{},
		`deeplink_gold_home`:                                                struct{}{},
		`O2OFSECallResTapped`:                                               struct{}{},
		`V2opened_app`:                                                      struct{}{},
		`instruction_new_order`:                                             struct{}{},
		`menu_score_intro_calculate_score_tap`:                              struct{}{},
		`mweb_open_app_modal_v3_outside_click`:                              struct{}{},
		`O2CrystalRunnrTipDataFetched`:                                      struct{}{},
		`GamificationServerSyncSucceeds`:                                    struct{}{},
		`O2MenuPromoBarTap`:                                                 struct{}{},
		`api_deeplink_login_success`:                                        struct{}{},
		`deeplink_search`:                                                   struct{}{},
		`support_accordion_toggle`:                                          struct{}{},
		`ProCartApplyPromoTapped`:                                           struct{}{},
		`O2CrystalRunnrTipCheckoutButtonClicked`:                            struct{}{},
		`ScratchCardSnippetTapped`:                                          struct{}{},
		`deeplink_generic_cart`:                                             struct{}{},
		`manage_outlet_click`:                                               struct{}{},
		`BlinkitInstallSnippetTapped`:                                       struct{}{},
		`customer_issues_tap`:                                               struct{}{},
		`manage_notifications_toggle_tap`:                                   struct{}{},
		`SDKAddPaymentMethodsBackTapped`:                                    struct{}{},
		`O2CrystalNDGOrderOnTimeBannerTapEvent`:                             struct{}{},
		`GamificationVideoDownloadFailed`:                                   struct{}{},
		`menu_filter_selected`:                                              struct{}{},
		`O2CrystalExitExplorerButtonTapEvent`:                               struct{}{},
		`review_dispute_failed_no_replies`:                                  struct{}{},
		`O2HomeVideoCardWatchedCompletely`:                                  struct{}{},
		`NsfwDetectionUserProfilePicRemoval`:                                struct{}{},
		`deeplink_wallet`:                                                   struct{}{},
		`dining-business-partner-app-page-open`:                             struct{}{},
		`print_kot_sent`:                                                    struct{}{},
		`O2CrystalGiftImpression`:                                           struct{}{},
		`menu_edit_sub_category_save_click`:                                 struct{}{},
		`locality_stress_rain_anomaly`:                                      struct{}{},
		`O2Plus`:                                                            struct{}{},
		`SDKInitCallStarted`:                                                struct{}{},
		`O2PaymentFailedPageRetryTapped`:                                    struct{}{},
		`TimingsChecked`:                                                    struct{}{},
		`ReferralPageLoad`:                                                  struct{}{},
		`feedback_order_tag_tapped`:                                         struct{}{},
		`feedback_page_open_failed`:                                         struct{}{},
		`rider_call_tapped`:                                                 struct{}{},
		`O2CrystalExplorerOfferTapEvent`:                                    struct{}{},
		`ResAwardsWinnerBookmarkTapped`:                                     struct{}{},
		`deeplink_gift_card`:                                                struct{}{},
		`webview-dining-tr-restrictions-add-slot-api-success`:               struct{}{},
		`view_permissions_tap`:                                              struct{}{},
		`sample_menu_change_description`:                                    struct{}{},
		`collection_expanded_click_homepage`:                                struct{}{},
		`ResPageSectionImpression`:                                          struct{}{},
		`ShareAddressTappedResult`:                                          struct{}{},
		`deeplink_r`:                                                        struct{}{},
		`image_qc_page_load`:                                                struct{}{},
		`JumboEnameO2SelectExistingAddress`:                                 struct{}{},
		`banner_form_request`:                                               struct{}{},
		`support_ticket_item_click`:                                         struct{}{},
		`ad_campaign_preview_carousal_tap`:                                  struct{}{},
		`notification_centre_email_tap`:                                     struct{}{},
		`O2CrystalRiderSafetyBottomsheetImageImpression`:                    struct{}{},
		`ndg_cart_snippet_impression`:                                       struct{}{},
		`res_page_back_icon_click`:                                          struct{}{},
		`outlet-location-diy-next-tap`:                                      struct{}{},
		`ownership-transfer/accepted-by-govid/bank-success`:                 struct{}{},
		`TRSlotCartPageLoaded`:                                              struct{}{},
		`hyperpure_tap`:                                                     struct{}{},
		`toggle_timings_click`:                                              struct{}{},
		`edit_phone_number_back_button_tapped`:                              struct{}{},
		`mx_dining_web_refund_reason_api_failed`:                            struct{}{},
		`ZPLMatchScreenBackButtonTapped`:                                    struct{}{},
		`inventory_category_fab_tap`:                                        struct{}{},
		`quick_link_tap`:                                                    struct{}{},
		`cart_ui_validation_failed`:                                         struct{}{},
		`multi-package-package-selection-postpaid-payment-click`:            struct{}{},
		`OSM_NONLOGS_TRACKABLE_ORDER_HISTORY_RESPONSE`:                      struct{}{},
		`O2CrystalResAddItemImpression`:                                     struct{}{},
		`SDKPollingPageBackConfirmed`:                                       struct{}{},
		`order_impression_from_error_page`:                                  struct{}{},
		`mapp_diy_contact_details`:                                          struct{}{},
		`ResPropertyTapped`:                                                 struct{}{},
		`MENU_PARSED`:                                                       struct{}{},
		`O2AddMoneyInWalletAmountTapped`:                                    struct{}{},
		`O2MenuCheckoutImpression`:                                          struct{}{},
		`SDKWalletLinkingResendOTP`:                                         struct{}{},
		`LoginThroughMailPageCTATapped`:                                     struct{}{},
		`SignInFailedForZomato`:                                             struct{}{},
		`mx_dining_web_menu_get_menu_api_request_success`:                   struct{}{},
		`mx_reporting_tab_click`:                                            struct{}{},
		`res_offline_view_details_tap`:                                      struct{}{},
		`verify_new_email_with_otp`:                                         struct{}{},
		`SMSClick`:                                                          struct{}{},
		`support_image_tile_tap`:                                            struct{}{},
		`search_location_fetched_from_cookie-0`:                             struct{}{},
		`SDKAddMoneyInWalletProceedTapped`:                                  struct{}{},
		`O2MenuSeeMorePresent`:                                              struct{}{},
		`HomeSetPreciseLocationImpression`:                                  struct{}{},
		`O2TipPayTapped`:                                                    struct{}{},
		`O2CrystalCallButtonTapped`:                                         struct{}{},
		`PromoPagePromoApplyClicked`:                                        struct{}{},
		`StorySwipedRight`:                                                  struct{}{},
		`O2CrystalRiderContactBottomSheetRecordInstructionsButtonTapped`:    struct{}{},
		`feed`:                                                 struct{}{},
		`past_order_detail_support_tap`:                        struct{}{},
		`zfb_side_menu`:                                        struct{}{},
		`feedback_help_centre_tap`:                             struct{}{},
		`assign_rider_primary_btn_tap`:                         struct{}{},
		`o2IntentClick`:                                        struct{}{},
		`logout_from_all_devices_success`:                      struct{}{},
		`LoginFail`:                                            struct{}{},
		`GoldMembershipTellFriendsImpression`:                  struct{}{},
		`O2MenuPreOrderSnippetImpression`:                      struct{}{},
		`StoryViewed`:                                          struct{}{},
		`ad_campaign_Details_page_loaded`:                      struct{}{},
		`go_to_all_orders_click`:                               struct{}{},
		`mx_dining_web_story_publish_failed`:                   struct{}{},
		`ps-insertion-empty-plan-meta`:                         struct{}{},
		`O2Discovery`:                                          struct{}{},
		`ResReviewsLoaded`:                                     struct{}{},
		`DiskIOMetrics`:                                        struct{}{},
		`O2MenuOosDetails`:                                     struct{}{},
		`LOCATION_CACHE_POTENTIAL_HIT`:                         struct{}{},
		`ResAwardsNomineesPageLoaded`:                          struct{}{},
		`ResReportSubmitted`:                                   struct{}{},
		`ContactSelectedFromPicker`:                            struct{}{},
		`ci_issue_alert_impression`:                            struct{}{},
		`connected-socket-event`:                               struct{}{},
		`ci_no_refund_reason_selected`:                         struct{}{},
		`review_dispute_failed_with_reply`:                     struct{}{},
		`socket_reconnect_called`:                              struct{}{},
		`menu_bulk_services_button`:                            struct{}{},
		`welcome_register_tap`:                                 struct{}{},
		`res_dsz_mapping`:                                      struct{}{},
		`phone_login_case`:                                     struct{}{},
		`CallRestaurant`:                                       struct{}{},
		`REFERRAL_CODE_SHARE_CLICK`:                            struct{}{},
		`generic_bottomsheet_primary_btn_click`:                struct{}{},
		`ZPLSubscriptionPageViewed`:                            struct{}{},
		`O2CrystalDownloadBillImpression`:                      struct{}{},
		`O2CrystalNDGOrderAddressUpdatedBannerTapEvent`:        struct{}{},
		`add_photo_manage_button_tapped`:                       struct{}{},
		`collection_tap`:                                       struct{}{},
		`order_delay_ring`:                                     struct{}{},
		`pro_promo_domino_logs`:                                struct{}{},
		`zomarks_bookmark_create`:                              struct{}{},
		`step_tracker_event`:                                   struct{}{},
		`add_gstin_option_save_success`:                        struct{}{},
		`ZomatoWebSignupSuccess'`:                              struct{}{},
		`O2CrystalHOZSnippetCrossButtonTapped`:                 struct{}{},
		`O2CrystalNDGOrderGeofenceOnTimeBanner`:                struct{}{},
		`O2AerobarUpdateEvent`:                                 struct{}{},
		`LocationHomeGPSPromptImpression`:                      struct{}{},
		`HomeVideoBottomButtonTapped`:                          struct{}{},
		`GenericWebviewCTA-legends_express_calendar_delhi_veg`: struct{}{},
		`cc-card-tap`:                                          struct{}{},
		`imagePushNotifClickedOnApp`:                           struct{}{},
		`mx_dining_web_your_customers_customer_summary_api_success_failure`: struct{}{},
		`pos_order_page_impression`:                                         struct{}{},
		`schedule_proceed_tap`:                                              struct{}{},
		`ad-manager-banner`:                                                 struct{}{},
		`web_scroll_to_top_button_click`:                                    struct{}{},
		`give_us_feedback`:                                                  struct{}{},
		`O2CrystalAppUpdateImpression`:                                      struct{}{},
		`SDKPaymentPageLoaded`:                                              struct{}{},
		`ZPLButtonLoaderImpression`:                                         struct{}{},
		`ad_campaign_view_history_tap`:                                      struct{}{},
		`aerobar_tap`:                                                       struct{}{},
		`ListingParseCuration1Ends`:                                         struct{}{},
		`offer_config_overlapping_agree`:                                    struct{}{},
		`RESTAURANT_NOT_ACCEPTING_DELAYED_NOTIFICATION`:                     struct{}{},
		`reviews_filter_applied`:                                            struct{}{},
		`HomeWebViewLoadFinished`:                                           struct{}{},
		`TRBookingHistoryTap`:                                               struct{}{},
		`deeplink_c-3`:                                                      struct{}{},
		`O2CartPromoApplied`:                                                struct{}{},
		`O2OFSEAddContactTapped`:                                            struct{}{},
		`aerobar_click`:                                                     struct{}{},
		`EDIT_EMAIL_FLOW_UPDATE_EMAIL`:                                      struct{}{},
		`auth_verification_verify`:                                          struct{}{},
		`menu_score_deeplink_navigation`:                                    struct{}{},
		`order_history_search_back`:                                         struct{}{},
		`tour_card_tap`:                                                     struct{}{},
		`O2CrystalChatSnippetTapped`:                                        struct{}{},
		`SDKAddCardNameEntered`:                                             struct{}{},
		`GenericWebviewCTA-Awards_notupdated`:                               struct{}{},
		`kpt_delay_screen_close_tap`:                                        struct{}{},
		`tour_screen_impression`:                                            struct{}{},
		`O2AddAddressCompleteAddressType`:                                   struct{}{},
		`O2AddAddressPageOpened`:                                            struct{}{},
		`top_strip_banner_click`:                                            struct{}{},
		`O2CrystalNDGOrderDelayedBannerTapEvent`:                            struct{}{},
		`GoldIntroResCardsImpression`:                                       struct{}{},
		`O2MenuServiceabilityBannerImpression`:                              struct{}{},
		`BlinkitInstallSnippetImpression`:                                   struct{}{},
		`deeplink_otp_login_mobile`:                                         struct{}{},
		`user_global_logout`:                                                struct{}{},
		`mx_dining_web_transaction_refund_api_request_success`:              struct{}{},
		`langBtnClicked`:                                                    struct{}{},
		`O2CrystalV16RiderInfoImpression`:                                   struct{}{},
		`AppLocationPermissionStatus`:                                       struct{}{},
		`O2MenuOfferButtonTapped`:                                           struct{}{},
		`O2CartInstructionLWGImpressionEvent`:                               struct{}{},
		`hp_partner_show_consent_success`:                                   struct{}{},
		`order-landed-type-event`:                                           struct{}{},
		`partner_user_permission_fail`:                                      struct{}{},
		`O2CrtstalViewOrderImpression`:                                      struct{}{},
		`OAuthFlow`:                                                         struct{}{},
		`event_menu_addons_info_tap`:                                        struct{}{},
		`permissions_group`:                                                 struct{}{},
		`support_rider_chat_support_tap`:                                    struct{}{},
		`mx_dining_web_click_on_view_story`:                                 struct{}{},
		`O2HomeSearchBarTapped`:                                             struct{}{},
		`ResAwardsPageLoaded`:                                               struct{}{},
		`contract_preview_impression`:                                       struct{}{},
		`order_tap`:                                                         struct{}{},
		`toggle_timings_confirm`:                                            struct{}{},
		`aerobar_api_not_called`:                                            struct{}{},
		`orders-fetched-from-polling`:                                       struct{}{},
		`O2CrystalThirdPartyVendorTrackingCallButtonTapped`:                 struct{}{},
		`convince_screen_accept_tap`:                                        struct{}{},
		`deeplink_review`:                                                   struct{}{},
		`gupshup_delivery_reports`:                                          struct{}{},
		`SDKCardConsentTaken`:                                               struct{}{},
		`LOCATION_DETECTION_AUTO`:                                           struct{}{},
		`deeplink_order`:                                                    struct{}{},
		`rider_map_impression`:                                              struct{}{},
		`customer_address_copy_click`:                                       struct{}{},
		`multi-package-backward-flow-dish-change-click`:                     struct{}{},
		`TRRestaurantDetailsSectionGetDirectionsTapped`:                     struct{}{},
		`mweb_dom_painted9548893`:                                           struct{}{},
		`web_universal_search_api_error'`:                                   struct{}{},
		`O2CrystalDownloadInvoiceButtonTapped`:                              struct{}{},
		`A13_FULLSCREEN_TOAST_SHOWN`:                                        struct{}{},
		`LocationAddAddressAPISuccess`:                                      struct{}{},
		`PreFetchedResultDiscarded`:                                         struct{}{},
		`TabSelectedV2`:                                                     struct{}{},
		`accept_order_click`:                                                struct{}{},
		`cc_give_refund_after_feedback_clicked`:                             struct{}{},
		`digital_contract_status_update`:                                    struct{}{},
		`order_timeline_info_dialog_impression`:                             struct{}{},
		`serviceability_reminder_poller`:                                    struct{}{},
		`mweb_dom_painted9955852`:                                           struct{}{},
		`ZDBManagerRequestCompleted`:                                        struct{}{},
		`PUMenuItemReadMoreTapped`:                                          struct{}{},
		`JumboEnameO2CartAddressChangeClicked`:                              struct{}{},
		`multi-package-main-page-impression`:                                struct{}{},
		`PLAN_PAGE_FULLY_LOADED`:                                            struct{}{},
		`ThirdPartyAPIs\IDFY\IDFYApiCaller_error`:                           struct{}{},
		`deeplink_videos`:                                                   struct{}{},
		`O2CrystalTipBottomSheetImpressionEvent`:                            struct{}{},
		`SDKUpiGpayHealthCheckStatus`:                                       struct{}{},
		`product_env_banner_impression`:                                     struct{}{},
		`surge_dc_config_run`:                                               struct{}{},
		`O2_ORDER_SERVICEABILITY'`:                                          struct{}{},
		`O2CrystalTipSectionButtonTapEvent`:                                 struct{}{},
		`LoginScreenPresented`:                                              struct{}{},
		`LocationReceiverMobileTapped`:                                      struct{}{},
		`delete_document_click`:                                             struct{}{},
		`menu_tap`:                                                          struct{}{},
		`prefered-pos-main-impression`:                                      struct{}{},
		`ecofriendly_fetch_form_data`:                                       struct{}{},
		`SdkMakePaymentCallPending`:                                         struct{}{},
		`SplashNavigationEvent`:                                             struct{}{},
		`impression_ads_budget_card_tap`:                                    struct{}{},
		`view_all_tickets_tap`:                                              struct{}{},
		`TRSlotBookingPageReservationDetailsSelected`:                       struct{}{},
		`o2_digital_contract_empty_phone`:                                   struct{}{},
		`ownership-transfer/accepted-by-govid/pro-success`:                  struct{}{},
		`otherCancelReason`:                                                 struct{}{},
		`LangaugeTourTextShown`:                                             struct{}{},
		`WEBVIEW_LOADED`:                                                    struct{}{},
		`base_price_box_click`:                                              struct{}{},
		`menu_item_add_image_tap`:                                           struct{}{},
		`callerid_lookup`:                                                   struct{}{},
		`otp_response_status_phone_verification`:                            struct{}{},
		`LoginSuccess`:                                                      struct{}{},
		`BottomsheetChangeButtonTapped`:                                     struct{}{},
		`O2MenuHeaderPopupDismiss`:                                          struct{}{},
		`InvalidCacheUsedEvent`:                                             struct{}{},
		`inventory_category_fab_list_item_tap`:                              struct{}{},
		`image_qc_filter_selected`:                                          struct{}{},
		`digital_contract_create_sign_open_success`:                         struct{}{},
		`O2EnterCVVBackTapped`:                                              struct{}{},
		`diy-save-top-dishes`:                                               struct{}{},
		`ndg_user_claim_failed`:                                             struct{}{},
		`deeplink_c-10`:                                                     struct{}{},
		`in_app_popup_impression`:                                           struct{}{},
		`ButtonSetupCardControlsTapEvent`:                                   struct{}{},
		`GoldPlanPageGoldRestaurantsClick`:                                  struct{}{},
		`remove_saved_cart`:                                                 struct{}{},
		`O2CrystalFooterNonLogsDeliveredTap`:                                struct{}{},
		`image_replace_qc_button`:                                           struct{}{},
		`logstore_refresh_click`:                                            struct{}{},
		`O2CrystalPipImpressionEvent`:                                       struct{}{},
		`ad_alert_off_cancel_on`:                                            struct{}{},
		`riderMovementTracking`:                                             struct{}{},
		`insights_card_filter_tap`:                                          struct{}{},
		`home_pinging_failed`:                                               struct{}{},
		`rejected_order_trends_impression`:                                  struct{}{},
		`NotifcationPermissionPopShown`:                                     struct{}{},
		`gold_special_cart_pro_removed`:                                     struct{}{},
		`new-order-appended-reducer`:                                        struct{}{},
		`order_share_page_opened`:                                           struct{}{},
		`page_card_impression`:                                              struct{}{},
		`PackagesBookingsListTapped`:                                        struct{}{},
		`viewedPhoneRedUserHistory`:                                         struct{}{},
		`welcome_help_tap`:                                                  struct{}{},
		`ShouldEnablePinningFlagRequested`:                                  struct{}{},
		`SDKPaymentOptionsExpandSection`:                                    struct{}{},
		`GoldCartPromoSuccess`:                                              struct{}{},
		`hp-buy-click`:                                                      struct{}{},
		`SDKUpiAppNotFound`:                                                 struct{}{},
		`ad_campaign_video_mute_unmute_tap`:                                 struct{}{},
		`ZTRENDS_CRASH_BOUNDARY_CAPTURE`:                                    struct{}{},
		`StoryViewLoaded`:                                                   struct{}{},
		`order_ready_tap`:                                                   struct{}{},
		`upload_attachment_tap`:                                             struct{}{},
		`res_growth_action_card_impression`:                                 struct{}{},
		`deals_card_type_2_impr_citi`:                                       struct{}{},
		`vendor_api_failure_12_184`:                                         struct{}{},
		`O2CrystalGoldCartCheckout`:                                         struct{}{},
		`MWEB_SEARCH_RESULTS_BANNER_IMPRESSION`:                             struct{}{},
		`SDKPollingPageBackPressed`:                                         struct{}{},
		`logistics-tab-activity-logs`:                                       struct{}{},
		`otp_bottomsheet_resend_tap`:                                        struct{}{},
		`phone_verify_otp_failure`:                                          struct{}{},
		`partner_top_of_the_funnel`:                                         struct{}{},
		`NetworkTransferMetrics`:                                            struct{}{},
		`SignUpSuccess`:                                                     struct{}{},
		`O2MenuSuperOfferTapped`:                                            struct{}{},
		`PaymentPartnerAccordionTapped`:                                     struct{}{},
		`ProPlanPageBuyProTap`:                                              struct{}{},
		`ci_issue_insights_tips_impression`:                                 struct{}{},
		`common_banner_tap`:                                                 struct{}{},
		`get-app-form-input-focus-tracking`:                                 struct{}{},
		`permission_group`:                                                  struct{}{},
		`rain_ddt_blocker_impression`:                                       struct{}{},
		`res_moq_update`:                                                    struct{}{},
		`O2CrystalRefundImpression`:                                         struct{}{},
		`PUCartDismissed`:                                                   struct{}{},
		`02CartAddMoreItemsTapped`:                                          struct{}{},
		`LoginConfigAPIFailure`:                                             struct{}{},
		`menu_item_update_image_tap`:                                        struct{}{},
		`Homepage Visit`:                                                    struct{}{},
		`TRSlotCartPageAddOccasionSubmitted`:                                struct{}{},
		`variant_name_selection_tap`:                                        struct{}{},
		`mx_dining_web_payout_apply_invoices_filter`:                        struct{}{},
		`photo_bar_post_success`:                                            struct{}{},
		`res_ba_update`:                                                     struct{}{},
		`O2CrystalUserSnippetSharingSnippetImpression`:                      struct{}{},
		`LocationMapScreenLocationTextFieldTap`:                             struct{}{},
		`GamificationWillResignActiveNotification`:                          struct{}{},
		`ReviewDraftPopUpLoaded`:                                            struct{}{},
		`ReadMoreTapped`:                                                    struct{}{},
		`console-error-logs`:                                                struct{}{},
		`customer_rating_tap`:                                               struct{}{},
		`gold_special_cart_promo_error`:                                     struct{}{},
		`order_blocker_get_app_form_impression`:                             struct{}{},
		`SignUpStart`:                                                       struct{}{},
		`PhotoSliderPhotoTapped`:                                            struct{}{},
		`ReviewDraftPopUpTapped`:                                            struct{}{},
		`O2GoldPageLoaderAddToCart`:                                         struct{}{},
		`StartJsonApiSuccess`:                                               struct{}{},
		`additional_rider_dismiss`:                                          struct{}{},
		`mx_asset_create`:                                                   struct{}{},
		`live_activities_permission_status`:                                 struct{}{},
		`order_history_screen_shown`:                                        struct{}{},
		`order_refund_close_tap`:                                            struct{}{},
		`res_offline_on_time_back_tap`:                                      struct{}{},
		`AppLaunchGPSLocationFetchedSilently`:                               struct{}{},
		`FullPageAPI`:                                                       struct{}{},
		`CartPaymentFailureViewDismissed`:                                   struct{}{},
		`fetch_location_prompt`:                                             struct{}{},
		`next_click`:                                                        struct{}{},
		`menu_item_tag_off_tap`:                                             struct{}{},
		`order_timelines_detail_tap`:                                        struct{}{},
		`personal_details_submit_tapped`:                                    struct{}{},
		`res_page_menu_image_click`:                                         struct{}{},
		`support_click`:                                                     struct{}{},
		`user_blocked_logging`:                                              struct{}{},
		`rain_ddt_bottom_sheet`:                                             struct{}{},
		`GenericWebviewCTA-roi_20230106`:                                    struct{}{},
		`Dev_image_loading_failure_error`:                                   struct{}{},
		`O2SelectLocationResultTapped`:                                      struct{}{},
		`ZPLSpecialOfferBannerImpression`:                                   struct{}{},
		`hp_partner_merchant_fragment_impression`:                           struct{}{},
		`menu_score_action_add_info_tap`:                                    struct{}{},
		`review_write_back`:                                                 struct{}{},
		`PUCartPageLoaded`:                                                  struct{}{},
		`web-party-event-page-open`:                                         struct{}{},
		`Review_Highlights_Scrolled`:                                        struct{}{},
		`SDKNativeEnterOTPLoaded`:                                           struct{}{},
		`PhoneCurrentLanguage`:                                              struct{}{},
		`order_blocker_impression'`:                                         struct{}{},
		`refresh-call-add-to-queue`:                                         struct{}{},
		`sneak_peek_detail_page_end`:                                        struct{}{},
		`AppLocationConfig`:                                                 struct{}{},
		`customer_rating_impression`:                                        struct{}{},
		`inventory_menu_score_banner_tap`:                                   struct{}{},
		`mx_nps_notification_display`:                                       struct{}{},
		`APP_COLD_START`:                                                    struct{}{},
		`O2CrystalDcPackDismiss`:                                            struct{}{},
		`DealsResBannerImpression`:                                          struct{}{},
		`ProFaqView`:                                                        struct{}{},
		`GenericWebviewCTA-legends_express_calendar_mumbai_veg`:             struct{}{},
		`choose_language_impression`:                                        struct{}{},
		`menu_add_variant_button`:                                           struct{}{},
		`hasSwiggy`:                                                         struct{}{},
		`O2CrystalContactlessDeliveryAcceptedImpression`:                    struct{}{},
		`O2AddItemMenuAddressBlockerImpressionEvent`:                        struct{}{},
		`res_offline_reasons_continue_tap`:                                  struct{}{},
		`footer_app_link_clicked`:                                           struct{}{},
		`max_safety_page_loaded`:                                            struct{}{},
		`mx_dining_web_transaction_confirm_refund_clicked`:                  struct{}{},
		`O2CartTimeSnippetDelayImpression`:                                  struct{}{},
		`arrived_rider_notif_tap`:                                           struct{}{},
		`view_payout_details_click`:                                         struct{}{},
		`UserReviewsImpressions`:                                            struct{}{},
		`grow_your_business_popup_impression`:                               struct{}{},
		`compress_photo_fail`:                                               struct{}{},
		`O2CrystalNDGOrderGeofenceOnTimeBannerTapEvent`:                     struct{}{},
		`SDKCardConsentPretickShown`:                                        struct{}{},
		`LoginThroughMailPageLoaded`:                                        struct{}{},
		`EventPageShareButtonTapped`:                                        struct{}{},
		`zqr_no_app_read`:                                                   struct{}{},
		`contact_details_add_phone`:                                         struct{}{},
		`delivered_orders_see_more_tap`:                                     struct{}{},
		`O2SelectLocationCurrentLocationTapped`:                             struct{}{},
		`event_details_page_views`:                                          struct{}{},
		`jumbo_health_metrics`:                                              struct{}{},
		`locationpermissiondisplay`:                                         struct{}{},
		`faq_v2`:                                                            struct{}{},
		`LOGISTICS_INDEX_EVENT_LOG`:                                         struct{}{},
		`mweb_amp_open_app_order_modal_resinfo_page_impression`:             struct{}{},
		`report_review_submit`:                                              struct{}{},
		`LocationSnappingConfig`:                                            struct{}{},
		`SDKRetryRecommendedPaymentOptionSelected`:                          struct{}{},
		`google_places`:                                                     struct{}{},
		`GoldIntroTopSnippetLostSavingsImpression`:                          struct{}{},
		`ticket_feedback_submit_tap`:                                        struct{}{},
		`sr_event`:                                                          struct{}{},
		`menu_item_delete_image_success`:                                    struct{}{},
		`ResAwardsCategorySwitcherImpression`:                               struct{}{},
		`aerobar_data`:                                                      struct{}{},
		`feedback_recommended_info_button_tapped`:                           struct{}{},
		`digital_contract_sign_creation_failed`:                             struct{}{},
		`mx_dining_web_events_get_contract_api`:                             struct{}{},
		`ci_need_help_raise_ticket`:                                         struct{}{},
		`Dev_image_data_size`:                                               struct{}{},
		`FeedingIndiaPanDetailsTapped`:                                      struct{}{},
		`O2PaymentMethodDetailsEmailEntered`:                                struct{}{},
		`SDKPaymentMethodDetailsNameEntered`:                                struct{}{},
		`O2MenuSearchSuggestionTapped`:                                      struct{}{},
		`order_share_open_app_click`:                                        struct{}{},
		`res_offline_backend_impression`:                                    struct{}{},
		`item_metadata_input_change`:                                        struct{}{},
		`mx_dining_web_menu_click_on_cancel_delete_menu_button`:             struct{}{},
		`O2CrystalCarouselRiderCallStatusTapped`:                            struct{}{},
		`ResSimilarRestaurantsTapped`:                                       struct{}{},
		`TRSlotCartPageAddOccasion`:                                         struct{}{},
		`cuisine_click_homepage`:                                            struct{}{},
		`HttP://bxss.me/t/xss.html?%00`:                                     struct{}{},
		`zfb_reset_tapped`:                                                  struct{}{},
		`O2ClearAllItemsTapped`:                                             struct{}{},
		`LoginModalClick`:                                                   struct{}{},
		`phone_send_otp_tap`:                                                struct{}{},
		`review_bar_post_failed`:                                            struct{}{},
		`webview-dining-venue-picker-our-recommend-page-load`:               struct{}{},
		`SDKSavedPaymentMethodsEditPaymentMethod`:                           struct{}{},
		`SDKRemoveWalletSuccess`:                                            struct{}{},
		`ResDosChatHelpButtonTapped`:                                        struct{}{},
		`menu_photo_picker_result`:                                          struct{}{},
		`JumboEnameO2PaymentMethodDefault`:                                  struct{}{},
		`copy_categories_to_restaurants_click`:                              struct{}{},
		`welcome_carousel_impression`:                                       struct{}{},
		`sample_menu_play_video`:                                            struct{}{},
		`WIFI_FAILED`:                                                       struct{}{},
		`RestaurantPageReviewsPhotoOpened`:                                  struct{}{},
		`Dev_saved_cart_not_clear`:                                          struct{}{},
		`mx_dining_web_transaction_filter_apply_clicked`:                    struct{}{},
		`support_address_input_continue_tap`:                                struct{}{},
		`vendor_api_failure_12_168`:                                         struct{}{},
		`BACKEND_O2MenuFetched`:                                             struct{}{},
		`O2MenuCustomizationEntityTap`:                                      struct{}{},
		`PaybillClicked`:                                                    struct{}{},
		`mweb_amp_open_app_menu_modal_menu_page_outside_click`:              struct{}{},
		`zomarks_bookmark_delete`:                                           struct{}{},
		`O2AppLaunched`:                                                     struct{}{},
		`post_order_primary_cell_expanded_collapsed`:                        struct{}{},
		`media_upload_images`:                                               struct{}{},
		`DashboardBannerImpressionEvent`:                                    struct{}{},
		`open`:                                                              struct{}{},
		`reviews_reply_sent`:                                                struct{}{},
		`tapped_google`:                                                     struct{}{},
		`webview_request_tracking`:                                          struct{}{},
		`tour_finish_click`:                                                 struct{}{},
		`O2SustainabilityLearnMoreTapped`:                                   struct{}{},
		`res_page_similiar_restaurant_impression`:                           struct{}{},
		`PUMenuMoreTapped`:                                                  struct{}{},
		`delayed_order_clicked`:                                             struct{}{},
		`rider-location-fetch`:                                              struct{}{},
		`settlement_utr_data`:                                               struct{}{},
		`get_challenge`:                                                     struct{}{},
		`Dev_menu_page_opened_for_each_res_id`:                              struct{}{},
		`gold_special_cart_feeding_india_seen`:                              struct{}{},
		`landing_page_event_tickets_benefit_impression`:                     struct{}{},
		`ad_payment_cancel_popup_impression`:                                struct{}{},
		`reviews_date_filtered`:                                             struct{}{},
		`crystalSwitchBannerShown`:                                          struct{}{},
		`GenericWebviewCTA-Awads_20230319`:                                  struct{}{},
		`ListingParseCuration1Starts`:                                       struct{}{},
		`ci_issue_alert_tap`:                                                struct{}{},
		`collection_impression`:                                             struct{}{},
		`hp_partner_login_success`:                                          struct{}{},
		`OrderSummaryRefundDetailsShown`:                                    struct{}{},
		`GenericWebviewCTA-ROI_20230316`:                                    struct{}{},
		`O2CrystalRiderChatButtonTappedV16`:                                 struct{}{},
		`O2MenuFooterAdServed`:                                              struct{}{},
		`remote_config_activate_success`:                                    struct{}{},
		`digital_contract_bank_details_save_success`:                        struct{}{},
		`badges_infographic_banner_viewed`:                                  struct{}{},
		`mx_dining_web_events_add_update_event_api`:                         struct{}{},
		`se_ticket_cancelled`:                                               struct{}{},
		`deeplink_grofers`:                                                  struct{}{},
		`PromoPopupDismissed`:                                               struct{}{},
		`GoldCartLoaded`:                                                    struct{}{},
		`fcm_token_reg_success`:                                             struct{}{},
		`charges_page_load`:                                                 struct{}{},
		`new_user_free_dc`:                                                  struct{}{},
		`help_question_expand`:                                              struct{}{},
		`zqr_app_scan_init`:                                                 struct{}{},
		`api_http_error`:                                                    struct{}{},
		`feedback_dish_item_rating_tapped`:                                  struct{}{},
		`menu_item_add_click`:                                               struct{}{},
		`error-debug-log`:                                                   struct{}{},
		`LocationMapTemplateScreenImpression`:                               struct{}{},
		`O2CrystalRiderContactBottomSheetEditInstructionsButtonTapped`:      struct{}{},
		`auth_logout_fail`:                                                  struct{}{},
		`O2AddAddressDSZTapped`:                                             struct{}{},
		`user_wifi_status`:                                                  struct{}{},
		`O2SharingCrystalOFSEBannerTapped`:                                  struct{}{},
		`support_schedule_appointment_tap`:                                  struct{}{},
		`karma_compute_ignore`:                                              struct{}{},
		`signup_success`:                                                    struct{}{},
		`O2_ORDER_SERVICEABILITY-0`:                                         struct{}{},
		`MENU_RENDERED`:                                                     struct{}{},
		`deeplink_pv`:                                                       struct{}{},
		`menu_bulk_taxes_submit_button`:                                     struct{}{},
		`ownership-transfer/accepted-by-govid/ticket-updated`:               struct{}{},
		`O2RestaurantCallScreenOrderButtonTapped`:                           struct{}{},
		`FOOTER_DROPDOWN_SELECTION_EVENT`:                                   struct{}{},
		`O2RepeatButtonTapped`:                                              struct{}{},
		`link_account_impression`:                                           struct{}{},
		`epicentre_header_tap`:                                              struct{}{},
		`webview-event-details-get-event-details-api-success`:               struct{}{},
		`collection_action`:                                                 struct{}{},
		`SKUPayingForTapped`:                                                struct{}{},
		`delete-dining-mx-res-contact`:                                      struct{}{},
		`zomarks_collection_delete`:                                         struct{}{},
		`listingResultsFetchFailed`:                                         struct{}{},
		`O2CrystalAppUpdateSuccess`:                                         struct{}{},
		`SDKRecacheFailure`:                                                 struct{}{},
		`edition_card_impression`:                                           struct{}{},
		`deeplink_page_type=dineout_home`:                                   struct{}{},
		`visitspack_serving`:                                                struct{}{},
		`ownership-transfer/accepted-by-govid/merchant-res-mapping-success`: struct{}{},
		`VOIP_EVENTS`:                                              struct{}{},
		`ci_learn_more`:                                            struct{}{},
		`click_action`:                                             struct{}{},
		`listing_offers_tapped`:                                    struct{}{},
		`reject_screen_close_tap`:                                  struct{}{},
		`res_growth_banner_tap`:                                    struct{}{},
		`deals_card_type_2_impr_mandiri`:                           struct{}{},
		`hyperpure_footer_link`:                                    struct{}{},
		`res_md_contacts_update`:                                   struct{}{},
		`O2CrystalPageViewed`:                                      struct{}{},
		`SDKAddCardScreenOpened`:                                   struct{}{},
		`CRYSTAL_FRAGMENT_RESUMED`:                                 struct{}{},
		`FoodMenuPicTapped`:                                        struct{}{},
		`ads_relevance_ltr_sort`:                                   struct{}{},
		`instruction_reject_confirm_click`:                         struct{}{},
		`menu_out_of_stock`:                                        struct{}{},
		`order-tile-rendered`:                                      struct{}{},
		`verify_email_with_otp`:                                    struct{}{},
		`O2CrystalNDGPerceptionSnippetImpression`:                  struct{}{},
		`O2CrystalRiderSafetyBannerImpression`:                     struct{}{},
		`O2MenuRailDishRemoved`:                                    struct{}{},
		`ChatBundleDownloaded`:                                     struct{}{},
		`link_account_user_selection`:                              struct{}{},
		`le-transfer/res-detail-click`:                             struct{}{},
		`socket_connect_called`:                                    struct{}{},
		`mqtt_disconnect_success`:                                  struct{}{},
		`APP_DEEPLINKS_UNSUPPORTED_PARAMS`:                         struct{}{},
		`O2CrystalAudioInstructionTourImpression`:                  struct{}{},
		`PinningKeyFetchDenied`:                                    struct{}{},
		`inaccurate_cache_used`:                                    struct{}{},
		`apply_search_filters`:                                     struct{}{},
		`O2CrystalRiderValetSnippetCallButtonTapped`:               struct{}{},
		`LoginConsentAllowButtonTap`:                               struct{}{},
		`hp_login_signup_tap`:                                      struct{}{},
		`mx_growth_appointment_details`:                            struct{}{},
		`asset-lottie-impression`:                                  struct{}{},
		`photo_bar_post_failed`:                                    struct{}{},
		`LocationOSPromptShown`:                                    struct{}{},
		`HomeVideoMuteTapped`:                                      struct{}{},
		`mweb_amp_open_app_menu_modal_resinfo_page_open_click`:     struct{}{},
		`tapped_more_login`:                                        struct{}{},
		`new_order_history_download_polling_api_failure`:           struct{}{},
		`restaurant_action_object_click`:                           struct{}{},
		`LocationMapScreenGPSPromptTapped`:                         struct{}{},
		`MenuCustomizationLoaded`:                                  struct{}{},
		`geocode_fetched_from_api`:                                 struct{}{},
		`OpeningHoursTapped`:                                       struct{}{},
		`O2TaxesAndChargesPopUpShown`:                              struct{}{},
		`O2CartSustainabilityContributionRemoveButton`:             struct{}{},
		`O2CrystalDeliveryInstructionSnippetImpression`:            struct{}{},
		`O2CrystalResRatingImpression`:                             struct{}{},
		`BrandPageResClicked`:                                      struct{}{},
		`zc_new_cart_action`:                                       struct{}{},
		`TRSlotCartPageEditDetailsTapped`:                          struct{}{},
		`bxss.me/t/xss.html?%00`:                                   struct{}{},
		`outlet_name_address_edit_confirm_tap`:                     struct{}{},
		`ZomatoWebSignupPageTap''`:                                 struct{}{},
		`O2CrystalWhatsappPromoOptinBannerTapped`:                  struct{}{},
		`mx_dining_web_get_stories_api_request_success`:            struct{}{},
		`AppGPSLocationEvents`:                                     struct{}{},
		`O2_ORDER_SERVICEABILITY`:                                  struct{}{},
		`convince_screen_reject_tap`:                               struct{}{},
		`menu_score_banner_impression`:                             struct{}{},
		`mx-login-new-error`:                                       struct{}{},
		`side_drawer_view`:                                         struct{}{},
		`step_3_impression_event`:                                  struct{}{},
		`mx_dining_web_view_story_guidelines_clicked`:              struct{}{},
		`mx_dining_web_request_vibe_shoot`:                         struct{}{},
		`ad_campaign_create_budget_slider_selected`:                struct{}{},
		`media_repo_delete_images`:                                 struct{}{},
		`old_ticket_tap`:                                           struct{}{},
		`zfbMobileMappingVerifyClick`:                              struct{}{},
		`CreationReviewPageLoad`:                                   struct{}{},
		`GoldO2CartSnippetTap`:                                     struct{}{},
		`ScratchCardRewardSnippetTapped`:                           struct{}{},
		`everyday_auto_print_called`:                               struct{}{},
		`photo_guideline_screen_next_tap`:                          struct{}{},
		`review_commented`:                                         struct{}{},
		`O2CartAbandonTapped`:                                      struct{}{},
		`custom-redirect-sumanbatra`:                               struct{}{},
		`NO_CONTENT_VIEW_ERROR_SHOWN`:                              struct{}{},
		`crystal_blocker_data`:                                     struct{}{},
		`location_diy_how_to_get_maps_url_tap`:                     struct{}{},
		`webview-event-details-native-share-via-success`:           struct{}{},
		`SDKNativeOtpIntentNotValid`:                               struct{}{},
		`O2DeliveryInstructionAudioInstructionDeleteTapped`:        struct{}{},
		`PROMO_SWITCH_POPUP`:                                       struct{}{},
		`support_feedback_dp_issues`:                               struct{}{},
		`zpay_scratch_banner_impression`:                           struct{}{},
		`prefered-pos-page-impression`:                             struct{}{},
		`delete_timings_confirm`:                                   struct{}{},
		`gold_history_chat_tap`:                                    struct{}{},
		`GenericWebviewCTA-3_anniversary_push`:                     struct{}{},
		`GoldIntroFAQImpression`:                                   struct{}{},
		`ResEventClicked`:                                          struct{}{},
		`O2MenuFetched`:                                            struct{}{},
		`O2MenuSearchSuggestionPillTapped`:                         struct{}{},
		`O2CrystalDIYCancellationInfoSnippetImpressionEvent`:       struct{}{},
		`address_detection_from_cache`:                             struct{}{},
		`SDKRemoveVpaFailure`:                                      struct{}{},
		`ps-insertion-success`:                                     struct{}{},
		`ownership-transfer/send-contract/failed`:                  struct{}{},
		`IS_DARK_MODE_ENABLED`:                                     struct{}{},
		`TAB_RENDERED`:                                             struct{}{},
		`feedback_order_review_tapped`:                             struct{}{},
		`mweb_amp_open_app_menu_modal_resinfo_page_continue_click`: struct{}{},
		`mx_notification_impression`:                               struct{}{},
		`review_search_bar_tapped`:                                 struct{}{},
		`zwallet_screen_loaded`:                                    struct{}{},
		`ad_campaign_contract_tap`:                                 struct{}{},
		`ResPageCarnivalSlotSectionImpression`:                     struct{}{},
		`PUMenuReviewTapped`:                                       struct{}{},
		`deals_card_type_2_impr_bca`:                               struct{}{},
		`SearchLocationClosedTapped`:                               struct{}{},
		`date_range_tap`:                                           struct{}{},
		`o2_home_banners`:                                          struct{}{},
		`review_write_rating`:                                      struct{}{},
		`PUMenuRepeatLastCustomizationTapped`:                      struct{}{},
		`O2CrystalPipPermissionEvent`:                              struct{}{},
		`LocationAddAddressButtonImpression`:                       struct{}{},
		`StoryCollectionClicked`:                                   struct{}{},
		`auto_start_click`:                                         struct{}{},
		`reject_oos_call_customer`:                                 struct{}{},
		`mx_dining_web_insights_revenue_and_transaction_summary_api_success_failure`: struct{}{},
		`mx_rush_hour_event_received`:                    struct{}{},
		`reporting_shown`:                                struct{}{},
		`transactions_order_detail_tap`:                  struct{}{},
		`GoldIntroContainerSnippetLostSavingsImpression`: struct{}{},
		`LocationMapScreenPinMoved`:                      struct{}{},
		`SearchLocationSearchFailed`:                     struct{}{},
		`O2AddAddressClosed`:                             struct{}{},
		`ZDBManagerRequestInitiated`:                     struct{}{},
		`restaurant_pickup_coordinates_updation_failiure_due_to_locality_issues`: struct{}{},
		`O2OFSEGiftingImageImpression`:                                           struct{}{},
		`PhotosLegacyCodeStillInUse`:                                             struct{}{},
		`StoryEstimatedBandwidth`:                                                struct{}{},
		`api_switch_deeplink_intent`:                                             struct{}{},
		`mx_res_offline_ticket`:                                                  struct{}{},
		`mx_dining_web_events_click_on_event_listing_buttons`:                    struct{}{},
		`LocationMapScreenAPIFailed`:                                             struct{}{},
		`event_menu_tab_tap`:                                                     struct{}{},
		`alert_tone_changed`:                                                     struct{}{},
		`payments_token_call_started`:                                            struct{}{},
		`mx_dining_web_menu_delete_menu_api_request_success`:                     struct{}{},
		`email_login_tap`:                                                        struct{}{},
		`webview-april-fools-2022-cta-share-message-click`:                       struct{}{},
		`GoldMembershipHelpTap`:                                                  struct{}{},
		`PARSING_EXCEPTION`:                                                      struct{}{},
		`tapped_login_continue`:                                                  struct{}{},
		`ci_no_refund`:                                                           struct{}{},
		`O2CrystalPayOnlinePaymentMethodTapped`:                                  struct{}{},
		`HomeVideoCrossTapped`:                                                   struct{}{},
		`PriorityDeliveryStatusOnOrderPlaced`:                                    struct{}{},
		`reviews_card_actioned`:                                                  struct{}{},
		`soa_feedback_submitted`:                                                 struct{}{},
		`multiple_rejection_turn_on_hash`:                                        struct{}{},
		`O2CrystalIntercityBannerImpression`:                                     struct{}{},
		`SDKPaymentOptionsRemoveVPATapped`:                                       struct{}{},
		`SearchLocationAddAddressTapped`:                                         struct{}{},
		`CrystalPayLaterPaymentStatus`:                                           struct{}{},
		`GoldCartPaymentFailed`:                                                  struct{}{},
		`menu_detailpage_image_icon_click`:                                       struct{}{},
		`new_order_strip_tap`:                                                    struct{}{},
		`O2AddAddressCompleteAddressTapped`:                                      struct{}{},
		`GnssInfo`:                                                               struct{}{},
		`mx_onboarding_feedback_rating`:                                          struct{}{},
		`deeplink_sendEmail`:                                                     struct{}{},
		`SDKPromoPageAddPaymentMethodBackTapped`:                                 struct{}{},
		`save-address-wrong-address-rejection`:                                   struct{}{},
		`StoryReportIcon`:                                                        struct{}{},
		`o2_suggestions_rendered`:                                                struct{}{},
		`bookmark_clicked`:                                                       struct{}{},
		`diy-onboarding-make-payment-click`:                                      struct{}{},
		`PUMenuSuperOfferTapped`:                                                 struct{}{},
		`GoldThankYouPageViewed`:                                                 struct{}{},
		`O2PhoneVerficationEnd`:                                                  struct{}{},
		`edition_wallet_tap`:                                                     struct{}{},
		`hp_onboarded_impression`:                                                struct{}{},
		`locality_mov_logs`:                                                      struct{}{},
		`order_search_input`:                                                     struct{}{},
		`search_tap`:                                                             struct{}{},
		`click_on_searched_deeplink`:                                             struct{}{},
		`PromoPageSavings`:                                                       struct{}{},
		`default_settings_selected`:                                              struct{}{},
		`inventory_all_items_tab_tap`:                                            struct{}{},
		`using_splash_location`:                                                  struct{}{},
		`fssai_web_registration_tap`:                                             struct{}{},
		`ThirdPartyAPIs\PierianPANGST\PierianApiCaller_error`:                    struct{}{},
		`mx_dining_web_login_from_instagram_success`:                             struct{}{},
		`dotd_ticket_purchase`:                                                   struct{}{},
		`ListingParseCuration1Else`:                                              struct{}{},
		`rainy_weather_data`:                                                     struct{}{},
		`rejected_orders_cards_impression`:                                       struct{}{},
		`phone_res_search_tap`:                                                   struct{}{},
		`O2HomeSearchNoResultsShown`:                                             struct{}{},
		`O2PostOrderCartNotDeliverReasonSnippetImpression`:                       struct{}{},
		`O2OFSECallbackSupportTapped`:                                            struct{}{},
		`O2MenuRailShown`:                                                        struct{}{},
		`collection_res_click`:                                                   struct{}{},
		`O2MenuVideoStatusChanged`:                                               struct{}{},
		`mx_dining_web_help_center_click_on_confirm_btn`:                         struct{}{},
		`refund_order_request_received`:                                          struct{}{},
		`PUDishAdded`:                                                            struct{}{},
		`order_dispatched_tap`:                                                   struct{}{},
		`rain_signal_continuity`:                                                 struct{}{},
		`TRSlotCartPageEditSlotTapped`:                                           struct{}{},
		`TRSlotBookingFailure`:                                                   struct{}{},
		`O2CrystalDominosStoreOrderNumber`:                                       struct{}{},
		`O2CrystalNDGOrderNotApplicableBanner`:                                   struct{}{},
		`O2FreebieSnackbarTap`:                                                   struct{}{},
		`DLOS_UNIFIED_EVENT_LOG`:                                                 struct{}{},
		`login-click`:                                                            struct{}{},
		`mweb_amp_open_app_menu_modal_resinfo_page_impression`:                   struct{}{},
		`ResAboutUsPageLoaded`:                                                   struct{}{},
		`deeplink_bookmarks`:                                                     struct{}{},
		`deeplink_www.zomato.com`:                                                struct{}{},
		`SDKPaymentMethodDetailsEmailEntered`:                                    struct{}{},
		`FeedingIndiaThankyouPopupDismissal`:                                     struct{}{},
		`menu_item_tag_on_tap`:                                                   struct{}{},
		`review_filter_pill_tapped`:                                              struct{}{},
		`TRSlotCartPageSpecialRequestSubmitted`:                                  struct{}{},
		`pickup_lat_lng_updates`:                                                 struct{}{},
		`O2MenuCakeSpillHeader`:                                                  struct{}{},
		`SearchLocationSearchBarTapped`:                                          struct{}{},
		`ResContactTapped`:                                                       struct{}{},
		`fssai_update_screen_impression`:                                         struct{}{},
		`gallery_tap`:                                                            struct{}{},
		`compress_photo_success`:                                                 struct{}{},
		`play_services_na`:                                                       struct{}{},
		`O2AddAddressFromCart`:                                                   struct{}{},
		`phone_resend_otp_success`:                                               struct{}{},
		`O2CartViewOffersTapped`:                                                 struct{}{},
		`SDKBankOptionsBackPressed`:                                              struct{}{},
		`GoldIntermediateMilestonePopupImpression`:                               struct{}{},
		`O2MenuCuratedItemsCardImpression`:                                       struct{}{},
		`ad_alert_off_schedule_configured`:                                       struct{}{},
		`support_actionable_item_click`:                                          struct{}{},
		`O2CrystaleExplorerTriggerEvent`:                                         struct{}{},
		`onboarding_back_button_tapped`:                                          struct{}{},
		`swipe_home_page`:                                                        struct{}{},
		`Backend_O2CartPromoApplied`:                                             struct{}{},
		`EDIT_EMAIL_FLOW_EDIT_EMAIL_BUTTON_CLICKED`:                              struct{}{},
		`PARSING_EXCEPTION_JSON_LOGGING`:                                         struct{}{},
		`PostDeliveryNumberMasking`:                                              struct{}{},
		`ZPLVideoQuestionOptionTapped`:                                           struct{}{},
		`O2CrystalCNRDeliveryAcceptedImpression`:                                 struct{}{},
		`O2SavedPaymentMethodsLoaded`:                                            struct{}{},
		`opened_app`:                                                             struct{}{},
		`sidebar_item_impression`:                                                struct{}{},
		`web_current_device_location_allow`:                                      struct{}{},
		`TRSlotBookingPageLoaded`:                                                struct{}{},
		`JumboEnameO2FabClicked`:                                                 struct{}{},
		`O2CrystalBlinkitScratchCardSnippetImpressionEvent`:                      struct{}{},
		`CRYSTAL_ON_MAP_LOADED_CALLBACK_RECEIVED`:                                struct{}{},
		`ReferButtonTapped`:                                                      struct{}{},
		`collection_home`:                                                        struct{}{},
		`digital_contract_open_failure`:                                          struct{}{},
		`page_dropdown_tap`:                                                      struct{}{},
		`OSM_NONLOGS_ORDER_HISTORY_RESPONSE`:                                     struct{}{},
		`ResPageGetDirectionsTapped`:                                             struct{}{},
		`langOption`:                                                             struct{}{},
		`ZPLInfoTileImpression`:                                                  struct{}{},
		`mx_reporting_tooltip_impression`:                                        struct{}{},
		`ResFetauresClosed`:                                                      struct{}{},
		`outlet-location-diy-back-tap`:                                           struct{}{},
		`mx_dining_web_your_customers_customer_details_modal_open`:               struct{}{},
		`FilterModalLoaded`:                                                      struct{}{},
		`one-support-chat-access-token-set`:                                      struct{}{},
		`menu_contact_am_to_add_photo`:                                           struct{}{},
		`O2MenuFooterAdTap`:                                                      struct{}{},
		`gccart_shown`:                                                           struct{}{},
		`O2CrystalNotificationCardImpression`:                                    struct{}{},
		`ResOtherOutletsNearbyTapped`:                                            struct{}{},
		`StoryRestaurantViewed`:                                                  struct{}{},
		`SDKAddCardExpiryEntered`:                                                struct{}{},
		`gold_special_cart_capsule_start_typing`:                                 struct{}{},
		`offer_config_overlapping_impression`:                                    struct{}{},
		`review_successful_with_photo`:                                           struct{}{},
		`ResPageCarnivalSlotBookNowTapped`:                                       struct{}{},
		`support_email_id_click`:                                                 struct{}{},
		`BmCollectionAddPlacesAction`:                                            struct{}{},
		`add_category_cancel`:                                                    struct{}{},
		`common_banner_cancel_clicked`:                                           struct{}{},
		`gclanding_banner`:                                                       struct{}{},
		`O2ExtrasPaymentMethodUpdated`:                                           struct{}{},
		`vendor_api_failure_12_289`:                                              struct{}{},
		`vendor_api_failure_12_12`:                                               struct{}{},
		`item_stock_toggle`:                                                      struct{}{},
		`gold_special_cart_zcredit_applied`:                                      struct{}{},
		`make_order_ndg_inapplicable`:                                            struct{}{},
		`ad_payment_cancel_popup_no_tap`:                                         struct{}{},
		`mqtt_connect_complete`:                                                  struct{}{},
		`O2CartTipBannerViewed`:                                                  struct{}{},
		`GameCardImpression`:                                                     struct{}{},
		`jumbo_infinite_loading_appeared`:                                        struct{}{},
		`mx_order_history_app`:                                                   struct{}{},
		`edit_phone_number_tapped`:                                               struct{}{},
		`PinningKeyFetchAllowed`:                                                 struct{}{},
		`ViewCartOnMenu`:                                                         struct{}{},
		`copy_categories_to_restaurants_next`:                                    struct{}{},
		`GoldMembershipPageSuccessImpression`:                                    struct{}{},
		`SDKLinkWalletSuccess`:                                                   struct{}{},
		`mapp_diy_timings_update`:                                                struct{}{},
		`res_page_similar_restaurant_tapped`:                                     struct{}{},
		`HomeLocationRequest`:                                                    struct{}{},
		`amp_app_pitch_bottom_bar_impresssion`:                                   struct{}{},
		`CallRestaurantWithOptionsFromRestaurantPage`:                            struct{}{},
		`track_performance_tab_tap`:                                              struct{}{},
		`AppExitMetrics`:                                                         struct{}{},
		`Dev_AppAPIRetryStart`:                                                   struct{}{},
		`O2PickupNotifTriggered`:                                                 struct{}{},
		`ResFeaturesTapped`:                                                      struct{}{},
		`res_delivery_charge_update`:                                             struct{}{},
		`O2ResClick`:                                                             struct{}{},
		`ResReviewsImpressions`:                                                  struct{}{},
		`PayForBookedSlotBannerClicked`:                                          struct{}{},
		`new_web_performance_metric''`:                                           struct{}{},
		`empty_city_or_subzone`:                                                  struct{}{},
		`res_mov_get_logs`:                                                       struct{}{},
		`TRSlotBookingPageBookSlotTapped`:                                        struct{}{},
		`contact_selector_redirect_click`:                                        struct{}{},
		`media_repo_map_images`:                                                  struct{}{},
		`res_page_render_log`:                                                    struct{}{},
		`mx_refund_pre_tcs_tds`:                                                  struct{}{},
		`ZPLVideoQuestionOptionImpression`:                                       struct{}{},
		`auth_token_invalidated`:                                                 struct{}{},
		`owner_hub_home_tab_tap`:                                                 struct{}{},
		`select_photo_screen_impression`:                                         struct{}{},
		`restaurant_selection_screen`:                                            struct{}{},
		`deeplink_badges`:                                                        struct{}{},
		`vendor_api_failure_12_16`:                                               struct{}{},
		`qc_image_bulk_action_moderation`:                                        struct{}{},
		`SDKPaymentOptionsOpenBankSelection`:                                     struct{}{},
		`V2O2MenuViewed`:                                                         struct{}{},
		`mx_bot_flow_issue_submit`:                                               struct{}{},
		`digital_contract_sign_creation_fetch_pan_error`:                         struct{}{},
		`SDKPaymentMethodWebViewLoaded`:                                          struct{}{},
		`ci_order_history_view_details`:                                          struct{}{},
		`consumer_info_dsz_info`:                                                 struct{}{},
		`LoginConsentApiActionFailed`:                                            struct{}{},
		`fssai_update_raise_ticket`:                                              struct{}{},
		`mx_bank_diy`:                                                            struct{}{},
		`contact_details_edit_photo`:                                             struct{}{},
		`jailbreak_root_tracking`:                                                struct{}{},
		`O2Home`:                                                                 struct{}{},
		`deeplink_nearby`:                                                        struct{}{},
		`SearchLocationCurrentLocationTapped`:                                    struct{}{},
		`appsflyer_onelink_linking`:                                              struct{}{},
		`menu_category_toggle_stock`:                                             struct{}{},
		`gstin_list_item_selected`:                                               struct{}{},
		`O2MenuReportIssueTap`:                                                   struct{}{},
		`hp_initialization`:                                                      struct{}{},
		`lat_lng_fail`:                                                           struct{}{},
		`o2_home_screen_impression`:                                              struct{}{},
		`mweb_review_modal_click_action`:                                         struct{}{},
		`upgrade-flow-redirect`:                                                  struct{}{},
		`O2_HOME_PARSEDV2`:                                                       struct{}{},
		`o2_dsz_tracking`:                                                        struct{}{},
		`AODApiStatus`:                                                           struct{}{},
		`claim_now_click`:                                                        struct{}{},
		`mweb_amp_open_app_modal_v2_outside_click`:                               struct{}{},
		`HomeSearchClick`:                                                        struct{}{},
		`GoldBottomSheetImpression`:                                              struct{}{},
		`EmailLoginFeedBlinkitInstallTapped`:                                     struct{}{},
		`O2DropPinMapLoaded`:                                                     struct{}{},
		`listingsDataFetched`:                                                    struct{}{},
		`invite_page_error`:                                                      struct{}{},
		`GATEWAY_IP_FAILED`:                                                      struct{}{},
		`PROMO_SERVED`:                                                           struct{}{},
		`menu_image_add_button`:                                                  struct{}{},
		`res_status_tab_switch`:                                                  struct{}{},
		`toggle_admin_status_ba`:                                                 struct{}{},
		`rejection_v2_reject_tap`:                                                struct{}{},
		`custom_offers_ui_element_impression`:                                    struct{}{},
		`product_env_banner_highlight_click`:                                     struct{}{},
		`O2MenuSustainabilityBannerImpression`:                                   struct{}{},
		`ResAwardsWinnerPageLoaded`:                                              struct{}{},
		`stockout_locality_mov`:                                                  struct{}{},
		`show_more_items_tap`:                                                    struct{}{},
		`zerp_ab`:                                                                struct{}{},
		`O2CrystalRiderContactBottomSheetPrimaryImpression`:                      struct{}{},
		`O2CrystalWhatsappOptInSnippetCrossButtonTapped`:                         struct{}{},
		`O2MenuFabCatExpandTap`:                                                  struct{}{},
		`menu_res`:                                                               struct{}{},
		`socket_imp_order_update`:                                                struct{}{},
		`SSL_PINNING_FAILURE`:                                                    struct{}{},
		`menu_bar_filter`:                                                        struct{}{},
		`SDKRetrySelectOtherPaymentMethodTapped`:                                 struct{}{},
		`GoldCartPageOpen`:                                                       struct{}{},
		`menu_recommended_items`:                                                 struct{}{},
		`mweb_open_app_modal_v3_open_click`:                                      struct{}{},
		`user_flow`:                                                              struct{}{},
		`assign_rider_tap`:                                                       struct{}{},
		`prefered-pos-main-api-error`:                                            struct{}{},
		`location_audio_tap`:                                                     struct{}{},
		`OrderCartLoaded`:                                                        struct{}{},
		`feature_toggle_requests`:                                                struct{}{},
		`js_set_data`:                                                            struct{}{},
		`O2CrystalRiderContactBottomSheetInstructionsImpression`:                 struct{}{},
		`menu_add_sub_category_button`:                                           struct{}{},
		`menu_image_score_opened`:                                                struct{}{},
		`react_18_error`:                                                         struct{}{},
		`O2ItemAddMaxQuantityReached`:                                            struct{}{},
		`aerobar_edit_email_impression`:                                          struct{}{},
		`ResendOtpFail`:                                                          struct{}{},
		`O2QuickFiltersMosaicImpression`:                                         struct{}{},
		`res_delivery_offer_imp'`:                                                struct{}{},
		`review_comment`:                                                         struct{}{},
		`O2CrystalResCallTapped`:                                                 struct{}{},
		`FilterRailSelected`:                                                     struct{}{},
		`GoldIntroSectionImpression`:                                             struct{}{},
		`ci_feedback_submit`:                                                     struct{}{},
		`impression_ads_preview_tap`:                                             struct{}{},
		`web_universal_search_api_error`:                                         struct{}{},
		`ticket_reopen_tap`:                                                      struct{}{},
		`rejected_order_trends_tab_click`:                                        struct{}{},
		`Call_Restaurant`:                                                        struct{}{},
		`PackagesCartLoaded`:                                                     struct{}{},
		`deeplink_handle`:                                                        struct{}{},
		`mx_nps_submission_tags`:                                                 struct{}{},
		`FeedingIndiaThankyouPopupImpression`:                                    struct{}{},
		`O2CrystalRiderAssignmentSnippetTap`:                                     struct{}{},
		`O2CartChargesPopUpShown`:                                                struct{}{},
		`ResSponsorSectionTapped`:                                                struct{}{},
		`banner_v2`:                                                              struct{}{},
		`contact_details_tap`:                                                    struct{}{},
		`deliveryOrderResponse`:                                                  struct{}{},
		`call_for_order_accepted`:                                                struct{}{},
		`is_new_order_history_download_cancelled`:                                struct{}{},
		`tapped_home_page_search_button`:                                         struct{}{},
		`AODTaskCancelled`:                                                       struct{}{},
		`AppRuntimeMetrics`:                                                      struct{}{},
		`NETWORK_REQUEST_FLOW`:                                                   struct{}{},
		`O2CrystalCarouselIntercityTap`:                                          struct{}{},
		`register_your_restaurant_click`:                                         struct{}{},
		`salesforce_tickets_meta`:                                                struct{}{},
		`zfb-banner-impressions`:                                                 struct{}{},
		`O2CrystalExplorerViewCouponsButtonTapEvent`:                             struct{}{},
		`web_universal_search_api_error-0`:                                       struct{}{},
		`LoginStart`:                                                             struct{}{},
		`SDKNoCVVAnimation`:                                                      struct{}{},
		`BmCollectionRestaurantBottomSheetAction`:                                struct{}{},
		`CartConfirmationViewCancelled`:                                          struct{}{},
		`gift_card_balance_added_popup_impression`:                               struct{}{},
		`multi-package-package-selection-prepaid-payment-click`:                  struct{}{},
		`order_share_rider_call_placed`:                                          struct{}{},
		`menu_bulk_charges_submit_button`:                                        struct{}{},
		`O2CrystalDIYPageOpen`:                                                   struct{}{},
		`O2CrystalRunnrTipPaymentMethodChanged`:                                  struct{}{},
		`O2MenuItemReadMoreTapped`:                                               struct{}{},
		`TAB_PARSED`:                                                             struct{}{},
		`claim_gift_card_code_bottom_sheet_impression`:                           struct{}{},
		`loginAdid`:                                                              struct{}{},
		`menu_add_sub_category_confirm_click`:                                    struct{}{},
		`mx_reporting_widget_impression`:                                         struct{}{},
		`webview-event-details-click-on-buy-ticket`:                              struct{}{},
		`zwallet_onboarding_form_loaded`:                                         struct{}{},
		`ZPLCrystalPageBannerTapped`:                                             struct{}{},
		`PUMenuItemImageClicked`:                                                 struct{}{},
		`AUTH_SDK_LOGIN_INITIATED`:                                               struct{}{},
		`ReviewsSearch_NoResults`:                                                struct{}{},
		`add_phone_notif_tap`:                                                    struct{}{},
		`GenericWebviewCTA-Telangana_0414`:                                       struct{}{},
		`RemoveGeofenceFailed`:                                                   struct{}{},
		`GamificationServerSyncFailed`:                                           struct{}{},
		`UserReviewdImpressions`:                                                 struct{}{},
		`menu_delete_category_confirm_button`:                                    struct{}{},
		`menu_score_calculated_impression`:                                       struct{}{},
		`mx_nps_dismiss_confirm`:                                                 struct{}{},
		`contact_details_edit_phone_initiate`:                                    struct{}{},
		`mx_dining_web_help_center_click_on_get_email_card`:                      struct{}{},
		`signup_intent`:                                                          struct{}{},
		`email_not_registered_continue_tap`:                                      struct{}{},
		`init_sdk_wait_completed`:                                                struct{}{},
		`is_new_order_history_download_clicked`:                                  struct{}{},
		`ad_campaign_billing_confirm_tap`:                                        struct{}{},
		`O2EnterCVVSubmitTapped`:                                                 struct{}{},
		`penalized_user`:                                                         struct{}{},
		`O2CartFullLoaderShown`:                                                  struct{}{},
		`vendor_api_failure_12_137`:                                              struct{}{},
		`ZomatoWebSignupPageLoaded''`:                                            struct{}{},
		`O2CartInstructionContactlessImpressionEvent`:                            struct{}{},
		`SDKWalletLinkingBackTapped`:                                             struct{}{},
		`V2HOMEPAGE_FAIL`:                                                        struct{}{},
		`ad_alert_off_tap`:                                                       struct{}{},
		`add_item_cancel`:                                                        struct{}{},
		`O2Sale`:                                                                 struct{}{},
		`hard_app_updates`:                                                       struct{}{},
		`menu_item_dismiss_tap`:                                                  struct{}{},
		`menu_booster_pack_read_tnc_tap`:                                         struct{}{},
		`AppIncorrectLocalization`:                                               struct{}{},
		`O2CartOfferSnippetImpression`:                                           struct{}{},
		`SDKphotoLoadTimes`:                                                      struct{}{},
		`inventory_item_toggle_tap`:                                              struct{}{},
		`valentines-2023`:                                                        struct{}{},
		`res_page_single_review_user_image_click'`:                               struct{}{},
		`O2MenuBrandSwitcherTapped`:                                              struct{}{},
		`deeplink_order_history`:                                                 struct{}{},
		`fcm_registration_logs`:                                                  struct{}{},
		`EVENT_TICKET_CONFIRMATION_WHATSAPP_BOT_SENT`:                            struct{}{},
		`api_signup_intent`:                                                      struct{}{},
		`init_sdk_completed_called`:                                              struct{}{},
		`BackButtonTapped`:                                                       struct{}{},
		`GoldIntroNotifyMeButtonTap`:                                             struct{}{},
		`UntrackedDeeplinks`:                                                     struct{}{},
		`support_faq_click`:                                                      struct{}{},
		`mx_dining_web_events_click_on_view_event_details`:                       struct{}{},
		`ResPageReviewClicked`:                                                   struct{}{},
		`CreditDetailsClicked`:                                                   struct{}{},
		`O2MenuCategoryImpression`:                                               struct{}{},
		`appsflyer_installconversion`:                                            struct{}{},
		`call_rider_tap`:                                                         struct{}{},
		`locality_dc_surge_logs`:                                                 struct{}{},
		`catalogue_header_service_change`:                                        struct{}{},
		`collections_click_action`:                                               struct{}{},
		`app_clone_warning_dialog_impression`:                                    struct{}{},
		`event_tap`:                                                              struct{}{},
		`offer_config_dismiss`:                                                   struct{}{},
		`ad_campaign_preview_next_tap`:                                           struct{}{},
		`ZPLMatchScreenHeaderImpression`:                                         struct{}{},
		`SDKNativeOtpRedirectionPressed`:                                         struct{}{},
		`kpt_minus_tap`:                                                          struct{}{},
		`o2_call_masking_calls`:                                                  struct{}{},
		`BrandReferralPageLoaded`:                                                struct{}{},
		`OtpSuccess`:                                                             struct{}{},
		`O2MenuOfferSelectionSnippetImpression`:                                  struct{}{},
		`PushNotificationsPermissionsState`:                                      struct{}{},
		`multi_item_photo_screen_impression`:                                     struct{}{},
		`mweb_res_view_gallery_click`:                                            struct{}{},
		`se_support_escalation_opened`:                                           struct{}{},
		`O2CrystalContactlessAcceptedImpression`:                                 struct{}{},
		`O2CrystalZomatoAnthemTapped`:                                            struct{}{},
		`Dev_no_deeplink_fired`:                                                  struct{}{},
		`events`:                                                                 struct{}{},
		`transactions_orders_tap`:                                                struct{}{},
		`upload-res-images-response-time`:                                        struct{}{},
		`O2AddMoneyInWalletLoaded`:                                               struct{}{},
		`O2CartOfferEditTap`:                                                     struct{}{},
		`V2HOMEPAGE_LOADED`:                                                      struct{}{},
		`custom_offers_preview_confirm_tap`:                                      struct{}{},
		`LongDistanceWarningPopup`:                                               struct{}{},
		`GoldCartPlaceOrderSuccess`:                                              struct{}{},
		`compensation_policy_detail_tap`:                                         struct{}{},
		`sign-up-button-click`:                                                   struct{}{},
		`O2CrystalUserAddressSnippetImpressionV16`:                               struct{}{},
		`GoogleCampaignDeeplinkResult`:                                           struct{}{},
		`consumer_info_place_info`:                                               struct{}{},
		`SdkMakePaymentCallUnknown`:                                              struct{}{},
		`choose_package_click`:                                                   struct{}{},
		`mx_dining_web_events_get_event_tags_api`:                                struct{}{},
		`deeplink_unlock_gold`:                                                   struct{}{},
		`video_ads_budget_card_tap`:                                              struct{}{},
		`Dev_AppAPIRetryFinish`:                                                  struct{}{},
		`PUMenuCustomizationMediaImpression`:                                     struct{}{},
		`ResDirectionTapped`:                                                     struct{}{},
		`StoryCollectionImpression`:                                              struct{}{},
		`crystal_funnel`:                                                         struct{}{},
		`menu_add_photo_preview_tap`:                                             struct{}{},
		`delivered_order_trends_tab_click`:                                       struct{}{},
		`gift_card_balance_page_FAQ_tapped`:                                      struct{}{},
		`reporting_time_period_selected`:                                         struct{}{},
		`ResPageSlotOfferSectionBottomSheetDayTapped`:                            struct{}{},
		`deals_card_type_2_impr_adcb`:                                            struct{}{},
		`InstantAddonsLoaded`:                                                    struct{}{},
		`MenuSearchClearTapped`:                                                  struct{}{},
		`AppLaunchGPSLocationFreshAttempt`:                                       struct{}{},
		`AppleLoginValidity`:                                                     struct{}{},
		`CRYSTAL_MESSAGE_RECEIVED`:                                               struct{}{},
		`OrderStatusAppSuccess`:                                                  struct{}{},
		`hp_cart_page_impression`:                                                struct{}{},
		`deeplink_brand`:                                                         struct{}{},
		`menu_item_confirm_tap`:                                                  struct{}{},
		`order-update-socket-event`:                                              struct{}{},
		`O2HomeSearchClicked`:                                                    struct{}{},
		`mx_dining_web_get_presigned_urls_success`:                               struct{}{},
		`support_home_tap`:                                                       struct{}{},
		`ad_campaign_view_details_tap`:                                           struct{}{},
		`PhonePopOpened`:                                                         struct{}{},
		`fetch_using_z_loc`:                                                      struct{}{},
		`O2PaymentMethodDetailsNumberEntered`:                                    struct{}{},
		`async-task-failed`:                                                      struct{}{},
		`phone_send_otp_failure`:                                                 struct{}{},
		`email_otp_verification_impression`:                                      struct{}{},
		`ndg_user_claim`:                                                         struct{}{},
		`thumbnail_tap`:                                                          struct{}{},
		`O2PaymentMethodDetailsExpiryEntered`:                                    struct{}{},
		`deeplink_generic_payment_status`:                                        struct{}{},
		`menu_variant_in_stock`:                                                  struct{}{},
		`menu_delete_category_confirm_click`:                                     struct{}{},
		`O2CrystalWhatsappOptInSnippetTapped`:                                    struct{}{},
		`O2CartMoreInstructionsTapped`:                                           struct{}{},
		`PinningKeyFetchRequested`:                                               struct{}{},
		`toggle-sidebar-collapse`:                                                struct{}{},
		`GenericWebviewCTA-ROI_0421`:                                             struct{}{},
		`webview-pro-click-on-tap-to-claim-cta`:                                  struct{}{},
		`O2MenuNutritionInfoTapped`:                                              struct{}{},
		`DV_TOKEN_INIT_FAILED`:                                                   struct{}{},
		`deeplink_dine`:                                                          struct{}{},
		`menu_item_pick_subcategory_next_click`:                                  struct{}{},
		`custom_offerss_screen_impression`:                                       struct{}{},
		`go_to_order_history_click`:                                              struct{}{},
		`add_gstin_option_save_error`:                                            struct{}{},
		`ad_campaign_preview_create_tap`:                                         struct{}{},
		`RewardsScratchCardAction`:                                               struct{}{},
		`SDKPaymentMethodInvalidCardEntered`:                                     struct{}{},
		`menu_add_category_save_click`:                                           struct{}{},
		`update_contact_time_diy_click`:                                          struct{}{},
		`V2O2CrystalPageViewed`:                                                  struct{}{},
		`learning_centre_locale_selected`:                                        struct{}{},
		`revoke_mx_permissions`:                                                  struct{}{},
		`tapped_home_page_location_search`:                                       struct{}{},
		`SDKPaymentPageClosed`:                                                   struct{}{},
		`aerobar_left_action`:                                                    struct{}{},
		`instant_show_order_id_click`:                                            struct{}{},
		`vendor_api_failure_12_290`:                                              struct{}{},
		`CartOFSESnippetTapEvent`:                                                struct{}{},
		`GoldMembershipHelpImpression`:                                           struct{}{},
		`SDKDefaultPaymentMethodFailure`:                                         struct{}{},
		`SDKUpiGpayLoadSuccessful`:                                               struct{}{},
		`O2OrderPlaceConfirmShown`:                                               struct{}{},
		`catalogue_dietary_tag_dropdown`:                                         struct{}{},
		`screen_impression`:                                                      struct{}{},
		`ownership-transfer/accepted-by-merchant`:                                struct{}{},
		`AppleLoginDataFetchFailure`:                                             struct{}{},
		`dish_add_tap`:                                                           struct{}{},
		`microphone-permission-granted`:                                          struct{}{},
		`payment_failure_dialog_impression`:                                      struct{}{},
		`verify-bank_account_click`:                                              struct{}{},
		`NewLocationPermissionFlow`:                                              struct{}{},
		`rejection_reason_trends_impression`:                                     struct{}{},
		`O2CrystalUserSnippetImpressionV16`:                                      struct{}{},
		`Dev_tabbar_height_record`:                                               struct{}{},
		`ZPLGameRuleImpression`:                                                  struct{}{},
		`contact_details_edit_phone_success`:                                     struct{}{},
		`deeplink_t`:                                                             struct{}{},
		`order_details_loaded`:                                                   struct{}{},
		`transactions_expenses_tap`:                                              struct{}{},
		`reject_order_confirm_web`:                                               struct{}{},
		`LocationMapScreenChangeLocationButtonTapped`:                            struct{}{},
		`LocationSheetDismissed`:                                                 struct{}{},
		`O2BgCartPageLoaded`:                                                     struct{}{},
		`GenericWebviewCTA-Legends_HowWeDoIt_20221213_V1`:                        struct{}{},
		`ads_contact_support_tap`:                                                struct{}{},
		`deeplink_edition`:                                                       struct{}{},
		`tour_specific_date_confirm`:                                             struct{}{},
		`O2PostOrderCartAddressSnippetImpression`:                                struct{}{},
		`inventory_item_oos_toggle_pre_dialog_impression`:                        struct{}{},
		`menu_duplicate_name`:                                                    struct{}{},
		`TranslationFeedback`:                                                    struct{}{},
		`logstore_search_bar_tap`:                                                struct{}{},
		`mx_dining_web_menu_hit_and_trial_upload_image_preview`:                  struct{}{},
		`SDKDefaultPaymentMethod`:                                                struct{}{},
		`BmCollectionAction`:                                                     struct{}{},
		`asset-banner-impression`:                                                struct{}{},
		`mx-o2-config`:                                                           struct{}{},
		`review_write_submit`:                                                    struct{}{},
		`order_notification_on`:                                                  struct{}{},
		`webview-pro-welcome-page-open`:                                          struct{}{},
		`FeedingIndiaPaymentMethodTapped`:                                        struct{}{},
		`web_universal_search_query'`:                                            struct{}{},
		`O2CrystalLateServiceableBanner`:                                         struct{}{},
		`menu_item_edit_save_tap`:                                                struct{}{},
		`webview-event-details-get-event-details-api-failure`:                    struct{}{},
		`cx_number_sharing_screen_load`:                                          struct{}{},
		`DeeplinkOpened`:                                                         struct{}{},
		`autosuggest_retrieved_from_api`:                                         struct{}{},
		`inventory_addons_tab_tap`:                                               struct{}{},
		`mx_nps_screen_display`:                                                  struct{}{},
		`O2AddPaymentMethodsBackTapped`:                                          struct{}{},
		`preferred-pos-expand-click`:                                             struct{}{},
		`custom_offers_data_submit_click`:                                        struct{}{},
		`is_otp_verification_successful`:                                         struct{}{},
		`CURRENT_SSID_FAILED`:                                                    struct{}{},
		`ResAwardsNomineesPageResImpression`:                                     struct{}{},
		`mx_action_decline`:                                                      struct{}{},
		`ZTRENDS_API_SUCCESS`:                                                    struct{}{},
		`ownership-transfer/accepted-by-govid/bank-error`:                        struct{}{},
		`CompetitionTracking`:                                                    struct{}{},
		`logout-success-new`:                                                     struct{}{},
		`photo_progress_bar_shown`:                                               struct{}{},
		`google_login_tap`:                                                       struct{}{},
		`res_delivery_add_item_clicked`:                                          struct{}{},
		`locationpermissionmanual`:                                               struct{}{},
		`deeplink_edit_profile`:                                                  struct{}{},
		`O2CrystalDownloadBillButtonTapped`:                                      struct{}{},
		`O2CrystalWhatsappPromoOptinBannerImpression`:                            struct{}{},
		`CART_ERROR_DIALOG_SHOWN`:                                                struct{}{},
		`ResPhotoTapped`:                                                         struct{}{},
		`zfb_advanced_filter_pill_applied`:                                       struct{}{},
		`GoogleSigninSuccess`:                                                    struct{}{},
		`order_history_refund_tap`:                                               struct{}{},
		`menu_topbar_add_category_button`:                                        struct{}{},
		`campaign_tap`:                                                           struct{}{},
		`order_cancellation_mail_success`:                                        struct{}{},
		`download_order_history_clicked`:                                         struct{}{},
		`AddPhotoClicked`:                                                        struct{}{},
		`O2MenuMiniCartViewed`:                                                   struct{}{},
		`EventPageAddtoCartClicked`:                                              struct{}{},
		`special_menus`:                                                          struct{}{},
		`add-dispute-review`:                                                     struct{}{},
		`ResPageTalabatBannerTapped`:                                             struct{}{},
		`deeplink_order_summary`:                                                 struct{}{},
		`web_universal_search_query-1`:                                           struct{}{},
		`FeedPostAction`:                                                         struct{}{},
		`callerid_sync_done`:                                                     struct{}{},
		`location_address_heuristics`:                                            struct{}{},
		`res_order_cart_continue_failed`:                                         struct{}{},
		`mx_dining_web_menu_click_on_cancel_rearrange_button`:                    struct{}{},
		`Zhealthy_PageLoaded`:                                                    struct{}{},
		`desktop_notification_request_logs`:                                      struct{}{},
		`gold_subscription_rail_tapped`:                                          struct{}{},
		`customer_type_select`:                                                   struct{}{},
		`mweb_amp_open_app_menu_modal_menu_page_impression`:                      struct{}{},
		`notification_centre_opened`:                                             struct{}{},
		`order_timelines_api`:                                                    struct{}{},
		`settings_pill_impression`:                                               struct{}{},
		`winback_page_impression`:                                                struct{}{},
		`ResReviewTabLoaded`:                                                     struct{}{},
		`claim_gift_card_update_personal_details_update_tapped`:                  struct{}{},
		`ownership-transfer/get-form-data`:                                       struct{}{},
		`ps-insertion-received`:                                                  struct{}{},
		`BrandReferralPageNoContentView`:                                         struct{}{},
		`LocationPermissionPageLoaded`:                                           struct{}{},
		`HomeLocationLoadFailed`:                                                 struct{}{},
		`LocationOFSEReceiverDetailsUpdated`:                                     struct{}{},
		`LocationAudioSnippetTapped`:                                             struct{}{},
		`TokenRefreshEvent`:                                                      struct{}{},
		`GoldMembershipPageActions`:                                              struct{}{},
		`gold_remove_plan_button_tapped`:                                         struct{}{},
		`refunds_v3`:                                                             struct{}{},
		`single-res-filter-tap`:                                                  struct{}{},
		`O2CrystalOrderSharingDeliveredSnippetImpression`:                        struct{}{},
		`O2DeliveryInstructionRailImpression`:                                    struct{}{},
		`MemoryWarningError`:                                                     struct{}{},
		`SDKNativeOtpMultipleSMS`:                                                struct{}{},
		`O2MenuCustomizationDismissed`:                                           struct{}{},
		`VideoPlayerError`:                                                       struct{}{},
		`growth_tab_tap`:                                                         struct{}{},
		`invalid_distance_response`:                                              struct{}{},
		`o2_suggestions_null_result`:                                             struct{}{},
		`multi-package-package-selection-menu-upload-click`:                      struct{}{},
		`unverified_contact_details_delete_confirm`:                              struct{}{},
		`PhotoSliderRightArrowTapped`:                                            struct{}{},
		`logs_order_timeouts`:                                                    struct{}{},
		`res_delivery_menu_category'`:                                            struct{}{},
		`O2CrystalAppRatingBlockerImpressionEvent`:                               struct{}{},
		`LocationMapScreenSubtitleEmpty`:                                         struct{}{},
		`ReviewPageMediaSelect`:                                                  struct{}{},
		`GoldIntroFAQTap`:                                                        struct{}{},
		`app_foreground`:                                                         struct{}{},
		`quiz_video_recover_attempt`:                                             struct{}{},
		`share_feedback_submit_tap`:                                              struct{}{},
		`O2LocationSearchResultSelected`:                                         struct{}{},
		`SDKRemoveCardFailure`:                                                   struct{}{},
		`ResPageSectionsImpression`:                                              struct{}{},
		`SDKCancelPaymentSuccess`:                                                struct{}{},
		`StorySwipeDown`:                                                         struct{}{},
		`mx_diy_onboarding_o2_timings_update`:                                    struct{}{},
		`O2SearchLocationChanged`:                                                struct{}{},
		`mx_action_mark_as_delivered`:                                            struct{}{},
		`ZomalandTabLoaded`:                                                      struct{}{},
		`CART_REQUEST`:                                                           struct{}{},
		`DeviceBroadcastEventReceived`:                                           struct{}{},
		`ci_listing_date_filter`:                                                 struct{}{},
		`o2_digital_contract_phone_mismatch`:                                     struct{}{},
		`signup_new_account`:                                                     struct{}{},
		`SavedLocationDeleteTapped`:                                              struct{}{},
		`O2CrystalEarlyDeliveryImpression`:                                       struct{}{},
		`UserPrefItemSelected`:                                                   struct{}{},
		`deeplink_safariwebview`:                                                 struct{}{},
		`order_history_order_filter_apply`:                                       struct{}{},
		`timeout_event`:                                                          struct{}{},
		`youtube_channel_info_tap`:                                               struct{}{},
		`ProFaqTap`:                                                              struct{}{},
		`O2TrendingBrandsTileSelected`:                                           struct{}{},
		`O2CrystalResDetailsTapped`:                                              struct{}{},
		`O2CrystalDeliveryInstructionTapped`:                                     struct{}{},
		`LocationPrecisePromptShown`:                                             struct{}{},
		`PaymentPartnerAccordionImpression`:                                      struct{}{},
		`common_banner_impression`:                                               struct{}{},
		`learning_centre_screentime`:                                             struct{}{},
		`ping_dns_fail`:                                                          struct{}{},
		`appRatingSnippetTapped`:                                                 struct{}{},
		`review_photo_upload_fail`:                                               struct{}{},
		`O2FirstDishAdded`:                                                       struct{}{},
		`O2MenuItemImpression`:                                                   struct{}{},
		`location_construct_with_place`:                                          struct{}{},
		`review_drafts_analysis`:                                                 struct{}{},
		`deeplink_c`:                                                             struct{}{},
		`NUMBER_MASKING`:                                                         struct{}{},
		`hp_login_screen_impression`:                                             struct{}{},
		`deeplink_choose_language`:                                               struct{}{},
		`menu_add_category_click`:                                                struct{}{},
		`notification-not-supported`:                                             struct{}{},
		`web_current_device_location_prompt_opened`:                              struct{}{},
		`zwallet_addmoney_success`:                                               struct{}{},
		`O2PostOrderCartConfirmAddressChangeButtonImpression`:                    struct{}{},
		`O2CrystalOptOutBillImpression`:                                          struct{}{},
		`mx_survey_dismissal`:                                                    struct{}{},
		`O2DropPinClosed`:                                                        struct{}{},
		`menu_upload_item_image_failed`:                                          struct{}{},
		`StoryBookmarked`:                                                        struct{}{},
		`markdown_parser_exception`:                                              struct{}{},
		`O2GoldPageLoaderViewCart`:                                               struct{}{},
		`LocationReceiverNameTapped`:                                             struct{}{},
		`delayed_order_pill_tap`:                                                 struct{}{},
		`res_delivery_timing_update`:                                             struct{}{},
		`deeplink_zomato.com`:                                                    struct{}{},
		`FeedingIndiaThankyouPopupOKButtonTap`:                                   struct{}{},
		`ResSeeDeliveryMenuClicked`:                                              struct{}{},
		`O2PostOrderAddressUpdateSuccess`:                                        struct{}{},
		`ResAwardsCategoryTileImpression`:                                        struct{}{},
		`ad_campaign_start_date_tap`:                                             struct{}{},
		`af_purchase`:                                                            struct{}{},
		`zomarks_collection_bookmark_delete_by_criteria`:                         struct{}{},
		`PackagesPostBookingCTA`:                                                 struct{}{},
		`ReferralPopUpLoaded`:                                                    struct{}{},
		`deeplink_table_reservation`:                                             struct{}{},
		`ownership-transfer/accepted-by-govid/pan-success`:                       struct{}{},
		`radar_impression`:                                                       struct{}{},
		`mx_bot_flow_raise_ticket`:                                               struct{}{},
		`SDKUpiGpayPaymentFailed`:                                                struct{}{},
		`chng_commission_page_impression`:                                        struct{}{},
		`owner_hub_home_tap`:                                                     struct{}{},
		`RestaurantCallScreenDismiss`:                                            struct{}{},
		`chng_commission_page_button_click`:                                      struct{}{},
		`GoldMembershipPageLoaded`:                                               struct{}{},
		`battery_low_message_shown`:                                              struct{}{},
		`bottom_navigation_bar_v2_render`:                                        struct{}{},
		`cookies_notification_message_click`:                                     struct{}{},
		`try_again_tap`:                                                          struct{}{},
		`hp_splash_shown`:                                                        struct{}{},
		`menu_score_intro_impression`:                                            struct{}{},
		`LOCAL_IPV4_FAILED`:                                                      struct{}{},
		`phone_verify_otp_success`:                                               struct{}{},
		`pull_to_refresh_orders`:                                                 struct{}{},
		`se_escalation_button_tapped`:                                            struct{}{},
		`mx_dining_web_payout_get_bank_details_api_request`:                      struct{}{},
		`deeplink_game`:                                                          struct{}{},
		`gstin_add_new_gst_save_success`:                                         struct{}{},
		`AODTaskTriggered`:                                                       struct{}{},
		`AppStartPermissions`:                                                    struct{}{},
		`SearchLocationTapped`:                                                   struct{}{},
		`O2OFSEMapContactTapped`:                                                 struct{}{},
		`ResOtherOutletsResTapped`:                                               struct{}{},
		`V2TABS_LOADED`:                                                          struct{}{},
		`alert_notification_deleted`:                                             struct{}{},
		`goldCrystalSavingsSnippetSeen`:                                          struct{}{},
		`digital_contract_login_error`:                                           struct{}{},
		`logstore_in_place_filter_option_tap`:                                    struct{}{},
		`fcm_msg_received`:                                                       struct{}{},
		`O2CrystalThirdPartyVendorTrackingTapped`:                                struct{}{},
		`order_out_for_delivery_click`:                                           struct{}{},
		`CartBillingSectionCollapsed`:                                            struct{}{},
		`item_radio_button_tap`:                                                  struct{}{},
		`SharedAddressSaveAPIFailed`:                                             struct{}{},
		`FiltersClosed`:                                                          struct{}{},
		`GamificationVideoNotDownloaded`:                                         struct{}{},
		`owner_whatsapp_optin_at_o2_registration`:                                struct{}{},
		`support_item_click`:                                                     struct{}{},
		`moderation_add_comment_button`:                                          struct{}{},
		`instant_late_aerobar_click`:                                             struct{}{},
		`inAppUpdate_success_aerobar`:                                            struct{}{},
		`network_kit_retry_attempted`:                                            struct{}{},
		`ResMenuTabLoaded`:                                                       struct{}{},
		`IosLiveActivityStateChangeSuccessEvent`:                                 struct{}{},
		`TRSlotCancellationPopUpCancelButtonTapped`:                              struct{}{},
		`deeplink_rewards`:                                                       struct{}{},
		`is_hyperpure_app_installed`:                                             struct{}{},
		`outlet_selection_search_tapped`:                                         struct{}{},
		`api_cancelled_due_to_601`:                                               struct{}{},
		`aerobar_edit_phone_tap`:                                                 struct{}{},
		`upload_gstin_doc_tap`:                                                   struct{}{},
		`CartCheckoutMessageImpression`:                                          struct{}{},
		`LocationFetchFailed`:                                                    struct{}{},
		`api_error`:                                                              struct{}{},
		`header_add_restaurant_click`:                                            struct{}{},
		`hp_order_status_cta_tap`:                                                struct{}{},
		`PUResClickedFromConsumer`:                                               struct{}{},
		`vendor_api_failure_12_94`:                                               struct{}{},
		`webview_loaded`:                                                         struct{}{},
		`digital_contract_details`:                                               struct{}{},
		`add_email_notif_tap`:                                                    struct{}{},
		`troubleshoot_action_tap`:                                                struct{}{},
		`menu_delete_sub_category_click`:                                         struct{}{},
		`GenericWebviewCTA-dont_send_cutlery`:                                    struct{}{},
		`SDKZomatoCreditsScreenLoaded`:                                           struct{}{},
		`mx_token_logout`:                                                        struct{}{},
		`O2CrystalInstructionPositiveFeedbackTapped`:                             struct{}{},
		`res_page_info_icon_click`:                                               struct{}{},
		`tapped_skiplogin`:                                                       struct{}{},
		`contact_details_delete_photo`:                                           struct{}{},
		`create_new_account_tap`:                                                 struct{}{},
		`ReviewsTabSelected`:                                                     struct{}{},
		`SDKPaymentMethodWebViewCancelDeclined`:                                  struct{}{},
		`SDKUPIAppRedirectionSuccess`:                                            struct{}{},
		`O2RunnrTipPaidSuccess`:                                                  struct{}{},
		`O2CartSaltOfferAction`:                                                  struct{}{},
		`O2CrystalAppRatingBlockerNegativeTapEvent`:                              struct{}{},
		`SDKPromoPageBottomSheetClicked`:                                         struct{}{},
		`Search Bar Shown`:                                                       struct{}{},
		`invite_user`:                                                            struct{}{},
		`menu_item_toggle_on_tap`:                                                struct{}{},
		`new_order_history_download_success`:                                     struct{}{},
		`preview_tap`:                                                            struct{}{},
		`manage_notifications_tap`:                                               struct{}{},
		`climacel_failed_localities`:                                             struct{}{},
		`go_offline_confirm_click`:                                               struct{}{},
		`otp_by_call_page_loaded`:                                                struct{}{},
		`vendor_api_failure_12_6`:                                                struct{}{},
		`gdp_purchase_success`:                                                   struct{}{},
		`O2MenuHeaderTapped`:                                                     struct{}{},
		`edit_bank_account_click`:                                                struct{}{},
		`menu_copy_category_button`:                                              struct{}{},
		`crystalSwitchDismissButtonTapped`:                                       struct{}{},
		`SDKCancelPaymentSucceeded`:                                              struct{}{},
		`SearchBarTapped`:                                                        struct{}{},
		`O2SaveTipSelected`:                                                      struct{}{},
		`error_state_timings`:                                                    struct{}{},
		`go_to_menu_tap`:                                                         struct{}{},
		`digital_contract_open_success`:                                          struct{}{},
		`page_rendered`:                                                          struct{}{},
		`pay_on_delivery`:                                                        struct{}{},
		`post_order_screen_open_failed`:                                          struct{}{},
		`ResPageLoaded`:                                                          struct{}{},
		`O2MenuModalFilterSelection`:                                             struct{}{},
		`GPSLocationFetched`:                                                     struct{}{},
		`ci_issue_tips_selection_tap`:                                            struct{}{},
		`ResendOtpSuccess`:                                                       struct{}{},
		`mx_dining_web_login_from_instagram_failed`:                              struct{}{},
		`ResAwardsNomineesPageResAction`:                                         struct{}{},
		`get-lat-lng-suggestions`:                                                struct{}{},
		`GeofenceJobScheduled`:                                                   struct{}{},
		`help_page_load`:                                                         struct{}{},
		`merchant_rider_rating_data`:                                             struct{}{},
		`1CvtZfLAO`:                                                              struct{}{},
		`Dev_api_auto_retry`:                                                     struct{}{},
		`o2_bulk_order_tracking`:                                                 struct{}{},
		`print_order_tap`:                                                        struct{}{},
		`orders-polling-status`:                                                  struct{}{},
		`mx_dining_web_zomato_library_clicked`:                                   struct{}{},
		`ownership-transfer/update-verification-status`:                          struct{}{},
		`PUMenuTopBannerClicked`:                                                 struct{}{},
		`ResMainPageLoaded`:                                                      struct{}{},
		`AnimatedRollingSnippetPresentInResponse`:                                struct{}{},
		`aerobar_crossed`:                                                        struct{}{},
		`support_order_tap`:                                                      struct{}{},
		`youtube_video_snippet_tap`:                                              struct{}{},
		`restaurant_payments`:                                                    struct{}{},
		`media_repo_button_click_delete_images`:                                  struct{}{},
		`menu_item_image_delete_confirm`:                                         struct{}{},
		`SDKNativeOtpBackPressed`:                                                struct{}{},
		`SdkMakePaymentCallStarted`:                                              struct{}{},
		`FullPageState`:                                                          struct{}{},
		`3dtouch_nearby`:                                                         struct{}{},
		`fetched_from_db`:                                                        struct{}{},
		`home_page_faq_click`:                                                    struct{}{},
		`user_migration_status`:                                                  struct{}{},
		`mx_dining_web_insights_click_on_understand_your_metrics_modal`:          struct{}{},
		`ResOtherDiningRecommendationsTapped`:                                    struct{}{},
		`BlinkitBottomSheetOpened`:                                               struct{}{},
		`ResClosingFloatingViewImpression`:                                       struct{}{},
		`reporting_widget_impression`:                                            struct{}{},
		`o2SmsSkippedTracks`:                                                     struct{}{},
		`PUCartPhoneNoChangeTapped`:                                              struct{}{},
		`ResOverviewPageLoadedNew`:                                               struct{}{},
		`ResPageTabSelected`:                                                     struct{}{},
		`LocationGpsOffTagImpression`:                                            struct{}{},
		`O2MenuSustainabilityBannerTap`:                                          struct{}{},
		`GoldCartCheckoutButtonTapped`:                                           struct{}{},
		`button`:                                                                 struct{}{},
		`redirect_click_to_support_from_fb`:                                      struct{}{},
		`instant_order_list_scroll`:                                              struct{}{},
		`campaign_list_card_impression`:                                          struct{}{},
		`user_address_updation`:                                                  struct{}{},
		`web_res_scroll''`:                                                       struct{}{},
		`SDKRetryPaymentScreenCancelled`:                                         struct{}{},
		`JumboEnameTimeTrack`:                                                    struct{}{},
		`res_page_z_logo_click`:                                                  struct{}{},
		`app_launch_abc_ad_off`:                                                  struct{}{},
		`res_delivery_cart_payment`:                                              struct{}{},
		`O2CartGoldDiscountApiRequest`:                                           struct{}{},
		`battery_settings_click`:                                                 struct{}{},
		`convince_screen_close_tap`:                                              struct{}{},
		`partial_recoup_v2_comparison`:                                           struct{}{},
		`sneak_peek_impression`:                                                  struct{}{},
		`ZPLGameDismissAlertTap`:                                                 struct{}{},
		`O2OrderPlaced`:                                                          struct{}{},
		`delete_property_group`:                                                  struct{}{},
		`know-more-pos-info-click`:                                               struct{}{},
		`TRSlotCartPageSpecialRequest`:                                           struct{}{},
		`new_order_history_download_query_api_failure`:                           struct{}{},
		`custom-redirect-anjanasarabhai`:                                         struct{}{},
		`ci_image_thumbs`:                                                        struct{}{},
		`O2CrystalDropOff`:                                                       struct{}{},
		`hp_start_hyperpure_flow`:                                                struct{}{},
		`menu_topbar_add_item_button`:                                            struct{}{},
		`zwallet_addmoney_screen`:                                                struct{}{},
		`signup_click`:                                                           struct{}{},
		`O2CrystalOrderSharingSupportSnippetImpressionEvent`:                     struct{}{},
		`HPPayButtonLandingPageSearchTapped`:                                     struct{}{},
		`KARMA_DEVICE_INFO_FAILED`:                                               struct{}{},
		`ToggleButtonTapped`:                                                     struct{}{},
		`deeplink_restaurant`:                                                    struct{}{},
		`make_api_soft_error`:                                                    struct{}{},
		`menu_price_input`:                                                       struct{}{},
		`with_credentails_logs_by_url`:                                           struct{}{},
		`support_clicked_from_error_page`:                                        struct{}{},
		`StoryDurationError`:                                                     struct{}{},
		`O2BillSummaryChargesExpand`:                                             struct{}{},
		`DiningHistoryPageLoaded`:                                                struct{}{},
		`ci_issue_strip_impression`:                                              struct{}{},
		`offers_tab_tap`:                                                         struct{}{},
		`PhotoSliderLeftArrowTapped`:                                             struct{}{},
		`GenericWebviewCTA-gold_old_member_emerging`:                             struct{}{},
		`new_web_performance_metric-0`:                                           struct{}{},
		`O2CartCookingInstructionsAdded`:                                         struct{}{},
		`O2CartOfferItemSelectionSnippetImpression`:                              struct{}{},
		`claim_gift_card_update_personal_details_popup_impression`:               struct{}{},
		`lat_lng_success`:                                                        struct{}{},
		`aerobar_view_order_click`:                                               struct{}{},
		`expand_rider_image_tap`:                                                 struct{}{},
		`CDNGMenuCustomizationLoaded`:                                            struct{}{},
		`res_growth_banner_impression`:                                           struct{}{},
		`phone_auto_verify_response`:                                             struct{}{},
		`dish_add`:                                                               struct{}{},
		`JumboEnameDeeplinkOpened`:                                               struct{}{},
		`aslite_enrichment_data`:                                                 struct{}{},
		`email_not_registered`:                                                   struct{}{},
		`prepaid_payment_click`:                                                  struct{}{},
		`recommendation_see_all_tapped`:                                          struct{}{},
		`aws_profiling`:                                                          struct{}{},
		`O2CrystalCarouselCXRXCrossTappedEvent`:                                  struct{}{},
		`O2CrystalFoodRatingTapped`:                                              struct{}{},
		`account_manager_contact_tap`:                                            struct{}{},
		`ready_tab_click`:                                                        struct{}{},
		`home_page_loaded_event`:                                                 struct{}{},
		`add_gstin_list_item_selected`:                                           struct{}{},
		`ticket_aerobar_tap`:                                                     struct{}{},
		`GoldMembershipChangeAppIconTap`:                                         struct{}{},
		`menu_score_action_impression`:                                           struct{}{},
		`StoryTapped`:                                                            struct{}{},
		`SelectionLocationPageOpened`:                                            struct{}{},
		`edition_wallet_impression`:                                              struct{}{},
		`in_app_popup_dismiss`:                                                   struct{}{},
		`O2CartAddPaymentMethodTapped`:                                           struct{}{},
		`new_order_notification_tap`:                                             struct{}{},
		`dining-table-booking-notify-me`:                                         struct{}{},
		`menu_description_textbox`:                                               struct{}{},
		`O2PostOrderCartSpecialMessageSnippetImpression`:                         struct{}{},
		`deeplink_tap`:                                                           struct{}{},
		`home_activity_create`:                                                   struct{}{},
		`res_delivery_menu_search_text`:                                          struct{}{},
		`web_universal_lm_close_with_cross_button`:                               struct{}{},
		`refresh-call-failure`:                                                   struct{}{},
		`mx_dining_web_load_more_stories_api_request_success`:                    struct{}{},
		`searchresponsetime`:                                                     struct{}{},
		`O2PostOrderCartBackToOrderButtonTapped`:                                 struct{}{},
		`LanguagebuttonTapped`:                                                   struct{}{},
		`mweb_amp_open_app_menu_modal_resinfo_page_outside_click`:                struct{}{},
		`notfication_centre_tour_click`:                                          struct{}{},
		`SDKPaymentSelectionPageShown`:                                           struct{}{},
		`O2MenuModalFilterOpen`:                                                  struct{}{},
		`live-tracking-data`:                                                     struct{}{},
		`track_offers_stop_success`:                                              struct{}{},
		`variant_item_delete`:                                                    struct{}{},
		`deeplink_search_interstitial`:                                           struct{}{},
		`vn93TUQi`:                                                               struct{}{},
		`O2CrystalContactlessDeliveredTapped`:                                    struct{}{},
		`O2CrystalRiderCarouselFraudImpression`:                                  struct{}{},
		`SDKMakePaymentCallbackSentToClient`:                                     struct{}{},
		`OrderStatusAppStart`:                                                    struct{}{},
		`O2_SEARCH_RESULTS_SERVICEABILITY`:                                       struct{}{},
		`settlement_detail_tap`:                                                  struct{}{},
		`web_response_times_server_event`:                                        struct{}{},
		`deeplink_o2`:                                                            struct{}{},
		`O2CrystalRecommendationImpression`:                                      struct{}{},
		`SDKPaymentMethodDetailsBackTapped`:                                      struct{}{},
		`ResAwardsCategoryTileTapped`:                                            struct{}{},
		`bank_details_edit_tap`:                                                  struct{}{},
		`contact_details_edit_email_initiate`:                                    struct{}{},
		`rejection_v2_policy_impression`:                                         struct{}{},
		`review_dispute_add_button_tapped`:                                       struct{}{},
		`restaurant_pickup_coordinates_updation`:                                 struct{}{},
		`mweb_open_app_modal_v3_open_app_click`:                                  struct{}{},
		`O2MenuNutritionInfoImpression`:                                          struct{}{},
		`O2ZomatoCreditsScreenLoaded`:                                            struct{}{},
		`auth_verification_init`:                                                 struct{}{},
		`pro_promo_add_order_logs`:                                               struct{}{},
		`rejected_orders_home_impression`:                                        struct{}{},
		`SDKWalletLinkingContinueTapped`:                                         struct{}{},
		`menu_item_oos_toggle_pre_dialog_impression`:                             struct{}{},
		`reviews_opened`:                                                         struct{}{},
		`da_user_segmentation`:                                                   struct{}{},
		`extra_rider_v2_submit_tap`:                                              struct{}{},
		`inventory_search_type`:                                                  struct{}{},
		`menu_score_know_more_impression`:                                        struct{}{},
		`TRSlotBookingPageNonGoldSheetNoThanksButtonTapped`:                      struct{}{},
		`O2SearchDishClicked`:                                                    struct{}{},
		`restaurant_delivery_settings`:                                           struct{}{},
		`ads_banner_priority`:                                                    struct{}{},
		`add_user_tap`:                                                           struct{}{},
		`banner_close`:                                                           struct{}{},
		`choose_language_tap`:                                                    struct{}{},
		`mweb_dom_painted`:                                                       struct{}{},
		`RewardsPageLoaded`:                                                      struct{}{},
		`SDKPaymentOptionsRemoveWalletTapped`:                                    struct{}{},
		`PackagesMenuPageLoaded`:                                                 struct{}{},
		`Redirection_from_Profile_page`:                                          struct{}{},
		`V2LoginPageLoaded`:                                                      struct{}{},
		`ZPLGameDismissAlertImpression`:                                          struct{}{},
		`call_customer_tap`:                                                      struct{}{},
		`deals_ticket_purchase`:                                                  struct{}{},
		`feedback_question_item_rating_tapped`:                                   struct{}{},
		`contract_share`:                                                         struct{}{},
		`onboarding_step_validation_for_saving_basic_details`:                    struct{}{},
		`recommended_toggle_on_tap`:                                              struct{}{},
		`soft_app_updates`:                                                       struct{}{},
		`track_offers_dialog_agree`:                                              struct{}{},
		`menu_image_score_cross_clicked`:                                         struct{}{},
		`ListingsDidFailReturn`:                                                  struct{}{},
		`promo_campaign_contract_tap`:                                            struct{}{},
		`one-support-chat-toggle`:                                                struct{}{},
		`welcome_privacy_tap`:                                                    struct{}{},
		`hamburger_menu_opened_home_page`:                                        struct{}{},
		`@@T9RbR`:                                                                struct{}{},
		`TwoStepRatingPrompt`:                                                    struct{}{},
		`ThirdPartyAPIs\Gupshup\WhatsappApiCaller_error`:                         struct{}{},
		`new_order_history_download_query_api_success`:                           struct{}{},
		`mx_dining_web_insights_revenue_and_transaction_revenue_rend_api_success_failure`: struct{}{},
		`redirect`: struct{}{},
		`menu_item_image_on_hold_view_details_tap`: struct{}{},
		`statements_tap`:                       struct{}{},
		`ZTRENDS_API_FAILURE`:                  struct{}{},
		`support_view_order_ratings_click`:     struct{}{},
		`GzP1JGwj`:                             struct{}{},
		`O2CartExtraChargesTapped`:             struct{}{},
		`SDKDefaultPaymentMethodStarted`:       struct{}{},
		`SdkMakePaymentCallSuccess`:            struct{}{},
		`PhoneNumberVerificationPageView`:      struct{}{},
		`app_launch`:                           struct{}{},
		`rainDetectionMLModelResponse`:         struct{}{},
		`o2_support_cost_one_min_cancellation`: struct{}{},
		`mx_dining_web_events_toggle_create_event_upload_media_tips_modal`: struct{}{},
		`gstin_option_refresh_success`:                                     struct{}{},
		`vendor_api_failure_12_189`:                                        struct{}{},
		`CERT_API_FAIL`:                                                    struct{}{},
		`CartOOSDialogClick`:                                               struct{}{},
		`ListingsHomeRequestParsedV2`:                                      struct{}{},
		`CDNGMenuCustomizationDismissed`:                                   struct{}{},
		`digital_contract_sign_creation_sucess`:                            struct{}{},
		`mweb_open_app_modal_v3_continue_click`:                            struct{}{},
		`mx_support_ticket_feedback`:                                       struct{}{},
		`print_order_sent`:                                                 struct{}{},
		`O2SelectLocationCurrentLocationFetched`:                           struct{}{},
		`growth_approval_action`:                                           struct{}{},
		`logout-success-old`:                                               struct{}{},
		`O2FabClicked`:                                                     struct{}{},
		`mx_invoice_generation_failed`:                                     struct{}{},
		`assign_rider_close_tap`:                                           struct{}{},
		`fb_success`:                                                       struct{}{},
		`O2CrystalOrderSharingResCutleryImpression`:                        struct{}{},
		`ad_alert_popup_cancel`:                                            struct{}{},
		`mx_dining_web_click_on_go_to_delivery_button`:                     struct{}{},
		`onwer_whatsapp_optin_at_res_creation`:                             struct{}{},
		`category_header_service_change`:                                   struct{}{},
		`PUFirstDishAdded`:                                                 struct{}{},
		`logstore_table_cell_filter_option_tap`:                            struct{}{},
		`call_customer_tap_web`:                                            struct{}{},
		`serviceability_reminder_expiration`:                               struct{}{},
		`login`:                                                            struct{}{},
		`O2_HOME_PARSED_ABOVE_THRESHOLD`:                                   struct{}{},
		`ad_campaign_budget_go_back_dialog_no_tap`:                         struct{}{},
		`cache_cleared_on_auto_logout_started`:                             struct{}{},
		`schedule_delete_tap`:                                              struct{}{},
		`ownership-transfer/accepted-by-govid/cbs-success`:                 struct{}{},
		`O2CartBuild`:                                                      struct{}{},
		`SDKAddCardCallFailed`:                                             struct{}{},
		`cart_snackbar_message`:                                            struct{}{},
		`menu_score_details_impression`:                                    struct{}{},
		`res_delivery_veg_unselect`:                                        struct{}{},
		`TicketTransactionStatusSuccess`:                                   struct{}{},
		`ResOverviewPageLoaded`:                                            struct{}{},
		`res_delivery_change_address`:                                      struct{}{},
		`O2MenuCategoryDidGetSticky`:                                       struct{}{},
		`O2SustainabilitySnippetImpression`:                                struct{}{},
		`O2CrystalRainBannerV15Impression`:                                 struct{}{},
		`zfbMobileWhatsappUnsubscribe`:                                     struct{}{},
		`tapped_login`:                                                     struct{}{},
		`BrandReferralPageVerifyFailed`:                                    struct{}{},
		`gc_prof_direct`:                                                   struct{}{},
		`kpt_add_tap`:                                                      struct{}{},
		`menu_item_image_upload_success`:                                   struct{}{},
		`delayed_order_impression`:                                         struct{}{},
		`res_growth_details_impression`:                                    struct{}{},
		`menuO2IntentExperiment`:                                           struct{}{},
		`promo_discard_tap`:                                                struct{}{},
		`BottomTabClick`:                                                   struct{}{},
		`SDKPaymentMethodDetailsEmailTapped`:                               struct{}{},
		`SDKAddCardFailure`:                                                struct{}{},
		`TAB_REQUEST`:                                                      struct{}{},
		`USER_AGENT_FAILED`:                                                struct{}{},
		`change-cuisine-tap`:                                               struct{}{},
		`AnimatedSplashImageLoadingEvent`:                                  struct{}{},
		`O2CrystalCNRDeliveredTapped`:                                      struct{}{},
		`support_res_offline_res_turn_on_tap`:                              struct{}{},
		`image_qc_approve_button`:                                          struct{}{},
		`ResAwardsWinnerShareTapped`:                                       struct{}{},
		`qc_image_enhance_click`:                                           struct{}{},
		`menu_item_delete_image_fail`:                                      struct{}{},
		`O2_ORDER_SERVICEABILITY-1`:                                        struct{}{},
		`OrderPlaced`:                                                      struct{}{},
		`navbar_help_page_button`:                                          struct{}{},
		`deeplink_tab`:                                                     struct{}{},
		`O2TaxesAndChargesViewed`:                                          struct{}{},
		`override_rule_get_rule`:                                           struct{}{},
		`cuisine_impression_homepage`:                                      struct{}{},
		`js_connect_socket`:                                                struct{}{},
		`ProMemberDOResPageViewedNonPro`:                                   struct{}{},
		`search_location_fetched_from_cookie''`:                            struct{}{},
		`3dtouch_bookmarks`:                                                struct{}{},
		`BACKEND_O2CartPageLoaded`:                                         struct{}{},
		`live_activity`:                                                    struct{}{},
		`O2CrystalRiderUpdateAudioInstructionTapped`:                       struct{}{},
		`O2MenuRailItemImpression`:                                         struct{}{},
		`SDKTokenRefreshStarted`:                                           struct{}{},
		`O2HomeVideoCardImpression`:                                        struct{}{},
		`save_and_submit_click`:                                            struct{}{},
		`recent_search`:                                                    struct{}{},
		`webview-dining-venue-picker-restaurant-info-api-failed`:           struct{}{},
		`invalid-event-id`:                                                 struct{}{},
		`Dev_otp_did_tap_phone_number_suggestion`:                          struct{}{},
		`mweb_menu_modal_click_action`:                                     struct{}{},
		`order_timeline_info_tap`:                                          struct{}{},
		`order_history_tap`:                                                struct{}{},
		`O2ChangeLocationTapped`:                                           struct{}{},
		`CartLocationBottomStickyImpression`:                               struct{}{},
		`CartOFSESnippetImpressionEvent`:                                   struct{}{},
		`LocationOpenWithZomatoFailed`:                                     struct{}{},
		`menu_item_view_selection_tap`:                                     struct{}{},
		`ad_campaign_renew_ad_tap`:                                         struct{}{},
		`web_nav_tab_clicked`:                                              struct{}{},
		`deeplink_collect_rewards`:                                         struct{}{},
		`GenericWebviewCTA-birthday_20221112`:                              struct{}{},
		`SDKRemoveVpaSuccess`:                                              struct{}{},
		`appsflyer_deeplink_received`:                                      struct{}{},
		`cart_tooltip_tap`:                                                 struct{}{},
		`compensation_policy_tap`:                                          struct{}{},
		`o2_redirection_show_o2_metric`:                                    struct{}{},
		`open_order_support`:                                               struct{}{},
		`partner_splash_screen_visibility`:                                 struct{}{},
		`custom_offers_screen_data_fetch_error`:                            struct{}{},
		`breadcrumb`:                                                       struct{}{},
		`O2AddAddressNext`:                                                 struct{}{},
		`reviews_filter`:                                                   struct{}{},
		`O2CrystalRunnrTipPaid`:                                            struct{}{},
		`ResPageTalabatBannerImpression`:                                   struct{}{},
		`GenericWebviewCTA-gold_old_member_beyond41`:                       struct{}{},
		`cc_review_again_click`:                                            struct{}{},
		`O2RemovedFromCart`:                                                struct{}{},
		`mx_rush_hour_mode_clicked`:                                        struct{}{},
		`tips_sdk_payment_logger`:                                          struct{}{},
		`order_search`:                                                     struct{}{},
		`ownership-transfer/accepted-by-govid/user-res-mapping-success`:    struct{}{},
		`ThirdPartyAPIs\Gupshup\WhatsappApiCaller_success`:                 struct{}{},
		`mealtime_distribution_impression`:                                 struct{}{},
		`review_experience_change`:                                         struct{}{},
		`social_card_tap`:                                                  struct{}{},
		`O2CrystalRiderRatingBottomsheeSubmitTapped`:                       struct{}{},
		`O2CrystalZvsZVideoBannerTapped`:                                   struct{}{},
		`IosLiveActivityToggleSuccessEvent`:                                struct{}{},
		`RedWebViewLoadStarted`:                                            struct{}{},
		`O2CrystalCovidMerchantWHOTapped`:                                  struct{}{},
		`insights_customer_complaints_impression`:                          struct{}{},
		`project_rewind_enqueue`:                                           struct{}{},
		`cartPageChatIconTapped`:                                           struct{}{},
		`menu_image_delete`:                                                struct{}{},
		`mweb_does_not_deliver_modal_close`:                                struct{}{},
		`vendor_api_failure_12_5`:                                          struct{}{},
		`ListingsDidFetchObjectReturn`:                                     struct{}{},
		`mweb_dom_painted9470522`:                                          struct{}{},
		`PackagesCartCheckout`:                                             struct{}{},
		`ReferralBannerTap`:                                                struct{}{},
		`review_successful_no_photo`:                                       struct{}{},
		`zpay_scratch_card_scratched`:                                      struct{}{},
		`video_ads_budget_continue_tap`:                                    struct{}{},
		`native_otp_failure`:                                               struct{}{},
		`VC_PAGE_SHOWN`:                                                    struct{}{},
		`accept_order_tap`:                                                 struct{}{},
		`res_offline_on_time_confirm_tap`:                                  struct{}{},
		`ad_campaign_view_trends_selection_tap`:                            struct{}{},
		`impression_ads_billing_confirm_tap`:                               struct{}{},
		`schedule_off_tap`:                                                 struct{}{},
		`sneak_peek_detail_page_load`:                                      struct{}{},
		`popular-searches-click`:                                           struct{}{},
		`O2CrystalDcPackBenefitSnippet`:                                    struct{}{},
		`SDKCancelPaymentFailure`:                                          struct{}{},
		`ad_deeplink_landing_impression`:                                   struct{}{},
		`O2CrystalPayLaterTourImpression`:                                  struct{}{},
		`deeplink_home`:                                                    struct{}{},
		`inventory_edit_menu_action_tap`:                                   struct{}{},
		`upi_suggestion_tapped`:                                            struct{}{},
		`O2CrystalPayOnlineTapped`:                                         struct{}{},
		`O2CrystalZPLPredictWin2023CarouselTapped`:                         struct{}{},
		`AppUpdatePromptTapped`:                                            struct{}{},
		`fetched_gp_from_db_using_id`:                                      struct{}{},
		`tapped_signup`:                                                    struct{}{},
		`mx_dining_web_click_on_create_story_button`:                       struct{}{},
		`veg_prep_add_tag_popup`:                                           struct{}{},
		`res_cart_viewed`:                                                  struct{}{},
		`download_certificate_confirm`:                                     struct{}{},
		`tab_id_log_mapper`:                                                struct{}{},
		`ReviewUploadFailed`:                                               struct{}{},
		`menu_copy_sub_category_button`:                                    struct{}{},
		`vendor_api_failure_12_208`:                                        struct{}{},
		`SDKAddMorePaymentMethodClicked`:                                   struct{}{},
		`O2MenuFilterImpression`:                                           struct{}{},
		`order_summary_action`:                                             struct{}{},
		`restaurant_selection_confirm_tap`:                                 struct{}{},
		`mx_dining_web_stories_list_scrolled`:                              struct{}{},
		`PaymentWebViewStatus`:                                             struct{}{},
		`RedPurchasePageLoaded`:                                            struct{}{},
		`outlet_offline_button_click`:                                      struct{}{},
		`ad_alert_to_be_sent`:                                              struct{}{},
		`le-transfer/otp-flow-init`:                                        struct{}{},
		`GenericWebviewCTA-FeedingIndia_0323`:                              struct{}{},
		`ListingsDelegateNilParseEnd`:                                      struct{}{},
		`O2MenuSeeMoreImpression`:                                          struct{}{},
		`O2MenuModalFilterClearCalled`:                                     struct{}{},
		`mapp_support_feedback`:                                            struct{}{},
		`O2CrystalMapMarkerViewImpression`:                                 struct{}{},
		`PUMenuFabTapped`:                                                  struct{}{},
		`ios_back_button`:                                                  struct{}{},
		`Sample-CellularConditionMetrics`:                                  struct{}{},
		`order-details-api-success`:                                        struct{}{},
		`res_delivery_egg_select`:                                          struct{}{},
		`O2CrystalNDGOrderGeofenceBannerTapEvent`:                          struct{}{},
		`O2CrystalNDGOrderOnTimeBanner`:                                    struct{}{},
		`SDKSelectPaymentOptionsTapped`:                                    struct{}{},
		`O2SaveTipShown`:                                                   struct{}{},
		`menu_manage_variant_click`:                                        struct{}{},
		`menu_save_changes_button`:                                         struct{}{},
		`menu_delete_category_click`:                                       struct{}{},
		`business_details`:                                                 struct{}{},
		`web_universal_search_query`:                                       struct{}{},
		`keywordSearchAction`:                                              struct{}{},
		`GenericWebviewCTA-contactless_delivery`:                           struct{}{},
		`SDKManagePaymentOptionsTapped`:                                    struct{}{},
		`O2CrystalRecommendationTapped`:                                    struct{}{},
		`gstin_list_impression`:                                            struct{}{},
		`review_detail_page_opened`:                                        struct{}{},
		`O2CrystalBillInvoiceHeaderImpression`:                             struct{}{},
		`location_search_event`:                                            struct{}{},
		`customization_discard_changes`:                                    struct{}{},
		`mx_dining_web_transaction_get_api_request_success`:                struct{}{},
		`mx_diy_onboarding_res_timings_update`:                             struct{}{},
		`mx_dining_web_payout_get_past_cycles_api_failed`:                  struct{}{},
		`ad_payment_selection_cancel`:                                      struct{}{},
		`GenericWebviewCTA-Tamil_0414`:                                     struct{}{},
		`react_18_error-1`:                                                 struct{}{},
		`Dev_access_token_validity_flag`:                                   struct{}{},
		`text_typed_for_deelink_search`:                                    struct{}{},
		`order_refund_cancel_refund_tap`:                                   struct{}{},
		`PROMO_USER_APPLY_ERROR_LOG_FOR_GOLD`:                              struct{}{},
		`SDKPaymentPublicInterfaceModelsNil`:                               struct{}{},
		`HomePageLoadFailed`:                                               struct{}{},
		`InvalidNotificationPayloadReceived`:                               struct{}{},
		`DOMINOS_SERVICEABILITY_FAILURE`:                                   struct{}{},
		`event_duplicate_ticket_click`:                                     struct{}{},
		`go_back_click`:                                                    struct{}{},
		`menu_save_items_deletion`:                                         struct{}{},
		`tour_next_click`:                                                  struct{}{},
		`save-address`:                                                     struct{}{},
		`deeplink_quick_links`:                                             struct{}{},
		`zgyb_home_page_submit_click`:                                      struct{}{},
		`GoldIntroCardButtonImpression`:                                    struct{}{},
		`LocationReceiverMobileDidEndTyping`:                               struct{}{},
		`deeplink_gift_card_balance`:                                       struct{}{},
		`listing_offers_impression`:                                        struct{}{},
		`LOCAL_IPV6_WIFI_FAILED`:                                           struct{}{},
		`new_order_history_download_polling_api_success`:                   struct{}{},
		`faq_is_helpful`:                                                   struct{}{},
		`O2CrystalOfflineRetryButtonImpressionEvent`:                       struct{}{},
		`SDKRemoveCardSuccess`:                                             struct{}{},
		`api_connect_timeout_error`:                                        struct{}{},
		`hp_api_failure`:                                                   struct{}{},
		`zwallet_onboarding_form_clicked`:                                  struct{}{},
		`moderation_page_load`:                                             struct{}{},
		`StoryMute`:                                                        struct{}{},
		`event_tabbed_main_deeplink_handle`:                                struct{}{},
		`inApp_manual_update_success`:                                      struct{}{},
		`zpay_status_page_payment_details_tap`:                             struct{}{},
		`partner_user_identity_fail`:                                       struct{}{},
		`CitySaleBottomSheetImpression`:                                    struct{}{},
		`CitySaleRenewButtonTap`:                                           struct{}{},
		`CitySaleRenewButtonImpression`:                                    struct{}{},
		`AutoAddMembershipBottomSheetImpression`:                           struct{}{},
		`AutoAddMembershipBottomSheetDismissed`:                            struct{}{},
		`AutoAddMembershipButtonTap`:                                       struct{}{},
		`AutoAddMembershipButtonImpression`:                                struct{}{},
		`DontRenewButtonTap`:                                               struct{}{},
		`DontRenewButtonImpression`:                                        struct{}{},
		`AutoAddRedirectToPlanPageImpression`:                              struct{}{},
		`HomePillLottieImpression`:                                         struct{}{},
		`HomePillLottieTap`:                                                struct{}{},
		`HomePillImpression`:                                               struct{}{},
		`HomePillTap`:                                                      struct{}{},
		`NewUserBottomSheetImpression`:                                     struct{}{},
		`LapsedUserFreeTrialBottomSheetImpression`:                         struct{}{},
		`GoldUfrBannerImpression`:                                          struct{}{},
		`FlashSaleBottomSheetImpression`:                                   struct{}{},
		`HomePageTopAnimationImpression`:                                   struct{}{},
		`NewUserBottomSheetButtonImpression`:                               struct{}{},
		`NewUserBottomSheetButtonTap`:                                      struct{}{},
		`NewUserSecondBottomSheetSuccessImpression`:                        struct{}{},
		`NewUserSecondBottomSheetDismissImpression`:                        struct{}{},
		`NewUserSecondBottomSheetButtonTap`:                                struct{}{},
		`FlashSaleBottomSheetButtonTap`:                                    struct{}{},
		`FlashSaleBottomSheetButtonImpression`:                             struct{}{},
		`GoldFloatingViewImpression`:                                       struct{}{},
		`GoldFloatingViewTap`:                                              struct{}{},
		`AutoAddRedirectToPlanPageTap`:                                     struct{}{},
		`ValidateCouponButtonImpression`:                                   struct{}{},
		`ValidateCouponButtonTap`:                                          struct{}{},
		`CouponCodeBannerImpression`:                                       struct{}{},
		`CouponCodeBannerTap`:                                              struct{}{},
		`MembershipExpiredBottomSheetImpression`:                           struct{}{},
		`GoldUfrExtraFreeDaysPopupImpression`:                              struct{}{},
		`GoldExpiryExtraFreeDaysPopupImpression`:                           struct{}{},
		`GoldUfrExtraFreeDaysPopupTap`:                                     struct{}{},
		`O2CartGoldRemovalModalDismiss`:                                    struct{}{},
		`O2CartGoldRemovalModalImpression`:                                 struct{}{},
		`O2CartGoldRemovalModalKeepGoldTap`:                                struct{}{},
		`O2CartGoldRemovalModalRemoveGoldTap`:                              struct{}{},
		`GoldExpiryExtraFreeDaysPopupTap`:                                  struct{}{},
		`DCPackPurchaseNudgeTabbedHomeImpression`:                          struct{}{},
		`DCPackPurchaseNudgeButtonTap`:                                     struct{}{},
		`DCPackIntroPageSuccessImpression`:                                 struct{}{},
		`DCPackBottomSheetChangeAddressTap`:                                struct{}{},
		`DCPackBottomSheetAddAddressImpression`:                            struct{}{},
		`DCPackBottomSheetAddAddressTap`:                                   struct{}{},
		`O2DCPackPageLoaderViewed`:                                         struct{}{},
		`O2DCPackPageLoaderDismiss`:                                        struct{}{},
		`O2DCPackPageLoaderAddToCart`:                                      struct{}{},
		`O2DCPackPageLoaderViewCart`:                                       struct{}{},
		`DCPackO2CartSnippetImpression`:                                    struct{}{},
		`DCPackO2CartSnippetTap`:                                           struct{}{},
		`DCPackWelcomeConfettiImpression`:                                  struct{}{},
		`res_dropdown_option_impression`:                                   struct{}{},
		`res_dropdown_option_tap`:                                          struct{}{},
		`user_profile_update`:                                              struct{}{},
		`O2CrystalRiderEVDeliveryComponentImpressionEvent`:                 struct{}{},
		`O2CrystalRiderBirthdayComponentImpressionEvent`:                   struct{}{},
		`O2CrystalBusinessProfileBannerTapped`:                             struct{}{},
		`O2CrystalBusinessProfileBannerImpression`:                         struct{}{},
		`O2CrystalSmartSocietyBannerImpression`:                            struct{}{},
		`O2CrystalSmartSocietyBannerTapped`:                                struct{}{},
		`LinkFacebookAccountBottomSheetImpression`:                         struct{}{},
		`ProfileTypeFilterButtonTapped`:                                    struct{}{},
		`PersonalProfileScreenViewed`:                                      struct{}{},
		`PersonalProfileUpdateButtonTapped`:                                struct{}{},
		`BusinessProfileScreenViewed`:                                      struct{}{},
		`BusinessProfileKnowMoreBannerTapped`:                              struct{}{},
		`BusinessProfileUpdateButtonTapped`:                                struct{}{},
		`BusinessProfileUpdatePromptImpression`:                            struct{}{},
		`BusinessProfileUpdatePromptYesButtonTapped`:                       struct{}{},
		`BusinessProfileUpdatePromptNoButtonTapped`:                        struct{}{},
		`BusinessProfileOTPBottomsheetImpression`:                          struct{}{},
		`BusinessProfileOTPBottomsheetDismissed`:                           struct{}{},
		`BusinessProfileOTPBottomsheetResendButtonTapped`:                  struct{}{},
		`BusinessProfileOTPBottomsheetVerifyButtonTapped`:                  struct{}{},
		`BusinessProfileUpdateSuccessAlertImpression`:                      struct{}{},
		`BusinessProfileUpdateSuccessAlertOkayButtonTapped`:                struct{}{},
		`ReimbursementReportsScreenViewed`:                                 struct{}{},
		`ReimbursementReportsScreenReportCardImpression`:                   struct{}{},
		`ReimbursementReportsScreenDownloadReportTapped`:                   struct{}{},
		`ReimbursementReportsScreenDownloadShareTapped`:                    struct{}{},
		`ReimbursementReportsScreenFAQButtonTapped`:                        struct{}{},
		`ReimbursementReportsScreenFAQScreenViewed`:                        struct{}{},
		`O2CartProfileTypeFilterImpression`:                                struct{}{},
		`O2CartProfileTypeFilterButtonTapped`:                              struct{}{},
		`order_history_filter_button_tapped`:                               struct{}{},
		`O2MenuLateRelayImpression`:                                        struct{}{},
		`O2MenuLateRelayTap`:                                               struct{}{},
		`O2MenuLateRelayClosed`:                                            struct{}{},
		`fssai_auto_verification_completed`:                                struct{}{},
		`validate_legal_docs`:                                              struct{}{},
		`menu_open_source_web`:                                             struct{}{},
		`RecommendedBottomsheetOpened`:                                     struct{}{},
		`RecommendedBottomsheetDismissed`:                                  struct{}{},
		`CartItemRecommendationUpdateCart`:                                 struct{}{},
		`RecommendedItemBottomSheetTap`:                                    struct{}{},
		`RecommendedItemBottomSheetImpression`:                             struct{}{},
		`RecommendedItemBottomSheetAdd`:                                    struct{}{},
		`UnavailableSnippetImpression`:                                     struct{}{},
		`UnavailableSnippetTap`:                                            struct{}{},
		`TabUnavailableItemBottomSheetTap`:                                 struct{}{},
		`TabUnavailableItemBottomSheetImpression`:                          struct{}{},
		`money_tab_gc_buy_click`:                                           struct{}{},
		`money_tab_gc_buy_page`:                                            struct{}{},
		`money_tab_gc_claim`:                                               struct{}{},
		`money_tab_gc_history`:                                             struct{}{},
		`money_tab_upi_activate_click`:                                     struct{}{},
		`money_tab_upi_copy`:                                               struct{}{},
		`money_tab_upi_scanner_click`:                                      struct{}{},
		`money_tab_upi_qr_view`:                                            struct{}{},
		`money_tab_upi_dashboard_click`:                                    struct{}{},
		`money_tab_upi_dashboard`:                                          struct{}{},
		`money_tab_wallet_activate_click`:                                  struct{}{},
		`money_tab_pill_click`:                                             struct{}{},
		`money_tab_card_click`:                                             struct{}{},
		`money_tab_wallet_impression`:                                      struct{}{},
		`money_tab_upi_impression`:                                         struct{}{},
		`money_tab_card_impression`:                                        struct{}{},
		`O2CrystalExplorerTriggerEvent`:                                    struct{}{},
		`O2CrystalRadarImpressionEvent`:                                    struct{}{},
		`PayBillBottomSheetSuggestedResSnippetImpression`:                  struct{}{},
		`PayBillBottomSheetSuggestedResSnippetTapped`:                      struct{}{},
		`PayBillBottomSheetBottomButtonTapped`:                             struct{}{},
		`O2CrystalDiningCarnivalCarouselTapped`:                            struct{}{},
		`O2CrystalDiningCarnivalCarouselImpression`:                        struct{}{},
		`O2CrystalDiningBottomBannerTapped`:                                struct{}{},
		`O2CrystalDiningBottomBannerImpression`:                            struct{}{},
		`GDC_CART_DROP_CHASER`:                                             struct{}{},
		`DEALS_CART_DROP_CHASER`:                                           struct{}{},
		`ZPLHistoryScreenBackTapped`:                                       struct{}{},
		`ZPLHistoryMatchDetailsTapped`:                                     struct{}{},
		`ZPLHistoryMatchDetailsImpression`:                                 struct{}{},
		`ZPLLastGameInfoSnippetTapped`:                                     struct{}{},
		`ZPLLastGameInfoImpression`:                                        struct{}{},
		`ZPLLeadGenBannerImpression`:                                       struct{}{},
		`ZPLLeadGenBannerTapped`:                                           struct{}{},
		`ZPLVideoPlayedImpression`:                                         struct{}{},
		`ZPLWinnerScreenOverlayAnimationImpression`:                        struct{}{},
		`ZPLCouponBottomSheetImpression`:                                   struct{}{},
		`ZPLCouponCardImpression`:                                          struct{}{},
		`ZPLCouponRedeemImpression`:                                        struct{}{},
		`ZPLCouponRedeemTapped`:                                            struct{}{},
		`ZPLUnscratchedCouponCardImpression`:                               struct{}{},
		`ZPLUnscratchedCouponCardTapped`:                                   struct{}{},
		`ZPLScratchOverlayImpression`:                                      struct{}{},
		`ZPLScratchedTopCardImpression`:                                    struct{}{},
		`O2ShareTapped`:                                                    struct{}{},
		`O2ShareProceed`:                                                   struct{}{},
		`login_impression`:                                                 struct{}{},
		`MENU_ITEM_CURATION_TIME`:                                          struct{}{},
		`O2CrystalSustainabilityV2BannerTapped`:                            struct{}{},
		`O2CrystalSustainabilityV2BannerImpression`:                        struct{}{},
		`O2CrystalBirthdayBannerTapped`:                                    struct{}{},
		`O2CrystalBirthdayBannerImpression`:                                struct{}{},
		`O2CrystalCarouselUnexpectedDelightTap`:                            struct{}{},
		`O2CrystalCarouselUnexpectedDelightImpression`:                     struct{}{},
		`O2CrystalUnexpectedDeligthBottomsheetImpressionEvent`:             struct{}{},
		`O2CrystalSnippetUnexpectedDelightRejectedImpression`:              struct{}{},
		`O2CrystalSnippetUnexpectedDelightRejectedTap`:                     struct{}{},
		`RewardInitializationEvent`:                                        struct{}{},
		`EventDetailsPageSectionImpression`:                                struct{}{},
		`EventDetailsPageCheckVenueTap`:                                    struct{}{},
		`EventDetailsPageCheckVenueImpression`:                             struct{}{},
		`money_tab_upi_promoted_click`:                                     struct{}{},
		`money_tab_upi_promoted_impression`:                                struct{}{},
		`O2CrystalCarouselBlinkitV2Impression`:                             struct{}{},
		`O2CrystalCarouselBlinkitV2Tapped`:                                 struct{}{},
		`O2CrystalUserSurveyBannerTapped`:                                  struct{}{},
		`O2CrystalUserSurveyBannerImpression`:                              struct{}{},
		`DiningRespageTableBookingSnippetImpression`:                       struct{}{},
		`DiningRespageTableBookingSnippetTapped`:                           struct{}{},
		`DiningRespageTableBookingSnippetViewDetailsTapped`:                struct{}{},
		`DiningRespageTableBookingSnippetPayBillTapped`:                    struct{}{},
		`SharedLinkTapped`:                                                 struct{}{},
		`CartEcardSnippetImpressionEvent`:                                  struct{}{},
		`CartEcardSnippetTapEvent`:                                         struct{}{},
		`SDKDeleteCardTapped`:                                              struct{}{},
		`SDKRenameCardTapped`:                                              struct{}{},
		`SDKAddCardTapped`:                                                 struct{}{},
		`SDKRemoveUpiIdTapped`:                                             struct{}{},
		`SDKSaveUpiIdTapped`:                                               struct{}{},
		`SDKAddNewUpiIdTapped`:                                             struct{}{},
		`SDKRechargeWalletTapped`:                                          struct{}{},
		`SDKLinkWalletContinueTapped`:                                      struct{}{},
		`SDKLinkWalletTapped`:                                              struct{}{},
		`SDKRemoveWalletTapped`:                                            struct{}{},
		`SDKNetbankingRemoveTapped`:                                        struct{}{},
		`SDKNetbankingTapped`:                                              struct{}{},
		`SDKZomatoCreditsTapped`:                                           struct{}{},
		`SDKManagePaymentPageLoaded`:                                       struct{}{},
		`SDKSelectPaymentPageLoaded`:                                       struct{}{},
		`SDKVerifyNowCardTapped`:                                           struct{}{},
		`MembershipPurchaseOfferSnippetSeen`:                               struct{}{},
		`O2MenuFilterNullSearchImpression`:                                 struct{}{},
		`O2CrystalIcExpSurveyButtonTapEvent`:                               struct{}{},
		`O2CrystalIcExpSurveyCrossTapEvent`:                                struct{}{},
		`O2CrystalIcExpSurveyImpressionEvent`:                              struct{}{},
		`IC_EXP`:                                                           struct{}{},
		`calendar_page`:                                                    struct{}{},
		`calendar_dish_impression`:                                         struct{}{},
		`LowSRBottomSheetImpression`:                                       struct{}{},
		`LowSRContinuePaymentTapped`:                                       struct{}{},
		`LowSRRecommendationsTapped`:                                       struct{}{},
		`PaymentsBottomSheetScreenDismissed`:                               struct{}{},
		`PaymentFailedBottomSheetImpression`:                               struct{}{},
		`PaymentFailedContinuePaymentTapped`:                               struct{}{},
		`PaymentFailedRecommendationsTapped`:                               struct{}{},
		`CodConversionCheckboxImpression`:                                  struct{}{},
		`CodConversionCheckboxTapped`:                                      struct{}{},
		`CodBoostedPaymentCancelled`:                                       struct{}{},
		`fathers_day_send_dad_treat`:                                       struct{}{},
		`fathers_day_home_page_load`:                                       struct{}{},
		`fathers_day_create_card`:                                          struct{}{},
		`fathers_day_share_button`:                                         struct{}{},
		`fathers_day_share_page_load`:                                      struct{}{},
		`webview-surveys-v2-page-load`:                                     struct{}{},
		`webview-surveys-v2-app-crash`:                                     struct{}{},
		`webview-surveys-v2-response-submit`:                               struct{}{},
		`ZBPaymentsPageImpression`:                                         struct{}{},
		`ZBPaymentsPageTapped`:                                             struct{}{},
		`ZBPaymentsCheckboxTapped`:                                         struct{}{},
		`ZBPaymentsStickyHeaderTapped`:                                     struct{}{},
		`ZBTooltipImpression`:                                              struct{}{},
		`PeriodicBackgroundTaskTriggered`:                                  struct{}{},
		`NotificationPermissionSoftPopupTapped`:                            struct{}{},
		`NotificationPermissionSoftPopupShown`:                             struct{}{},
		`PaymentMethodCategoryImpression`:                                  struct{}{},
		`PaymentMethodTypeImpression`:                                      struct{}{},
		`PaymentMethodTypeTapped`:                                          struct{}{},
		`ZBTileImpression`:                                                 struct{}{},
		`ZBInfoButtonTapped`:                                               struct{}{},
		`ZBUsageCheckboxTapped`:                                            struct{}{},
		`O2CrystalNeeyatBannerTapped`:                                      struct{}{},
		`O2CrystalNeeyatBannerImpression`:                                  struct{}{},
		`O2CrystalMovieBannerTapped`:                                       struct{}{},
		`O2CrystalMovieBannerImpression`:                                   struct{}{},
		`O2CrystalOrderModificationHeaderImpression`:                       struct{}{},
		`O2CrystalOrderModificationItemImpression`:                         struct{}{},
		`O2CrystalOrderModificationCancelTapped`:                           struct{}{},
		`O2CrystalOrderModificationAddItemImpression`:                      struct{}{},
		`O2CrystalOrderModificationRemainingImpression`:                    struct{}{},
		`O2CrystalOrderModificationReplacementImpression`:                  struct{}{},
		`O2CrystalOrderModificationCancelImpression`:                       struct{}{},
		`O2CrystalOrderModificationExploreMenuImpression`:                  struct{}{},
		`O2CrystalOrderModificationAddMenuTapped`:                          struct{}{},
		`O2CrystalOrderModificationAddItemTapped`:                          struct{}{},
		`O2CrystalOrderModificationExploreMenuTapped`:                      struct{}{},
		`O2CrystalOrderModificationReplacementTapped`:                      struct{}{},
		`O2CrystalOrderModificationRemainingTapped`:                        struct{}{},
		`O2CrystalCancelPositiveButtonTapped`:                              struct{}{},
		`O2CrystalCancelNegativeButtonTapped`:                              struct{}{},
		`CartPollingStarted`:                                               struct{}{},
		`CartPollingSuccess`:                                               struct{}{},
		`CartPollingFailed`:                                                struct{}{},
		`CrystalPingJourneyEvent`:                                          struct{}{},
		`TrackMqttUpdates`:                                                 struct{}{},
		`deeplink_train_ordering`:                                          struct{}{},
		`BuyTicketsTapped`:                                                 struct{}{},
		`EventsTicketStepperTap`:                                           struct{}{},
		`CarouselRailItemTapped`:                                           struct{}{},
		`TFEbannerTapped`:                                                  struct{}{},
		`TFEBannerImpression`:                                              struct{}{},
		`MainStageRailBannerImpression`:                                    struct{}{},
		`MainStageRailBannerTapped`:                                        struct{}{},
		`ZomalandAfterMovieTapped`:                                         struct{}{},
		`ZomalandAfterMovieImpression`:                                     struct{}{},
		`ZomlandFollowusBannerImpression`:                                  struct{}{},
		`ZomlandFollowusBannerTapped`:                                      struct{}{},
		`AllPartnersRailImpression`:                                        struct{}{},
		`ZomalandTicketImpression`:                                         struct{}{},
		`EventsTicketSnippetImpression`:                                    struct{}{},
		`EventsUserDetailsImpression`:                                      struct{}{},
		`CarouselRailItemImpression`:                                       struct{}{},
		`CompletePaymentCallStarted`:                                       struct{}{},
		`CompletePaymentCallSuccess`:                                       struct{}{},
		`CompletePaymentCallFailed`:                                        struct{}{},
		`O2CrystalLWGDeliveryAcceptedImpression`:                           struct{}{},
		`O2CrystalLWGDeliveryMissedTapped`:                                 struct{}{},
		`O2CrystalLWGDeliveredTapped`:                                      struct{}{},
		`O2CrystalLWGDeliveryPromptImpression`:                             struct{}{},
		`O2CrystalLWGDeliveryNoImageImpression`:                            struct{}{},
		`O2CrystalWasContactlessDeliveryPromptImpression`:                  struct{}{},
		`O2CrystalContactlessInstructionFollowedNoTapped`:                  struct{}{},
		`O2CrystalContactlessInstructionFollowedYesTapped`:                 struct{}{},
		`zlive_whereobar_impression`:                                       struct{}{},
		`fssai_ocr_document_rejected`:                                      struct{}{},
	}
)

func IsJeventEnameWhitelisted(ename string) bool {
	if strings.HasPrefix(ename, "http://") || strings.HasPrefix(ename, "https://") {
		return false
	}
	ename = strings.TrimSpace(ename)
	_, isWhitelisted := whiteListedEnames[ename]
	return isWhitelisted
}
