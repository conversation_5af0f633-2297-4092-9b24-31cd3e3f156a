package handlers

import (
	"context"
	"errors"
	"fmt"

	"github.com/Shopify/sarama"
	"github.com/Zomato/flash-gateway/internal/env"
	"github.com/Zomato/flash-gateway/internal/helpers"
	"github.com/Zomato/flash-gateway/internal/models"
	log "github.com/Zomato/go/logger"
	"github.com/Zomato/go/newrelic"
	"github.com/Zomato/go/tracer"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/types/known/anypb"

	"github.com/Zomato/flash-gateway/pkg/services/clevertap"
	"github.com/Zomato/flash-gateway/pkg/services/kafka"
	"github.com/Zomato/flash-gateway/pkg/services/statsd"
	ztracer "github.com/Zomato/flash-gateway/pkg/tracer"
	ads "github.com/Zomato/zomato-event-registry-client-golang/zomato-event-registry/ads"
	menu "github.com/Zomato/zomato-event-registry-client-golang/zomato-event-registry/eta/menu"
	event "github.com/Zomato/zomato-event-registry-client-golang/zomato-event-registry/event"
	subs "github.com/Zomato/zomato-event-registry-client-golang/zomato-event-registry/subscription/devicebannertrackingevents"
	subtrackingevents "github.com/Zomato/zomato-event-registry-client-golang/zomato-event-registry/subscription/trackingevents"
	"github.com/golang/protobuf/proto"
	"github.com/golang/protobuf/ptypes"
	"github.com/google/uuid"
	jsoniter "github.com/json-iterator/go"
	"github.com/tidwall/gjson"
	"go.uber.org/zap"
)

var json = jsoniter.ConfigCompatibleWithStandardLibrary

func ZJumboConsumerClaimFuncFrontend(ctx context.Context, message *sarama.ConsumerMessage) error {
	return JumboConsumerClaimFuncFrontend(ctx, "zomato", message)
}
func ZJumboConsumerClaimFuncBackend(ctx context.Context, message *sarama.ConsumerMessage) error {
	return JumboConsumerClaimFuncBackend(ctx, "zomato", message)
}

func BlinkitJumboConsumerClaimFuncFrontend(ctx context.Context, message *sarama.ConsumerMessage) error {
	return JumboConsumerClaimFuncFrontend(ctx, "blinkit", message)
}
func BlinkitJumboConsumerClaimFuncBackend(ctx context.Context, message *sarama.ConsumerMessage) error {
	return JumboConsumerClaimFuncBackend(ctx, "blinkit", message)
}

func DistrictJumboConsumerClaimFuncFrontend(ctx context.Context, message *sarama.ConsumerMessage) error {
	return JumboConsumerClaimFuncFrontend(ctx, "district", message)
}
func NuggetJumboConsumerClaimFuncFrontend(ctx context.Context, message *sarama.ConsumerMessage) error {
	return JumboConsumerClaimFuncFrontend(ctx, "nugget", message)
}

func JumboConsumerClaimFuncFrontend(ctx context.Context, tenant string, message *sarama.ConsumerMessage) error {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "Handlers/JumboConsumerClaimFuncFrontend", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}
	header, payloads := helpers.SeparateMessageParts(string(message.Value))
	if len(payloads) == 0 {
		return fmt.Errorf("empty payload. Payload: %s", string(message.Value))
	}
	return parseJumboConsumerMessage(ctx, tenant, header, payloads, message.Key)

}

func JumboConsumerClaimFuncBackend(ctx context.Context, tenant string, message *sarama.ConsumerMessage) error {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "Handlers/JumboConsumerClaimFuncBackend", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}
	var payloads models.AppPayloadWrapper
	var error error
	err := json.Unmarshal(message.Value, &payloads)
	if err != nil {
		return errors.New("Unable to unmarshal message. Error: " + err.Error())
	}
	for _, payload := range payloads.AppPayload {

		key := payload.Payload["key"]
		if key == "" {
			log.Warn(ctx, "Unable to get key", zap.Any("payload", payload.Payload))
			continue
		}
		headerInterface, payloadInterface := payload.Header, payload.Payload
		headerBytes, err := json.Marshal(headerInterface)
		if err != nil {
			log.Error("error converting header to string")
			return err
		}
		valueBytes, err := json.Marshal(payloadInterface)
		if err != nil {
			log.Error(err.Error())
			continue
		}
		var valueSlice []string
		valueSlice = append(valueSlice, string(valueBytes))
		error = parseJumboConsumerMessage(ctx, tenant, string(headerBytes), valueSlice, message.Key)
	}
	return error

}

func parseJumboConsumerMessage(ctx context.Context, tenant string, header string, payloads []string, messageKey []byte) error {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "Handlers/parseJumboConsumerMessage", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}
	for _, rawPayload := range payloads {
		// validate if payload is valid json
		if !gjson.Valid(rawPayload) {
			log.Error(ctx, "Invalid payload json", zap.Any("payload", rawPayload))
			continue
		}

		key := gjson.Get(rawPayload, "key").String()
		url := gjson.Get(rawPayload, "url").String()
		if ok := helpers.ShouldHandleV2Format(key); ok {
			HandleV2FormatMessage(ctx, tenant, key, header, rawPayload, messageKey)
			continue
		}
		if ok := helpers.IsInStringSlice(key, helpers.Tables); !ok {
			continue
		}

		table := helpers.TablesEventsMap[key]
		eventName := gjson.Get(rawPayload, "value."+table.EventNameField).String()
		if ok := helpers.IsInStringSlice(eventName, table.AllowedEventsStore); !ok {
			continue
		}
		topicProducers := table.AllowedEvents[eventName]
		shouldFilter := false
		for _, topicProducer := range topicProducers {
			if !shouldFilter {
				shouldFilter = helpers.ShouldFilter(topicProducer.Filters, rawPayload)
			}
		}
		if shouldFilter {
			continue
		}
		payload, status := helpers.GetTableSpecificPayload(ctx, key, eventName, url, header, rawPayload)

		// if we were not able to build payload
		if status == 1 {
			log.Error(ctx, "Unable to build payload", zap.Any("payload", rawPayload))
			continue
		}

		if payload == nil {
			continue
		}

		jsBytes, err := json.Marshal(payload)
		if err != nil {
			log.Error(ctx, "Unable to marshal payload", zap.Any("payload", payload), zap.String("error", err.Error()))
			continue
		}

		payloadString := string(jsBytes)
		kafka.Publish(
			ctx,
			helpers.TablesEventsMap[key].AllowedEvents[eventName],
			payloadString,
			getProtoMessage(key, eventName, payload),
			[]byte(messageKey),
		)
	}

	return nil
}

func HandleV2FormatMessage(ctx context.Context, tenant, key, header, rawPayload string, messageKey []byte) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "Handlers/HandleV2FormatMessage", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}
	if ok := helpers.IsInStringSlice(key, helpers.V2FormatTables); !ok {
		return
	}

	payload := helpers.GetV2FormatTableSpecificPayload(ctx, header, rawPayload, tenant)
	// if we were not able to build payload
	if payload == nil {
		log.Error(ctx, "Unable to build payload", zap.Any("payload", rawPayload))
		return
	}

	result, err := json.Marshal(payload)
	if err != nil {
		log.Warn(ctx, "Unable to marshal", zap.String("error", err.Error()))
		return
	}
	var topic string
	var producer kafka.Producer
	switch tenant {
	case "blinkit":
		topic = fmt.Sprintf(`%s.%s`, tenant, key)
	case "district":
		topic = fmt.Sprintf(`%s.%s`, tenant, key)
	case "nugget":
		topic = fmt.Sprintf(`%s.%s`, tenant, key)
	default:
		topic = "flash." + key
	}

	producer, ok := kafka.Producers[tenant]
	if !ok {
		producer = kafka.DefaultProducer
	}
	producer.PushStringWithSegment(ctx, topic, string(result), messageKey)
}

// getProtoMessage returns proto message basede on conditions
func getProtoMessage(key, eventName string, payload interface{}) proto.Message {
	if key == "jadtracking" && eventName == "click" {
		if payloadInterface, ok := payload.(models.JAdTrackingPayload); ok {
			protoMessage := &ads.AdClickEvent{
				UserId:     payloadInterface.UserID,
				CampaignId: payloadInterface.CampaignID,
				EntityId:   payloadInterface.EntityID,
				EntityType: payloadInterface.EntityType,
				Timestamp:  payloadInterface.Time,
			}

			return protoMessage
		}
	}

	if key == "jevent" && helpers.IsInStringSlice(eventName, []string{"O2DishAdded", "O2FirstDishAdded", "O2MenuDropOff", "PUMenuDropOff"}) {
		if payloadInterface, ok := payload.(models.JEventsPayload); ok {
			protoMessage := &menu.MenuDishAddEvent{
				Source:             payloadInterface.Source,
				DeviceId:           payloadInterface.DeviceID,
				SessionId:          payloadInterface.SessionID,
				UserAgent:          payloadInterface.UserAgent,
				UserId:             payloadInterface.UserID,
				Time:               payloadInterface.Time,
				Location:           payloadInterface.Location,
				Var1:               payloadInterface.Var1,
				Var2:               payloadInterface.Var2,
				Var3:               payloadInterface.Var3,
				Var4:               payloadInterface.Var4,
				Var5:               payloadInterface.Var5,
				Var6:               payloadInterface.Var6,
				EventName:          payloadInterface.EventName,
				IngestionTimestamp: payloadInterface.IngestionTime,
			}

			anyMessage, err := ptypes.MarshalAny(protoMessage)
			if err != nil {
				return nil
			}

			uuid := uuid.New().String()

			return &event.Event{
				Header: &event.Header{
					TraceId: uuid,
					Uuid:    uuid,
				},
				Payload: anyMessage,
			}
		}
	}

	if key == "jevent" && helpers.IsInStringSlice(eventName, []string{"DeviceLimitingBottomSheetImpression", "DeviceLimitingBottomSheetButtonTap"}) {
		if payloadInterface, ok := payload.(models.JEventsPayload); ok {

			protoMessage := &subs.DeviceBannerTrackingEvent{
				Source:      payloadInterface.Source,
				DeviceId:    payloadInterface.DeviceID,
				SessionId:   payloadInterface.SessionID,
				UserAgent:   payloadInterface.UserAgent,
				UserId:      cast.ToUint64(payloadInterface.UserID),
				Time:        cast.ToUint64(payloadInterface.Time),
				Location:    cast.ToUint32(payloadInterface.Location),
				EventName:   payloadInterface.EventName,
				EventSource: payloadInterface.Var1,
			}

			anyMessage, err := ptypes.MarshalAny(protoMessage)
			if err != nil {
				return nil
			}

			uuid := uuid.New().String()

			return &event.Event{
				Header: &event.Header{
					TraceId: uuid,
					Uuid:    uuid,
				},
				Payload: anyMessage,
			}
		}

	}

	if key == "jevent" &&
		helpers.IsInStringSlice(eventName,
			[]string{
				"MembershipExpiredBottomSheetImpression",
				"AutoAddMembershipBottomSheetImpression",
				"CitySaleBottomSheetImpression",
				"NewUserBottomSheetImpression",
				"LapsedUserFreeTrialBottomSheetImpression",
				"GoldUfrBannerImpression",
				"FlashSaleBottomSheetImpression",
				"DCPackPurchaseNudgeTabbedHomeImpression",
				"GoldMembershipPageSuccessImpression",
				"O2DCPackPageLoaderViewed",
				"O2GoldPageLoaderViewed",
				"O2GoldPageLoaderLostSavingsViewed",
				"POISaleBottomSheetImpression",
			},
		) {
		if payloadInterface, ok := payload.(models.JEventsPayload); ok {

			protoMessage := &subtrackingevents.TrackingEvent{
				Source:      payloadInterface.Source,
				DeviceId:    payloadInterface.DeviceID,
				SessionId:   payloadInterface.SessionID,
				UserAgent:   payloadInterface.UserAgent,
				UserId:      cast.ToUint64(payloadInterface.UserID),
				Time:        cast.ToUint64(payloadInterface.Time),
				Location:    cast.ToUint32(payloadInterface.Location),
				EventName:   payloadInterface.EventName,
				EventSource: payloadInterface.Var2,
			}

			if eventName == "O2GoldPageLoaderViewed" {
				protoMessage.PlanType = payloadInterface.Var3
			}

			if helpers.IsInStringSlice(eventName, []string{"CitySaleBottomSheetImpression", "FlashSaleBottomSheetImpression", "POISaleBottomSheetImpression"}) {
				protoMessage.CampaignId = payloadInterface.Var6
			}

			anyMessage, err := ptypes.MarshalAny(protoMessage)
			if err != nil {
				return nil
			}

			uuid := uuid.New().String()

			return &event.Event{
				Header: &event.Header{
					TraceId: uuid,
					Uuid:    uuid,
				},
				Payload: anyMessage,
			}
		}

	}

	if key == "subscription_tracking_events" &&
		helpers.IsInStringSlice(eventName,
			[]string{
				"EVENT_TYPE_GOLD_LITE_PURCHASE_NUDGE_BOTTOMSHEET_IMPRESSION",
				"EVENT_TYPE_GOLD_MEMBERSHIP_EXPIRED_V2_BOTTOMSHEET_IMPRESSION",
				"EVENT_TYPE_DC_PACKS_UPGRADED_BOTTOMSHEET_IMPRESSION",
				"EVENT_TYPE_DISTANCE_EXTENSION_BOTTOMSHEET_IMPRESSION",
				"EVENT_TYPE_NOTIFY_PROMO_CODE_BUTTON_TAP",
				"EVENT_TYPE_HOME_PILL_LOTTIE_IMPRESSION",
				"EVENT_TYPE_EXPIRY_RENEWAL_LOST_SAVINGS_BANNER_IMPRESSION",
				"EVENT_TYPE_EXPIRY_RENEWAL_SAVINGS_BANNER_IMPRESSION",
				"EVENT_TYPE_EXPIRY_RENEWAL_NEUTRAL_BANNER_IMPRESSION",
			},
		) {
		if payloadInterface, ok := payload.(models.SubscriptionTrackingPayload); ok {
			protoMessage := &subtrackingevents.TrackingEvent{
				Source:     payloadInterface.Source,
				DeviceId:   payloadInterface.DeviceID,
				SessionId:  payloadInterface.SessionID,
				UserAgent:  payloadInterface.UserAgent,
				UserId:     cast.ToUint64(payloadInterface.UserID),
				Time:       cast.ToUint64(payloadInterface.Time),
				Location:   cast.ToUint32(payloadInterface.Location),
				EventName:  payloadInterface.EventType,
				OrderId:    cast.ToString(payloadInterface.OrderID),
				PromoId:    payloadInterface.PromoId,
				CampaignId: payloadInterface.CampaignID,
			}

			anyMessage, err := ptypes.MarshalAny(protoMessage)
			if err != nil {
				return nil
			}

			uuid := uuid.New().String()

			return &event.Event{
				Header: &event.Header{
					TraceId: uuid,
					Uuid:    uuid,
				},
				Payload: anyMessage,
			}
		}

	}

	if key == "live_order_service_events" {
		if helpers.IsInStringSlice(eventName, []string{"O2CrystalCarouselImpression"}) {
			payloadInterface, ok := payload.(models.LiveOrderServiceEvents)
			if ok &&
				payloadInterface.TrackingState != "" &&
				helpers.IsInStringSlice(payloadInterface.TrackingState, []string{}) {
				protoMessage := &subtrackingevents.TrackingEvent{
					EventName:            payloadInterface.EventName,
					Source:               payloadInterface.Source,
					DeviceId:             payloadInterface.DeviceID,
					SessionId:            payloadInterface.SessionID,
					UserAgent:            payloadInterface.UserAgent,
					UserId:               cast.ToUint64(payloadInterface.UserID),
					Time:                 cast.ToUint64(payloadInterface.Time),
					Location:             cast.ToUint32(payloadInterface.Location),
					OrderId:              cast.ToString(payloadInterface.OrderId),
					CrystalTrackingState: cast.ToString(payloadInterface.TrackingState),
				}

				anyMessage, err := anypb.New(protoMessage)
				if err != nil {
					return nil
				}

				uuid := uuid.New().String()
				return &event.Event{
					Header: &event.Header{
						TraceId: uuid,
						Uuid:    uuid,
					},
					Payload: anyMessage,
				}
			}
		}
	}

	if key == "jevent" && helpers.IsInStringSlice(eventName, []string{"GoldIntermediateMilestonePopupImpression", "GoldDefaultMilestonePopupImpression"}) {
		if payloadInterface, ok := payload.(models.JEventsPayload); ok {

			protoMessage := &subtrackingevents.TrackingEvent{
				UserId:    cast.ToUint64(payloadInterface.Var2),
				EventName: payloadInterface.EventName,
				OrderId:   payloadInterface.Var3,
			}

			anyMessage, err := ptypes.MarshalAny(protoMessage)
			if err != nil {
				return nil
			}

			uuid := uuid.New().String()

			return &event.Event{
				Header: &event.Header{
					TraceId: uuid,
					Uuid:    uuid,
				},
				Payload: anyMessage,
			}
		}
	}

	if key == "jevent" &&
		helpers.IsInStringSlice(
			eventName,
			[]string{
				"GoldUfrExtraFreeDaysPopupImpression",
				"GoldExpiryExtraFreeDaysPopupImpression",
				"HomePillLottieImpression",
				"HomePageTopAnimationImpression",
			},
		) {
		if payloadInterface, ok := payload.(models.JEventsPayload); ok {

			protoMessage := &subtrackingevents.TrackingEvent{
				UserId:      cast.ToUint64(payloadInterface.UserID),
				EventName:   payloadInterface.EventName,
				OrderId:     payloadInterface.Var3,
				EventSource: payloadInterface.Var1,
			}

			if eventName == "HomePillLottieImpression" {
				protoMessage.CampaignId = payloadInterface.Var2
			}

			anyMessage, err := ptypes.MarshalAny(protoMessage)
			if err != nil {
				return nil
			}

			uuid := uuid.New().String()

			return &event.Event{
				Header: &event.Header{
					TraceId: uuid,
					Uuid:    uuid,
				},
				Payload: anyMessage,
			}
		}
	}

	return nil
}

func CreateClevertapProcessor(ctx context.Context) func(messages []interface{}) (int, error) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "Handlers/CreateClevertapProcessor", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}
	return func(messages []interface{}) (int, error) {
		defer newrelic.StartNonWebTransaction("PIPELINE_CT_INGEST").End()

		events := make([]clevertap.ClevertapEventData, 0)
		ev, err := env.FromContext(ctx)
		if err != nil {
			return len(events), err
		}
		client := ev.Clevertap()

		for _, message := range messages {
			if event, ok := message.(clevertap.ClevertapEventData); ok {
				events = append(events, event)
			}
		}
		log.Info(fmt.Sprintf("Processing %v Clevertap Event(s)", len(events)))

		faultyEventsCount, err := client.UploadEvents(ctx, events)
		statsd.RecordIncrMetric(ctx, "clevertap_failed_messages", int64(faultyEventsCount))
		if err != nil {
			newrelic.NoticeError(ctx, err)
		}
		return faultyEventsCount, err
	}
}
