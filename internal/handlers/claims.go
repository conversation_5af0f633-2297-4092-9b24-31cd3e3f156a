package handlers

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"reflect"
	"strings"
	"time"

	"github.com/Zomato/flash-gateway/internal/models"
	jumbo_v2_event_consumer "github.com/Zomato/flash-gateway/internal/transformers/jumbo-v2-event-consumer"

	"github.com/Shopify/sarama"
	"github.com/Zomato/flash-gateway/internal/constants"
	"github.com/Zomato/flash-gateway/internal/enricher"
	"github.com/Zomato/flash-gateway/internal/helpers"

	// "github.com/Zomato/flash-gateway/internal/transformers"
	clevertapTransforms "github.com/Zomato/flash-gateway/internal/transformers/destinations/clevertap"

	gateway "github.com/Zomato/flash-gateway-client-golang/gateway"
	"github.com/Zomato/flash-gateway/pkg/services/cache"
	"github.com/Zomato/flash-gateway/pkg/services/cache/redis"
	"github.com/Zomato/flash-gateway/pkg/services/clevertap"
	"github.com/Zomato/flash-gateway/pkg/services/kafka"
	"github.com/Zomato/flash-gateway/pkg/services/statsd"
	ztracer "github.com/Zomato/flash-gateway/pkg/tracer"
	"github.com/Zomato/flash-gateway/pkg/workerpool"
	"github.com/Zomato/go/config"
	log "github.com/Zomato/go/logger"
	"github.com/Zomato/go/newrelic"
	"github.com/Zomato/go/tracer"
	jumboEvent "github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/event"

	zomatoEvent "github.com/Zomato/zomato-event-registry-client-golang/zomato-event-registry/event"

	"github.com/golang/protobuf/proto"
	"github.com/spf13/viper"

	"go.uber.org/zap"
)

type Claim struct {
	ctx context.Context

	JobType string
}

func NewClaim(ctx context.Context) *Claim {
	return &Claim{ctx: ctx}
}

func (c *Claim) ConsumerClaimWrapper(claimFunc func(ctx context.Context, message *sarama.ConsumerMessage) error) func(session sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
	return func(session sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
		defer func() {
			if err := recover(); err != nil {
				helpers.HandlePanic(c.ctx, err)
			}
		}()

		for message := range claim.Messages() {
			txn := newrelic.StartNonWebTransaction(c.JobType)
			ctx := newrelic.NewContext(c.ctx, txn)

			if ztracer.IsZTracerEnabled(ctx) {
				tctx, span := tracer.StartSpan(ctx, "Handlers/ConsumerClaimWrapper", tracer.WithSpanKind(tracer.SpanKindInternal))
				ctx = tctx
				defer span.End()
			}

			session.MarkMessage(message, "")
			if message != nil {
				start := time.Now()
				err := claimFunc(ctx, message)
				if err != nil {
					log.Error(c.ctx, "Unable to process message", zap.String("error", err.Error()))
					statsd.PushFailedMessagesCount(c.ctx)
				}

				statsd.PushEventProcessingTime(c.ctx, time.Since(start))
			}

			txn.End()
		}

		return nil
	}
}

// FlashConsumerClaimFunc must start a consumer loop of ConsumerGroupClaim's Messages().
func FlashConsumerClaimFunc(ctx context.Context, message *sarama.ConsumerMessage) error {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "Handlers/FlashConsumerClaimFunc", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}
	envelope := &gateway.MessageEnvelope{}
	if err := proto.Unmarshal(message.Value, envelope); err != nil {
		return errors.New("Failed to parse address envelope. Error: " + err.Error())
	}

	result, topic, err := helpers.GetParsedMessage(ctx, envelope)
	if result == "" || topic == "" || err != nil {
		return err
	}

	kafka.DefaultProducer.PushStringWithSegment(ctx, topic, result, message.Key)

	return nil
}

type alertingConsumerMessage struct {
	Title   string                 `json:"messageTitle"`
	Heading string                 `json:"messageHeading"`
	Metrics map[string]interface{} `json:"metrics"`
}

func AlertingConsumerClaimFunc(ctx context.Context, message *sarama.ConsumerMessage) error {
	statsd.RecordIncrMetric(ctx, "notify_alert_total_total", 1)
	var v alertingConsumerMessage
	err := json.Unmarshal(message.Value, &v)

	if err != nil {
		statsd.RecordIncrMetric(ctx, "notify_alert_failed_total", 1)
		return errors.New("Unable to unmarshal message. Error: " + err.Error())
	}

	j, err := json.Marshal(v)
	if err != nil {
		statsd.RecordIncrMetric(ctx, "notify_alert_failed_total", 1)
		return errors.New("Unable to marshal message. Error: " + err.Error())
	}
	resp, err := http.Post(config.GetString(ctx, "notify.url"), "application/json", bytes.NewBuffer(j))
	defer resp.Body.Close()
	if body, err := ioutil.ReadAll(resp.Body); err != nil {
		statsd.RecordIncrMetric(ctx, "notify_alert_failed_total", 1)
		return errors.New("Got error while reading teams api response body. Error: " + err.Error())
	} else {
		log.Info(string(body))
	}
	statsd.RecordIncrMetric(ctx, "notify_alert_success_total", 1)
	return nil
}

func CompositeConsumerClaimFunc(ctx context.Context, message *sarama.ConsumerMessage) error {

	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "Handlers/CompositeConsumerClaimFunc", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}

	envelope := &zomatoEvent.Event{}
	if err := proto.Unmarshal(message.Value, envelope); err != nil {
		return errors.New("Failed to parse address envelope. Error: " + err.Error())
	}

	result, err, kafka_message_key := helpers.GetZomatoEventParsedMessage(envelope)
	if result == "" || err != nil {
		return err
	}

	topic := config.GetString(ctx, "kafka.compositeeventsoutputtopic")

	kafka.DefaultProducer.PushStringWithSegment(ctx, topic, result, []byte(kafka_message_key))

	return nil
}

func DistrictJumboV2EventConsumerClaimFunc(ctx context.Context, message *sarama.ConsumerMessage) error {
	return JumboV2EventConsumerClaimFunc(ctx, constants.DISTRICT_TENANT, message)
}

func ZomatoJumboV2EventConsumerClaimFunc(ctx context.Context, message *sarama.ConsumerMessage) error {
	return JumboV2EventConsumerClaimFunc(ctx, constants.ZOMATO_TENANT, message)
}

func BlinkitJumboV2EventConsumerClaimFunc(ctx context.Context, message *sarama.ConsumerMessage) error {
	return JumboV2EventConsumerClaimFunc(ctx, constants.BLINKIT_TENANT, message)
}

func NuggetJumboV2EventConsumerClaimFunc(ctx context.Context, message *sarama.ConsumerMessage) error {
	return JumboV2EventConsumerClaimFunc(ctx, constants.NUGGET_TENANT, message)
}

// JumboV2EventConsumerClaimFunc consumer to consume proto messages and output json, output of protoconvertor and make transformations
func JumboV2EventConsumerClaimFunc(ctx context.Context, tenant string, message *sarama.ConsumerMessage) error {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "Handlers/JumboV2EventConsumerClaimFunc", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}
	envelope := &jumboEvent.JumboEvent{}
	if err := proto.Unmarshal(message.Value, envelope); err != nil {
		return errors.New("Failed to parse address envelope. Error: " + err.Error())
	}

	var transformedEvents []*models.JumboV2TransformedEvent
	var err error

	customFunction, found := jumbo_v2_event_consumer.CustomHandlingJumboV2Map[tenant][envelope.GetTable()]
	namespace := envelope.GetHeader().GetNamespace().String()
	if found {
		transformedEvents, err = customFunction(ctx, envelope, message.Key)
		if err != nil {
			return errors.New("failed to apply custom transformer. error: " + err.Error())
		}
	} else {
		transformedEvents, err = jumbo_v2_event_consumer.GetJumboV2FlattenedJSONMap(envelope, tenant, message.Key)
		if err != nil {
			return errors.New("failed to apply default transformer. error: " + err.Error())
		}
	}

	for _, event := range transformedEvents {
		if event.Broker == "" && event.Topic == "" {
			if strings.Contains(message.Topic, "blinkit") {
				event.Topic = "jumbo_transformed.blinkit." + event.Table
				// Get the topic-specific broker configuration
				topicBroker := viper.GetString("job_groups.blinkit.jumbo_v2_consumer_topics." + message.Topic)
				if topicBroker != "" {
					event.Broker = topicBroker
				} else {
					event.Broker = "blinkit"
					event.Broker = viper.GetString("job_groups.blinkit.jumbo_v2_consumer_topics.default")
				}
			} else if strings.Contains(message.Topic, "district") {
				event.Topic = "jumbo_transformed.district." + event.Table
				event.Broker = "district"
			} else if strings.Contains(namespace, "NUGGET") {
				event.Topic = "jumbo_transformed." + strings.ToLower(namespace) + "." + event.Table
				event.Broker = "default"
			} else {
				event.Topic = "jumbo_transformed." + event.Table
				event.Broker = "default"
			}
		}

		topicProducer, exists := kafka.Producers[event.Broker]
		if !exists {
			return fmt.Errorf("specified [%s] producer is not initialised", event.Broker)
		}
		topicProducer.PushStringWithSegment(ctx, event.Topic, string(event.Payload), event.Key)
	}
	return nil
}

func CleverTapConsumerClaimFunc(ctx context.Context) func(ctx context.Context, message *sarama.ConsumerMessage) error {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "Handlers/CleverTapConsumerClaimFunc", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}
	jobType := ctx.Value(constants.JobNameCtxKey).(string)
	label := ctx.Value(constants.TenantCtxKey).(string)
	processClevertapEvents := CreateClevertapProcessor(ctx)
	flushinterval, _ := time.ParseDuration(helpers.FetchConfig(ctx, "clevertap.producers.%s.workerpool.flushinterval", label).(string))
	workerPool, err := workerpool.New(
		jobType,
		int32(helpers.FetchConfig(ctx, "clevertap.producers.%s.workerpool.size", label).(int)),
		uint32(helpers.FetchConfig(ctx, "clevertap.producers.%s.workerpool.batchsize", label).(int)),
		flushinterval,
		uint32(helpers.FetchConfig(ctx, "clevertap.producers.%s.workerpool.queuelength", label).(int)),
		processClevertapEvents,
		reflect.TypeOf(clevertap.ClevertapEventData{}),
	)

	if err != nil {
		log.Error(ctx, "Unable to register worker pool", zap.Error(err))
		return nil
	}

	workerPool.Start()

	return func(ctx context.Context, message *sarama.ConsumerMessage) error {
		ctEvent, err := clevertapTransforms.TransformEvent(message.Value)
		if err != nil {
			return errors.New("Unable to process event to clevertap format. Error: " + err.Error())
		}

		statsd.RecordIncrMetric(ctx, fmt.Sprintf("clevertap_consumed_messages:%v:%v", ctEvent.Type, ctEvent.EventName), 1)

		c, err := json.Marshal(ctEvent)
		if err != nil {
			return errors.New("Unable to marshal event to clevertap format. Error: " + err.Error())
		}
		log.Debug("Processing Clevertap Event", zap.Any("event", string(c)))

		success := workerPool.Enqueue(ctEvent, message.Partition)
		if !success {
			statsd.RecordIncrMetric(ctx, "failed_enqueue_messages_total", 1)
			log.Error("Unable to enqueue message", zap.Any("message", ctEvent))
		}

		return nil
	}
}

type appPayload struct {
	Header  map[string]interface{} `json:"header"`
	Payload map[string]interface{} `json:"payload"`
}

type appPayloadWrapper struct {
	AppPayload []appPayload `json:"app_payload"`
}

// JumboV2ConsumerClaimFunc must start a consumer loop of ConsumerGroupClaim's Messages().
func JumboV2ConsumerClaimFunc(ctx context.Context, message *sarama.ConsumerMessage) error {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "Handlers/JumboV2ConsumerClaimFunc", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}
	var payloads appPayloadWrapper
	err := json.Unmarshal(message.Value, &payloads)
	if err != nil {
		return errors.New("Unable to unmarshal message. Error: " + err.Error())
	}

	for _, payload := range payloads.AppPayload {
		key := payload.Payload["key"]
		if key == "" {
			log.Warn(ctx, "Unable to get key", zap.Any("payload", payload.Payload))
			continue
		}

		eventTimestamp := int64(0)
		eventTimestampInterface := payload.Header["timestamp"]
		if eventTimestampInterface != nil {
			eventTimestamp = int64(eventTimestampInterface.(float64))
			if eventTimestamp == 0 {
				eventTimestamp = int64(payload.Header["time"].(float64))
			}
		} else {
			eventTimestamp = int64(payload.Header["time"].(float64))
		}

		statsd.PushEventLag(helpers.ComputeEventLag(time.Unix(eventTimestamp, 0)), ctx)

		ipAddress := payload.Header["ip_address"]
		if ipAddress == nil {
			ipAddress = ""
		}

		var value map[string]interface{}
		valuePayload := payload.Payload["value"]
		if valuePayload != nil {
			value = valuePayload.(map[string]interface{})
		} else {
			value = make(map[string]interface{})
		}

		value["key"] = key.(string)
		value["time"] = eventTimestamp
		value["location"] = payload.Header["location"]
		value["url"] = payload.Payload["url"]
		value["source"] = payload.Header["source"]
		value["device_id"] = payload.Header["device_id"]
		value["session_id"] = payload.Header["session_id"]
		value["user_id"] = payload.Header["user_id"]
		value["user_agent"] = payload.Header["user_agent"]
		value["ip_address"] = ipAddress

		result, err := json.Marshal(value)
		if err != nil {
			log.Warn(ctx, "Unable to marshal", zap.String("error", err.Error()))
			continue
		}

		topic := "flash." + key.(string)
		kafka.DefaultProducer.PushStringWithSegment(ctx, topic, string(result), message.Key)
	}

	return nil
}

// EnricherConsumeClaimFunc must start a consumer loop of ConsumerGroupClaim's Messages().
func EnricherConsumeClaimFunc(ctx context.Context, message *sarama.ConsumerMessage) error {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "Handlers/EnricherConsumeClaimFunc", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}
	result, topic, err := enricher.GetEnrichedMessage(ctx, message)
	if topic == "" || result == "" {
		return nil
	}

	if err != nil {
		return errors.New("Unable to compute enriched result. Enriched Topic: " + topic + ". Result: " + result)
	}

	producer := kafka.Producers[config.GetString(ctx, "enricher.kafka")]
	producer.PushStringWithSegment(ctx, topic, result, message.Key)
	return nil
}

type enrichmentIngestorMessage struct {
	Key    string                 `json:"key"`
	Fields map[string]interface{} `json:"fields"`
}

func EnrichmentIngestorClaimFuncV2(ctx context.Context) func(ctx context.Context, message *sarama.ConsumerMessage) error {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "Handlers/EnrichmentIngestorClaimFuncV2", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}
	jobType := ctx.Value(constants.JobNameCtxKey).(string)
	ingestEnrichmentMessages := CreateEnrichmentIngestor(ctx)
	workerPool, err := workerpool.New(
		jobType,
		config.GetInt32(ctx, "workerpool.size"),
		config.GetUint32(ctx, "workerpool.batchsize"),
		config.GetDuration(ctx, "workerpool.flushinterval"),
		config.GetUint32(ctx, "workerpool.queuelength"),
		ingestEnrichmentMessages,
		reflect.TypeOf(redis.HMSETPair{}),
	)

	if err != nil {
		log.Error(ctx, "Unable to register worker pool", zap.Error(err))
	}

	workerPool.Start()

	return func(ctx context.Context, message *sarama.ConsumerMessage) error {
		var v enrichmentIngestorMessage
		err := json.Unmarshal(message.Value, &v)
		if err != nil {
			return errors.New("Unable to unmarshal message. Error: " + err.Error())
		}

		m := redis.HMSETPair{
			Key:    v.Key,
			Fields: v.Fields,
		}
		success := workerPool.Enqueue(m, message.Partition)
		if !success {
			statsd.PushFailedEnqueueMessagesTotal(ctx)
			log.Error("Unable to enqueue message", zap.Any("message", m))
		}

		return nil
	}
}

func CreateEnrichmentIngestor(ctx context.Context) func(messages []interface{}) (int, error) {
	if ztracer.IsZTracerEnabled(ctx) {
		tctx, span := tracer.StartSpan(ctx, "Handlers/CreateEnrichmentIngestor", tracer.WithSpanKind(tracer.SpanKindInternal))
		ctx = tctx
		defer span.End()
	}
	return func(messages []interface{}) (int, error) {
		log.Info(fmt.Sprintf("Processing %v enrichment messages", len(messages)))
		pairs := make([]redis.HMSETPair, 0)
		for _, message := range messages {
			if pair, ok := message.(redis.HMSETPair); ok {
				pairs = append(pairs, pair)
			}
		}
		log.Debug(pairs)

		txn := newrelic.StartNonWebTransaction("PIPELINE_HMSET")
		ctx := newrelic.NewContext(ctx, txn)
		defer txn.End()

		log.Info(fmt.Sprintf("Ingesting %v enrichment messages", len(pairs)))
		err := cache.HMSet(ctx, pairs...)
		if err != nil {
			statsd.RecordIncrMetric(ctx, "enrichment_ingestor_failed_messages", int64(len(pairs)))
			return len(pairs), errors.New("Unable to set keys. Error: " + err.Error())
		}

		return 0, nil
	}
}
