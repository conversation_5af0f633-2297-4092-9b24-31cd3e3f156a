package handlers

import (
	"fmt"
	"strconv"
	"time"

	"github.com/Zomato/flash-gateway/internal/helpers"
	log "github.com/Zomato/go/logger"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/event"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/platform/jumbofailedevents"
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/zomato/web/jevent"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"
	googleproto "google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"
	"google.golang.org/protobuf/types/known/wrapperspb"
)

var CustomHandlingMap = func() map[string]func(string) string {
	return map[string]func(string) string{
		"ZOMATO.app_error_metrics":           getAppMetricsString,
		"ZOMATO.app_request_metrics":         getAppMetricsString,
		"ZOMATO.jadtracking":                 getJadTrackingString,
		"ZOMATO.pageview":                    getPageviewString,
		"ZOMATO.ztracking":                   getZtrackingString,
		"ZOMATO.userlifecycle":               getUserLifecycleString,
		"ZOMATO.raw_driver_events":           getRawDriverEventsString,
		"ZOMATO.delivery_driver_actions":     getDelieveryDriverActionsString,
		"ZOMATO.zautosuggestion_events_log":  getZautoSuggestionLogsString,
		"ZOMATO.driver_accounting_ledgers":   getDriverAccountingLedgers,
		"BLINKIT.blinkit_app_events":         getBlinkitAppEventsString,
		"BLINKIT.click_events":               getSplitBlinkitConsumerEventsString,
		"BLINKIT.impression_events":          getSplitBlinkitConsumerEventsString,
		"BLINKIT.product_image_shown_events": getSplitBlinkitConsumerEventsString,
		"BLINKIT.blinkit_web_events":         getBlinkitWebEventsString,
		"BLINKIT.bistro_click_events":        getBistroAppEventsString,
		"BLINKIT.bistro_impression_events":   getBistroAppEventsString,
		"ZOMATO.chat_events":                 getChatEventsString,
		"ZOMATO.apitrack":                    getApiTrackString,
	}
}

var BlinkitAppEventsFieldMap = map[string]string{
	"properties.favourite_icon_state":      "String",
	"properties.product_list_id":           "String",
	"properties.entry_source_position":     "String",
	"properties.is_coupon_applicable":      "String",
	"properties.subcategory_id":            "String",
	"properties.suggestion_value":          "String",
	"properties.order_id":                  "String",
	"properties.status":                    "String",
	"properties.notification_type":         "String",
	"properties.message":                   "String",
	"properties.order_status":              "String",
	"properties.location_fetch_accuracy":   "String",
	"properties.location_fetch_altitude":   "String",
	"properties.post_change_longitude":     "String",
	"properties.post_change_latitude":      "String",
	"properties.additional_charge_amount":  "String",
	"properties.cart_id":                   "String",
	"properties.widget_revision_id":        "String",
	"properties.widget_variation_id":       "String",
	"properties.child_widget_revision_id":  "String",
	"properties.child_widget_variation_id": "String",
	"properties.entry_source_id":           "String",
	"properties.data1":                     "String",
	"properties.rating":                    "String",
	"properties.address_id":                "String",
	"properties.brand":                     "String",
	"properties.ads_asset_type_id":         "String",
	"properties.custom_data":               "String",
	"properties.id":                        "String",
	"properties.state_data":                "String",
	"properties.type":                      "String",
	"properties.inventory_limit":           "Int32",
	"properties.sbc_price":                 "Int32",
	"properties.index_of_suggestion":       "Int32",
	"properties.ads_campaign_id":           "Int32",
	"properties.search_result_count":       "Int32",
	"properties.widget_position":           "Int32",
	"properties.widget_impression_count":   "Int32",
	"properties.product_id":                "Int64",
}

var RootBlinkitAppEventsFieldMap = map[string]string{
	"favourite_icon_state":      "String",
	"product_list_id":           "String",
	"entry_source_position":     "String",
	"is_coupon_applicable":      "String",
	"subcategory_id":            "String",
	"suggestion_value":          "String",
	"order_id":                  "String",
	"status":                    "String",
	"notification_type":         "String",
	"message":                   "String",
	"order_status":              "String",
	"location_fetch_accuracy":   "String",
	"location_fetch_altitude":   "String",
	"post_change_longitude":     "String",
	"post_change_latitude":      "String",
	"additional_charge_amount":  "String",
	"cart_id":                   "String",
	"widget_revision_id":        "String",
	"widget_variation_id":       "String",
	"child_widget_revision_id":  "String",
	"child_widget_variation_id": "String",
	"entry_source_id":           "String",
	"data1":                     "String",
	"rating":                    "String",
	"address_id":                "String",
	"brand":                     "String",
	"ads_asset_type_id":         "String",
	"custom_data":               "String",
	"id":                        "String",
	"state_data":                "String",
	"type":                      "String",
	"inventory_limit":           "Int32",
	"sbc_price":                 "Int32",
	"index_of_suggestion":       "Int32",
	"ads_campaign_id":           "Int32",
	"search_result_count":       "Int32",
	"widget_position":           "Int32",
	"widget_impression_count":   "Int32",
	"product_id":                "Int64",
}

var BlinkitCommonSet = map[string]bool{
	"cart_id":       true,
	"city_name":     true,
	"latitude":      true,
	"longitude":     true,
	"merchant_id":   true,
	"merchant_name": true,
}

var BlinkitTraitsSet = map[string]bool{
	"anonymousId":               true,
	"segment_type":              true,
	"app_details":               true,
	"app_version":               true,
	"device_details":            true,
	"memory_details":            true,
	"network_details":           true,
	"user_id":                   true,
	"userId":                    true,
	"app_flavor":                true,
	"cart_id":                   true,
	"chain_id":                  true,
	"channel":                   true,
	"city_id":                   true,
	"city_name":                 true,
	"device_uuid":               true,
	"install_campaign":          true,
	"install_source":            true,
	"latitude":                  true,
	"lifetime_orders":           true,
	"longitude":                 true,
	"merchant_id":               true,
	"merchant_name":             true,
	"monthly_orders":            true,
	"segment_enabled_features":  true,
	"session_launch_source":     true,
	"session_uuid":              true,
	"total_order_value":         true,
	"user_type":                 true,
	"app_version_code":          true,
	"user_experiment_buckets":   true,
	"install_medium":            true,
	"install_referrer":          true,
	"is_default_merchant":       true,
	"tracking_id":               true,
	"appsflyer_app_instance_id": true,
	"firebase_app_instance_id":  true,
}

var BlinkitWebEventsFields = []string{
	"traits.user_id",
	"properties.search_keyword_parent",
}

func getJeventProto(message string) (googleproto.Message, error) {
	ename := helpers.ConvertToString(gjson.Get(message, "ename"))
	if !helpers.IsJeventEnameWhitelisted(ename) {
		return nil, nil
	}
	return &jevent.JEvent{
		Ename: &wrapperspb.StringValue{Value: ename},
		Var1:  &wrapperspb.StringValue{Value: helpers.ConvertToString(gjson.Get(message, "var1"))},
		Var2:  &wrapperspb.StringValue{Value: helpers.ConvertToString(gjson.Get(message, "var2"))},
		Var3:  &wrapperspb.StringValue{Value: helpers.ConvertToString(gjson.Get(message, "var3"))},
		Var4:  &wrapperspb.StringValue{Value: helpers.ConvertToString(gjson.Get(message, "var4"))},
		Var5:  &wrapperspb.StringValue{Value: helpers.ConvertToString(gjson.Get(message, "var5"))},
		Var6:  &wrapperspb.StringValue{Value: helpers.ConvertToString(gjson.Get(message, "var6"))},
	}, nil
}

func getJumboFailedEventsProto(message string, error string, table_name string) googleproto.Message {
	return &jumbofailedevents.JumboFailedEvents{
		MessageString: &wrapperspb.StringValue{Value: message},
		Error:         &wrapperspb.StringValue{Value: error},
		TableName:     &wrapperspb.StringValue{Value: table_name},
	}
}

func getHeaderProto(headerString string) (*event.Header, error) {
	timeString := helpers.GetMultipleValueResults(headerString, "timestamp", "time")
	var timeinMillis int64
	if timeString.Exists() {
		timeinMillis = int64(helpers.GetOptTime(int(timeString.Int())))
	} else {
		timeinMillis = time.Now().Unix()
	}
	var namespace string
	if gjson.Get(headerString, "namespace").Exists() {
		namespace = gjson.Get(headerString, "namespace").String()
	} else {
		namespace = "NAMESPACE_UNSPECIFIED"
	}

	ingestionTime := time.Now()
	return &event.Header{
		Source:        event.Source(event.Source_value[gjson.Get(headerString, "source").String()]),
		DeviceId:      &wrapperspb.StringValue{Value: gjson.Get(headerString, "device_id").String()},
		SessionId:     &wrapperspb.StringValue{Value: gjson.Get(headerString, "session_id").String()},
		UserId:        &wrapperspb.StringValue{Value: gjson.Get(headerString, "user_id").String()},
		Namespace:     event.Namespace(event.Namespace_value[namespace]),
		UserAgent:     &wrapperspb.StringValue{Value: helpers.GetMultipleValueResults(headerString, "user_agent", "user-agent").String()},
		Timestamp:     &timestamppb.Timestamp{Seconds: timeinMillis / 1000, Nanos: int32((timeinMillis % 1000) * 1000000)},
		Location:      &wrapperspb.UInt32Value{Value: uint32(gjson.Get(headerString, "location").Int())},
		IpAddress:     &wrapperspb.StringValue{Value: helpers.GetMultipleValueResults(headerString, "ip_address", "ip").String()},
		IngestionTime: &timestamppb.Timestamp{Seconds: ingestionTime.Unix(), Nanos: int32(ingestionTime.Nanosecond())},
		LocationInfo: &event.LocationInfo{
			UserDefinedLatitude:  &wrapperspb.DoubleValue{Value: *helpers.GetFloat64ValueFromJsonString(headerString, "location_info.user_defined_latitude")},
			UserDefinedLongitude: &wrapperspb.DoubleValue{Value: *helpers.GetFloat64ValueFromJsonString(headerString, "location_info.user_defined_longitude")},
			CurrentLongitude:     &wrapperspb.DoubleValue{Value: *helpers.GetFloat64ValueFromJsonString(headerString, "location_info.current_longitude")},
			CurrentLatitude:      &wrapperspb.DoubleValue{Value: *helpers.GetFloat64ValueFromJsonString(headerString, "location_info.current_latitude")},
		},
		AppInfo: &event.AppInfo{
			DevicePerformance: helpers.GetHeaderAppInfoDriverPerformance(headerString, "app_info.device_performance"),
			Theme:             event.ThemeType(event.ThemeType_value[gjson.Get(headerString, "app_info.theme").String()]),
			SystemTheme:       event.SystemTheme(event.SystemTheme_value[gjson.Get(headerString, "app_info.system_theme").String()]),
			AppAppearance:     event.AppAppearance(event.AppAppearance_value[gjson.Get(headerString, "app_info.app_appearance").String()]),
		},
	}, nil
}

func getAppMetricsString(message string) string {
	stacktrace := gjson.Get(message, "stacktrace")
	var err error
	modifiedMessage := message
	switch stacktrace.Type {
	case gjson.String:
		modifiedMessage, err = sjson.Delete(message, "stacktrace")
	}
	logError(err)
	return modifiedMessage
}
func getRawDriverEventsString(message string) string {

	message, err := sjson.Set(message, "driver_id", gjson.Get(message, "driver_id").String())
	logError(err)
	message, err = sjson.Set(message, "event_value", gjson.Get(message, "event_value").String())
	logError(err)

	return message
}

func getDelieveryDriverActionsString(message string) string {
	message, err := sjson.Set(message, "reason_subtype", gjson.Get(message, "reason_subtype").String())
	logError(err)
	return message
}

func getPageviewString(message string) string {
	if message == "" {
		return `{"referrer":"none"}`
	}
	if !helpers.IsJSONHacky(message) {
		return `{"referrer":"` + message + `"}`
	}
	return message

}
func getZautoSuggestionLogsString(message string) string {
	metadata := gjson.Get(message, "metadata")

	switch metadata.Type {
	case gjson.String:
		metadata, err := convertStringToMap(metadata.String())
		if err == nil {
			message, err = sjson.Set(message, "metadata", metadata)
			logError(err)
			return message
		}
	}

	return message
}

func getApiTrackString(message string) string {
	message, err := sjson.Set(message, "appversion", gjson.Get(message, "app_version").String())
	logError(err)
	message, err = sjson.Set(message, "fromdevice", gjson.Get(message, "from").String())
	logError(err)
	message, err = sjson.Set(message, "apptype", gjson.Get(message, "app_type").String())
	logError(err)
	headers := gjson.Get(message, "headers").String()
	if !helpers.IsJSONHacky(headers) || helpers.IsJSONArray(headers) || headers == "{}" {
		message, err = sjson.Set(message, "headers", map[string]string{})
		logError(err)
	}
	data := gjson.Get(message, "data").String()
	if helpers.IsJSONHacky(data) {
		message, err = sjson.Set(message, "data", data)
		logError(err)
	}

	return message
}

func getDriverAccountingLedgers(message string) string {
	message, err := sjson.Set(message, "info", gjson.Get(message, "info").String())
	logError(err)

	return message
}

func convertStringToMap(message string) (map[string]interface{}, error) {
	v := make(map[string]interface{})
	if err := json.Unmarshal([]byte(message), &v); err != nil {
		return nil, err
	}
	return v, nil
}

func getZtrackingString(message string) string {

	message, err := sjson.Set(message, "entity_id", gjson.Get(message, "entity_id").String())
	logError(err)
	message, err = sjson.Set(message, "zreferrer_id", gjson.Get(message, "zreferrer_id").String())
	logError(err)
	message, err = sjson.Set(message, "location_id", gjson.Get(message, "location_id").String())
	logError(err)
	message, err = sjson.Set(message, "current_lat", gjson.Get(message, "current_lat").String())
	logError(err)
	message, err = sjson.Set(message, "current_long", gjson.Get(message, "current_long").String())
	logError(err)

	return message

}

func logError(err error) {
	if err != nil {
		log.Error(err.Error())
	}
}
func getJadTrackingString(message string) string {
	data := gjson.Get(message, "data")
	isNewAd := gjson.Get(message, "isNewAd")
	message, err := sjson.Set(message, "data", data.String())
	logError(err)
	message, err = sjson.Set(message, "isnewad", isNewAd.String())

	logError(err)
	return message
}

func getUserLifecycleString(message string) string {
	positionNumber := gjson.Get(message, "position_number")
	entityId := gjson.Get(message, "entity_id")
	message, err := sjson.Set(message, "position_number", positionNumber.String())
	logError(err)
	message, err = sjson.Set(message, "entity_id", entityId.String())
	logError(err)
	return message
}

func convertDataType(datatype string, str_val string) interface{} {
	var new_val interface{}
	var err error
	switch datatype {
	case "String":
		new_val = str_val
	case "Int32":
		new_val, err = strconv.ParseInt(str_val, 10, 32)
	case "Int64":
		new_val, err = strconv.ParseInt(str_val, 10, 64)
	case "Double":
		new_val, err = strconv.ParseFloat(str_val, 64)
	}
	if err != nil {
		logError(err)
		return nil
	} else {
		return new_val
	}
}

func formatBlinkitEvents(message *string, field string, datatype string) {
	fieldValue := gjson.Get(*message, field)
	if !fieldValue.Exists() {
		return
	}
	*message, _ = sjson.Set(*message, field, convertDataType(datatype, fieldValue.String()))
}

func nestBlinkitTraitsProperties(message *string) *string {
	value, ok := gjson.Parse(*message).Value().(map[string]interface{})
	if !ok {
		return nil
	}

	// Case if properties and traits are already nested
	traits_old, _ := value["traits"].(map[string]interface{})
	properties_old, _ := value["properties"].(map[string]interface{})
	if len(traits_old) != 0 && len(properties_old) != 0 {
		return nil
	}

	traits := make(map[string]interface{})
	properties := make(map[string]interface{})
	eventName, _ := value["event_name"]
	var populate_traits = true

	if len(traits_old) != 0 && len(properties_old) == 0 {
		traits = traits_old
		populate_traits = false
	}

	for key, val := range value {
		_, trait_exists := BlinkitTraitsSet[key]
		_, common_exists := BlinkitCommonSet[key]
		datatype, do_conversion := RootBlinkitAppEventsFieldMap[key]
		var new_val interface{}
		if do_conversion {
			new_val = convertDataType(datatype, fmt.Sprintf("%v", val))
		} else {
			new_val = val
		}
		if common_exists {
			properties[key] = new_val
		}
		if populate_traits && trait_exists {
			traits[key] = new_val
		} else if !trait_exists && key != "traits" {
			properties[key] = new_val
		}
	}
	data := map[string]interface{}{
		"traits":     traits,
		"properties": properties,
		"event_name": eventName}
	jsonData, err := json.Marshal(data)
	if err != nil {
		return nil
	}
	jsonDataStr := string(jsonData)
	return &jsonDataStr
}

func getSplitBlinkitConsumerEventsString(message string) string {
	// Nesting the event into properties and traits for blinkit consumer events
	nestedStringPtr := nestBlinkitTraitsProperties(&message)
	if nestedStringPtr != nil {
		return *nestedStringPtr
	}
	return getBlinkitAppEventsString(message)
}

func getBlinkitAppEventsString(message string) string {
	for field, datatype := range BlinkitAppEventsFieldMap {
		formatBlinkitEvents(&message, field, datatype)
	}
	return message
}

func getBistroAppEventsString(message string) string {
	for field, datatype := range RootBlinkitAppEventsFieldMap {
		formatBlinkitEvents(&message, field, datatype)
	}
	return message
}

func getBlinkitWebEventsString(message string) string {
	for _, field := range BlinkitWebEventsFields {
		var err error
		fieldValue := gjson.Get(message, field)
		if fieldValue.Exists() {
			message, err = sjson.Set(message, field, fieldValue.String())
			logError(err)
		}
	}
	return message
}

func getChatEventsString(message string) string {
	message, err := sjson.Set(message, "chat_user_id", gjson.Get(message, "chat_user_id").String())
	logError(err)
	message, err = sjson.Set(message, "app_version", gjson.Get(message, "app_version").String())
	logError(err)
	message, err = sjson.Set(message, "app_version", gjson.Get(message, "app_version").String())
	logError(err)
	dataMap, err := convertToStringMap(gjson.Get(message, "data").String())
	logError(err)
	message, err = sjson.Set(message, "data", dataMap)
	logError(err)
	metaMap, err := convertToStringMap(gjson.Get(message, "tags").String())
	logError(err)
	message, err = sjson.Set(message, "tags", metaMap)
	logError(err)

	return message
}

func convertToStringMap(inputString string) (map[string]string, error) {
	inputMap := make(map[string]interface{})
	err := json.UnmarshalFromString(inputString, &inputMap)
	if err != nil {
		return nil, err
	}
	outputMap := make(map[string]string)

	for key, value := range inputMap {
		if strVal, ok := value.(string); ok {
			outputMap[key] = strVal
		} else {
			x, err := json.Marshal(value)
			if err != nil {
				return nil, err
			}
			outputMap[key] = string(x)
		}
	}
	return outputMap, err
}

func getEventPriority(priorityLevelStr string) event.PriorityLevel {
	if v, ok := event.PriorityLevel_value[priorityLevelStr]; ok {
		return event.PriorityLevel(v)
	}

	priorityLevelInt, err := strconv.Atoi(priorityLevelStr)
	if err == nil {
		if _, ok := event.PriorityLevel_name[int32(priorityLevelInt)]; ok {
			return event.PriorityLevel(priorityLevelInt)
		}
	}

	return event.PriorityLevel_PRIORITY_LEVEL_UNSPECIFIED
}
