package constants

const (
	JobNameCtxKey                            = "job_name_ctx_key"
	TenantCtxKey                             = "tenant_ctx_key"
	JOBTYPE_FLASH_CONSUMER                   = "flash-consumer"
	JOBTYPE_JUMBO_CONSUMER_FRONTEND          = "jumbo-consumer-frontend"
	JOBTYPE_BLINKIT_JUMBO_CONSUMER_FRONTEND  = "blinkit-jumbo-consumer-frontend"
	JOBTYPE_JUMBO_CONSUMER_BACKEND           = "jumbo-consumer-backend"
	JOBTYPE_BLINKIT_JUMBO_CONSUMER_BACKEND   = "blinkit-jumbo-consumer-backend"
	JOBTYPE_JUMBO_V2_CONSUMER                = "jumbo-v2-consumer"
	JOBTYPE_ENRICHER                         = "enricher"
	JOBTYPE_ENRICHMENT_INGESTOR              = "enrichment-ingestor"
	JOBTYPE_ALERTING_CONSUMER                = "alerting-consumer"
	JOBTYPE_COMPOSITE_CONSUMER               = "composite-consumer"
	JOBTYPE_JUMBOV2_EVENTS_CONSUMER          = "jumbov2-events-consumer"
	JOBTYPE_DISTRICT_JUMBOV2_EVENTS_CONSUMER = "district-jumbov2-events-consumer"
	JOBTYPE_BLINKIT_JUMBOV2_EVENTS_CONSUMER  = "blinkit-jumbov2-events-consumer"
	JOBTYPE_NUGGET_JUMBOV2_EVENTS_CONSUMER   = "nugget-jumbov2-events-consumer"
	JOBTYPE_PROTO_CONVERTOR_BACKEND          = "proto-convertor-backend"
	JOBTYPE_PROTO_CONVERTOR_FRONTEND         = "proto-convertor-frontend"

	JOBTYPE_BLINKIT_PROTO_CONVERTOR          = "blinkit-proto-convertor"
	JOBTYPE_BLINKIT_PROTO_CONVERTOR_BACKEND  = "blinkit-proto-convertor-backend"
	JOBTYPE_BLINKIT_PROTO_CONVERTOR_FRONTEND = "blinkit-proto-convertor-frontend"

	JOBTYPE_BLINKIT_CLEVERTAP_CONSUMER  = "blinkit-clevertap-consumer"
	JOBTYPE_BISTRO_CLEVERTAP_CONSUMER   = "bistro-clevertap-consumer"
	PROTO_PACKAGE_NAME                  = "jumbo.eventregistry.zomato"
	TENANT_ZOMATO                       = "ZOMATO."
	TENANT_HYPERPURE                    = "HYPERPURE."
	HYPERPURE_TENANT                    = "hyperpure"
	TENANT_BLINKIT                      = "BLINKIT."
	ERRORS_TABLE                        = "jumbo_failed_events"
	ERRORS_TENANT_TABLE                 = TENANT_ZOMATO + ERRORS_TABLE
	PROTO_CONVERTOR_OUTPUT_TOPIC_PREFIX = "jumbo_streaming"
	ZOMATO_JUMBO_CONSUMER_FRONTEND      = "zomato-jumbo-consumer-frontend"
	ZOMATO_JUMBO_CONSUMER_BACKEND       = "zomato-jumbo-consumer-backend"
	ZOMATO_TENANT                       = "zomato"

	BLINKIT_JUMBO_CONSUMER_FRONTEND = "blinkit-jumbo-consumer-frontend"
	BLINKIT_JUMBO_CONSUMER_BACKEND  = "blinkit-jumbo-consumer-backend"
	BLINKIT_TENANT                  = "blinkit"
	BLINKIT_WARPSTREAM              = "blinkit_warpstream"

	DISTRICT_JUMBO_CONSUMER_BACKEND  = "district-jumbo-consumer-backend"
	DISTRICT_JUMBO_CONSUMER_FRONTEND = "district-jumbo-consumer-frontend"
	DISTRICT_TENANT                  = "district"

	OFFLINE_MSK                            = "offlineMsk"
	DEFAULT_BROKER                         = "default"
	TENANT_DISTRICT                        = "DISTRICT."
	TENANT_NUGGET                          = "NUGGET."
	JOBTYPE_DISTRICT_PROTO_CONVERTOR       = "district-proto-convertor"
	DriverLeadClientID                     = "1"
	JOBTYPE_DISTRICT_JUMBO_CONSUMER        = "district-jumbo-consumer"
	JOBTYPE_NUGGET_JUMBO_CONSUMER_FRONTEND = "nugget-jumbo-consumer-frontend"
	JOBTYPE_NUGGET_JUMBO_CONSUMER_BACKEND  = "nugget-jumbo-consumer-backend"
	NUGGET_TENANT                          = "nugget"

	JOBTYPE_FEEDINGINDIA_PROTO_CONVERTOR = "feedingindia-proto-convertor"
	TENANT_FEEDINGINDIA                  = "FEEDINGINDIA."
	FEEDINGINDIA_TENANT                  = "feedingindia"

	BACKEND_FLOW  = "backend_flow"
	FRONTEND_FLOW = "frontend_flow"
)

var PROTO_TABLE_CONVERT = map[string]string{"api.track": "apitrack", "user_lifecycle": "userlifecycle"}

var RiderAppMapping = map[string]string{
	"com.zomato.delivery":    "ZOMATO",
	"app.blinkit.onboarding": "BLINKIT",
}

var PROMO_EVENTS_WHITELISTED_EVENT_NAMES = []string{"PROMO_APPLIED", "PROMO_FAILED", "PROMO_IMPRESSION", "PROMO_APPLY_CLICKED"}
