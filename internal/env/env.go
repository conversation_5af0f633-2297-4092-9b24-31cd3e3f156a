package env

import (
	"context"
	"errors"

	"github.com/Zomato/flash-gateway/pkg/services/clevertap"
)

// Env is the application environment exposing application scoped instances
// in request handlers
type Env struct {
	clevertapClient   *clevertap.ClevertapClient
}

type envContextKey string

const (
	// EnvCtxKey is the key to set and retrieve Env in context
	EnvCtxKey envContextKey = "env"
)

// NewEnv returns a new Env instance
func NewEnv(options ...func(env *Env)) Env {
	env := Env{}

	for _, option := range options {
		option(&env)
	}

	return env
}

// WithClevertapClient sets a clevertap client in the Env
func WithClevertapClient(clevertapClient *clevertap.ClevertapClient) func(*Env) {
	return func(env *Env) {
		env.clevertapClient = clevertapClient
	}
}

// <PERSON>levertap retrieves the clevertap client from the Env
func (env *Env) Clevertap() *clevertap.ClevertapClient {
	return env.clevertapClient
}

// WithContext returns a context containing the env Value
func (env *Env) WithContext(ctx context.Context) context.Context {
	nctx := context.WithValue(ctx, EnvCtxKey, env)
	return nctx
}

// FromContext retrieves the Env from the current context, if no Env is found
// in the context and error is returned
func FromContext(ctx context.Context) (*Env, error) {
	env, ok := ctx.Value(EnvCtxKey).(*Env)
	if !ok {
		return env, errors.New("failed to get environment from context")
	}
	return env, nil
}
