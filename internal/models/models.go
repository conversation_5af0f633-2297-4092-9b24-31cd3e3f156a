package models

import (
	"github.com/Zomato/jumbo-event-registry-client-golang/jumbo/eventregistry/annotations"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/reflect/protoreflect"
)

type Header struct {
	Source        string       `json:"source"`
	DeviceID      string       `json:"device_id"`
	SessionID     string       `json:"session_id"`
	UserID        string       `json:"user_id"`
	UserAgent     string       `json:"user_agent"`
	Time          string       `json:"timestamp"`
	IngestionTime string       `json:"ingestion_time"`
	Location      string       `json:"location"`
	LocationInfo  LocationInfo `json:"location_info"`
}

type JEventsPayload struct {
	Header

	Key       string `json:"key"`
	EventName string `json:"ename"`
	Var1      string `json:"var1"`
	Var2      string `json:"var2"`
	Var3      string `json:"var3"`
	Var4      string `json:"var4"`
	Var5      string `json:"var5"`
	Var6      string `json:"var6"`
	Url       string `json:"url"`
}

type JAdTrackingPayload struct {
	Header

	BannerID          string   `json:"banner_id"`
	Bzone             string   `json:"bzone"`
	Action            string   `json:"action"`
	CollectionID      string   `json:"collection_id"`
	CategoryID        string   `json:"category_id"`
	SlotID            string   `json:"slot_id"`
	DisplayPage       string   `json:"display_page"`
	AdPosition        string   `json:"ad_position"`
	SliderPosition    string   `json:"slider_position"`
	SliderSequence    string   `json:"slider_sequence"`
	CreativeID        string   `json:"creative_id"`
	EntityType        string   `json:"entity_type"`
	EntityID          string   `json:"entity_id"`
	LocationID        string   `json:"location_id"`
	LocationType      string   `json:"location_type"`
	Rating            string   `json:"rating"`
	IsNewAd           string   `json:"isNewAd"`
	BillingUnit       string   `json:"billing_unit"`
	CampaignID        string   `json:"campaign_id"`
	ExperimentGroupID string   `json:"experiment_group_id"`
	Facets            []string `json:"facets"`
	Gamma             float64  `json:"gamma"`
	Key               string   `json:"key"`
	EncryptedPrice    string   `json:"encrypted_price"`
	FlinkDisplayPage  string   `json:"flink_display_page"`
}

type SubscriptionTrackingPayload struct {
	Header

	Key         string `json:"key"`
	EventType   string `json:"event_type"`
	SourceType  string `json:"source_type"`
	PlanType    string `json:"plan_type"`
	PlanID      string `json:"plan_id"`
	PlanGroupID string `json:"plan_group_id"`
	CampaignID  string `json:"campaign_id"`
	VoucherType string `json:"voucher_type"`
	OrderID     string `json:"order_id"`
	PromoId     string `json:"promo_id"`
}

type RawDriverEvents struct {
	Header
	DriverID   string            `json:"driver_id"`
	EventType  string            `json:"event_type"`
	AppVersion string            `json:"app_version"`
	EventName  string            `json:"event_name"`
	EventValue string            `json:"event_value"`
	Properties map[string]string `json:"properties"`
}

type EtaTracking struct {
	Header
	Category              string  `json:"category"`
	ResId                 int64   `json:"res_id"`
	OrderId               int64   `json:"order_id"`
	RawEta                float64 `json:"raw_eta"`
	SmoothenEta           float64 `json:"smoothen_eta"`
	Polyline              string  `json:"polyline"`
	OrderStatus           string  `json:"order_status"`
	UserLatitude          float64 `json:"user_latitude"`
	UserLongitude         float64 `json:"user_longitude"`
	RiderLatitude         float64 `json:"rider_latitude"`
	RiderLongitude        float64 `json:"rider_longitude"`
	ResLatitude           float64 `json:"res_latitude"`
	ResLongitude          float64 `json:"res_longitude"`
	PingTimestamp         int64   `json:"ping_timestamp"`
	Rat                   float64 `json:"rat"`
	Kpt                   float64 `json:"kpt"`
	PickupEta             float64 `json:"pickup_eta"`
	DropEta               float64 `json:"drop_eta"`
	PickupDistance        float64 `json:"pickup_distance"`
	DropDistance          float64 `json:"drop_distance"`
	AcceptanceTime        float64 `json:"acceptance_time"`
	Buffer                float64 `json:"buffer"`
	KptHandoverTime       float64 `json:"kpt_handover_time"`
	TimeToReach           float64 `json:"time_to_reach"`
	MetaDataIsLogs        string  `json:"metadata__is_logs"`
	MetaDataEstablishment string  `json:"metadata__establishment"`
	MetaDataCallerService string  `json:"metadata__caller_service"`
	MetaDataKptBuffer     string  `json:"metadata__kpt_buffer"`
	MetaDataRdtBuffer     string  `json:"metadata__rdt_buffer"`
}
type KarmaEvents struct {
	UserID                   string  `json:"user_id"`
	SessionID                string  `json:"session_id"`
	NmNationalProjectedScore int64   `json:"nm_national_projected_score"`
	PBin                     int64   `json:"p_bin"`
	RestrictedTimeInterval   string  `json:"restricted_time_interval"`
	CurrentTimeHrMin         string  `json:"current_time_hr_min"`
	ExperimentBucket         string  `json:"experiment_bucket"`
	NewUserFlag              bool    `json:"new_user_flag"`
	CityID                   uint32  `json:"city_id"`
	DszID                    int64   `json:"dsz_id"`
	ResID                    int64   `json:"res_id"`
	OfflineCodLimit          float32 `json:"offline_cod_limit"`
	RealtimeCodLimit         float32 `json:"realtime_cod_limit"`
	OfflineDszCodLimit       float32 `json:"offline_dsz_cod_limit"`
	OfflineResCodLimit       float32 `json:"offline_res_cod_limit"`
	ModelCodLimit            int64   `json:"model_cod_limit"`
	RealtimeRuleCodLimit     int64   `json:"realtime_rule_cod_limit"`
	RealtimeRuleName         string  `json:"realtime_rule_name"`
	HighRiskFlag             bool    `json:"high_risk_flag"`
	IsEternalCustomer        bool    `json:"is_eternal_customer"`
	ActionReason             string  `json:"action_reason"`
	CodLimit                 int64   `json:"cod_limit"`
	DefaultCodLimitFlag      bool    `json:"default_cod_limit_flag"`
	PreviousKarmaScore       string  `json:"previous_karma_score"`
	PreviousKarmaReason      string  `json:"previous_karma_reason"`
	NmKarmaScore             string  `json:"nm_karma_score"`
	NmKarmaReason            string  `json:"nm_karma_reason"`
	NmKarmaPolicyLabel       string  `json:"nm_karma_policy_label"`
	PreviousNmKarmaScore     string  `json:"previous_nm_karma_score"`
	PreviousNmKarmaReason    string  `json:"previous_nm_karma_reasonstring"`
	PenaltyBucket            string  `json:"penalty_bucket"`
	CartTime                 int64   `json:"cart_time"`
}

type EveryDayOrderRelayEvents struct {
	ResID       int64  `json:"res_id"`
	OrderStatus string `json:"order_status"`
	ResStatus   string `json:"res_status"`
	RelayMode   string `json:"relay_mode"`
	Timestamp   int64  `json:"timestamp"`
}

type LiveOrderServiceEvents struct {
	Header
	Key              string `json:"key"`
	OrderId          int64  `json:"order_id"`
	EventName        string `json:"event_name"`
	CarouselBannerId string `json:"carousel_banner_id"`
	VideoBannerId    string `json:"video_banner_id"`
	Timestamp        int64  `json:"timestamp"`
	TrackingState    string `json:"tracking_state"`
	PlanID           string `json:"plan_id"`
	GoldData         string `json:"gold_data"`
	UserId           string `json:"user_id"`
	BannerId         string `json:"banner_id"`
}

type AppPerformanceMetricsEvents struct {
	EventName                   string `json:"event_name"`
	PageName                    string `json:"page_name"`
	CountryId                   int32  `json:"country_id"`
	AppType                     string `json:"app_type"`
	AppVersion                  string `json:"app_version"`
	OsVersion                   string `json:"os_version"`
	DeviceName                  string `json:"device_name"`
	NetworkType                 string `json:"network_type"`
	NetworkOperator             string `json:"network_operator"`
	EntityId                    string `json:"entity_id"`
	EntityType                  string `json:"entity_type"`
	BusinessType                string `json:"business_type"`
	Duration                    int64  `json:"duration"`
	Component                   string `json:"component"`
	AppState                    string `json:"app_state"`
	FrameInfoSlowFrames         int32  `json:"frame_info__slow_frames"`
	FrameInfoFrameCounts        int32  `json:"frame_info__frame_counts"`
	MemoryInfoAppMemory         int32  `json:"memory_info__app_memory"`
	MemoryInfoScreenMemory      int32  `json:"memory_info__screen_memory"`
	MemoryInfoAvailableOSMemory int32  `json:"memory_info__available_os_memory"`
	ScreenTime                  int32  `json:"screen_time"`
	IsDebug                     bool   `json:"is_debug"`
	CityID                      int64  `json:"city_id"`
	Timestamp                   int64  `json:"timestamp"`
}

type ServiceabilityLocalityStats struct {
	LocalityID                 int32   `json:"locality_id"`
	LineOfBusiness             string  `json:"line_of_business"`
	CarrierType                string  `json:"carrier_type"`
	OrderRiderRatio            float64 `json:"order_rider_ratio"`
	FreeDriverMatchedCount     int64   `json:"free_driver_matched_count"`
	FreeDriverUnmatchedCount   int64   `json:"free_driver_unmatched_count"`
	QueuedDriverMatchedCount   int64   `json:"queued_driver_matched_count"`
	QueuedDriverUnmatchedCount int64   `json:"queued_driver_unmatched_count"`
	Timestamp                  int64   `json:"timestamp"`
}

type AdsCentralWebTracking struct {
	Timestamp int64  `json:"timestamp"`
	Url       string `json:"url"`
	EventType string `json:"event_type"`
	EventName string `json:"event_name"`
}

type WebviewTracking struct {
	Timestamp   int64  `json:"timestamp"`
	Url         string `json:"url"`
	EventType   string `json:"event_type"`
	Component   string `json:"component"`
	WebviewName string `json:"webview_name"`
}

type AdsStudioTracking struct {
	Timestamp int64  `json:"timestamp"`
	Url       string `json:"url"`
	EventType string `json:"event_type"`
	Component string `json:"component"`
}

type HyperpureOrderEvents struct {
	OrderPlacedTime  int64   `json:"order_placed_time"`
	BuyerOutletID    int32   `json:"buyer_outlet_id"`
	AccountID        int32   `json:"account_id"`
	OrderNumber      string  `json:"order_number"`
	OrderID          int64   `json:"order_id"`
	TotalOrderPrice  float64 `json:"total_order_price"`
	TotalOrderWeight float64 `json:"total_order_weight"`
	OrderStatus      string  `json:"order_status"`
	WarehouseCode    string  `json:"warehouse_code"`
	Version          string  `json:"version"`
	IngestionTime    int64   `json:"ingestion_time"`
	Timestamp        int64   `json:"timestamp"`
}

type SearchServiceabilityCellSufficiency struct {
	CityID           int32                                   `json:"city_id"`
	IngestionTime    int64                                   `json:"ingestion_time"`
	Timestamp        int64                                   `json:"timestamp"`
	DeliveryCellId   string                                  `json:"delivery_cell_id"`
	CheckPoint       string                                  `json:"checkpoint"`
	RequestId        string                                  `json:"request_id"`
	SourceType       string                                  `json:"source_type"`
	StatusCodeCounts []*SearchServiceabilityStatusCodeCounts `json:"status_code_counts"`
}

type SearchServiceabilityStatusCodeCounts struct {
	ResCount   int32 `json:"res_count"`
	StatusCode int32 `json:"status_code"`
}

type WebOrderEvents struct {
	Source        string `json:"source"`
	CityID        int32  `json:"city_id"`
	EventName     string `json:"event_name"`
	IngestionTime int64  `json:"ingestion_time"`
	Timestamp     int64  `json:"timestamp"`
}

type EtaTrackingV2 struct {
	AcceptanceTime        float32 `json:"acceptance_time"`
	Buffer                float64 `json:"buffer"`
	Category              string  `json:"category"`
	DeviceID              string  `json:"device_id"`
	DropDistance          float32 `json:"drop_distance"`
	DropEta               float64 `json:"drop_eta"`
	Kpt                   float64 `json:"kpt"`
	KptHandoverTime       float64 `json:"kpt_handover_time"`
	Location              string  `json:"location"`
	MetadataCallerService string  `json:"metadata__caller_service"`
	MetadataEstablishment string  `json:"metadata__establishment"`
	MetadataIsLogs        string  `json:"metadata__is_logs"`
	MetadataKptBuffer     string  `json:"metadata__kpt_buffer"`
	MetadataRdtBuffer     string  `json:"metadata__rdt_buffer"`
	OrderID               int64   `json:"order_id"`
	OrderStatus           string  `json:"order_status"`
	PickupDistance        float32 `json:"pickup_distance"`
	PickupEta             float32 `json:"pickup_eta"`
	PingTimestamp         int64   `json:"ping_timestamp"`
	Polyline              string  `json:"polyline"`
	Rat                   float64 `json:"rat"`
	RawEta                float64 `json:"raw_eta"`
	ResID                 int     `json:"res_id"`
	ResLatitude           float64 `json:"res_latitude"`
	ResLongitude          float64 `json:"res_longitude"`
	RiderLatitude         float64 `json:"rider_latitude"`
	RiderLongitude        float64 `json:"rider_longitude"`
	SessionID             string  `json:"session_id"`
	SmoothenEta           float64 `json:"smoothen_eta"`
	Source                string  `json:"source"`
	TimeToReach           float32 `json:"time_to_reach"`
	Timestamp             string  `json:"timestamp"`
	UserAgent             string  `json:"user_agent"`
	UserID                string  `json:"user_id"`
	UserLatitude          float64 `json:"user_latitude"`
	UserLongitude         float64 `json:"user_longitude"`
}

type MoviesEvents struct {
	Source                    string  `json:"source"`
	DeviceID                  string  `json:"device_id"`
	SessionID                 string  `json:"session_id"`
	UserID                    string  `json:"user_id"`
	UserAgent                 string  `json:"user_agent"`
	Timestamp                 int64   `json:"timestamp"`
	IngestionTime             int64   `json:"ingestion_time"`
	AppTrackingAppType        string  `json:"app_tracking__app_type"`
	AppTrackingAppVersion     string  `json:"app_tracking__app_version"`
	AppTrackingOsVersion      string  `json:"app_tracking__os_version"`
	AppTrackingDeviceName     string  `json:"app_tracking__device_name"`
	AppTrackingEventType      string  `json:"app_tracking__event_type"`
	PageNavigationClickTime   string  `json:"page_navigation__click_time"`
	PageNavigationFirstRender string  `json:"page_navigation__first_render"`
	PageNavigationFullRender  string  `json:"page_navigation__full_render"`
	PagePaintApi              string  `json:"page_paint__api"`
	PagePaintComponent        string  `json:"page_paint__component"`
	PagePaintTotal            string  `json:"page_paint__total"`
	PaymentDetailsMethodType  string  `json:"payment_details__method_type"`
	PaymentDetailsDisplayName string  `json:"payment_details__display_name"`
	CinemaDistance            float64 `json:"cinema_distance"`
	ErrorCode                 int64   `json:"error_code"`
	ErrorMessage              string  `json:"error_message"`
	EventName                 string  `json:"event_name"`
	EventType                 string  `json:"event_type"`
	Location                  int32   `json:"location"`
	MovieId                   string  `json:"movie_id"`
	OrderId                   string  `json:"order_id"`
	Page                      string  `json:"page"`
	PreviousPage              string  `json:"previous_page"`
	PromoApplied              string  `json:"promo_applied"`
	PromoCountAvailable       float64 `json:"promo_count_available"`
	PromoUpfront              string  `json:"promo_upfront"`
	TheatreId                 string  `json:"theatre_id"`
	TicketQuantity            int32   `json:"ticket_quantity"`
	ToastMessageText          string  `json:"toast_message_text"`
}
type CompositeEvents struct {
	ConsumerOrderID                                                   int64   `json:"consumer_order_id"`
	ConsumerOrderCurrency                                             string  `json:"consumer_order_currency"`
	ConsumerOrderResID                                                int64   `json:"consumer_order_res_id"`
	ConsumerOrderAppVersion                                           string  `json:"consumer_order_app_version"`
	ConsumerOrderState                                                string  `json:"consumer_order_state"`
	ConsumerOrderUserID                                               int64   `json:"consumer_order_user_id"`
	ConsumerOrderDeliveryMode                                         string  `json:"consumer_order_delivery_mode"`
	ConsumerOrderTestOrder                                            bool    `json:"consumer_order_test_order"`
	ConsumerOrderPosVersion                                           string  `json:"consumer_order_pos_version"`
	ConsumerOrderVendorID                                             int64   `json:"consumer_order_vendor_id"`
	ConsumerOrderTotalQuantity                                        int32   `json:"consumer_order_total_quantity"`
	ConsumerOrderTotalCost                                            float64 `json:"consumer_order_total_cost"`
	ConsumerOrderTotalUnitCost                                        float64 `json:"consumer_order_total_unit_cost"`
	ConsumerOrderCancelReasonID                                       int32   `json:"consumer_order_cancel_reason_id"`
	ConsumerOrderCancelledAt                                          int64   `json:"consumer_order_cancelled_at"`
	ConsumerOrderIsOtof                                               bool    `json:"consumer_order_is_otof"`
	ConsumerOrderCreatedAt                                            int64   `json:"consumer_order_created_at"`
	ConsumerOrderCreatedISTHour                                       int     `json:"consumer_order_created_ist_hour"`
	ConsumerOrderCreatedISTDay                                        int     `json:"consumer_order_created_ist_day"`
	ConsumerOrderCreatedISTISOWeek                                    int     `json:"consumer_order_created_ist_isoweek"`
	ConsumerOrderCreatedISTMonth                                      int     `json:"consumer_order_created_ist_month"`
	ConsumerOrderCreatedISTYear                                       int     `json:"consumer_order_created_ist_year"`
	ConsumerOrderCreatedISTDt                                         string  `json:"consumer_order_created_ist_dt"`
	ConsumerOrderMealtime                                             string  `json:"consumer_order_mealtime"`
	ConsumerOrderDiscountAppliedFlag                                  int     `json:"consumer_order_discount_applied_flag"`
	ConsumerOrderUserProfileSelectedPrimaryId                         string  `json:"consumer_order_user_profile_selected_primary_id"`
	ConsumerOrderUserProfileSelectedType                              string  `json:"consumer_order_user_profile_selected_type"`
	ConsumerOrderRating                                               int     `json:"consumer_order_rating"`
	ConsumerOrderTotalAmountTotalCost                                 float64 `json:"consumer_order_total_amount_total_cost"`
	ConsumerOrderFulfilmentLogisticsShipmentDestinationCityId         uint64  `json:"consumer_order_fulfilment_logistics_shipment_destination_city_id"`
	ConsumerOrderFulfilmentLogisticsShipmentSourceCityId              uint64  `json:"consumer_order_fulfilment_logistics_shipment_source_city_id"`
	ConsumerOrderFulfilmentLogisticsFulfilmentType                    string  `json:"consumer_order_fulfilment_logistics_fulfilment_type"`
	ConsumerOrderFulfilmentFulfilmentType                             string  `json:"consumer_order_fulfilment_fulfilment_type"`
	ConsumerOrderIsInstantOrder                                       bool    `json:"consumer_order_is_instant_order"`
	ConsumerOrderIsInterCityOrder                                     bool    `json:"consumer_order_is_intercity_order"`
	ConsumerOrderPaymentsPrimaryPaymentPaymentMethodPaymentMethodType string  `json:"consumer_order_payments_primary_payment_payment_method_payment_method_type"`
	ConsumerOrderFulfilmentLogisticsLogisticsPartnerId                int32   `json:"consumer_order_fulfilment_logistics_logistics_partner_id"`
	ConsumerOrderDeliverySpeed                                        string  `json:"consumer_order_delivery_speed"`
	MerchantOrderID                                                   int64   `json:"merchant_order_id"`
	MerchantOrderState                                                string  `json:"merchant_order_state"`
	MerchantOrderRejectReasonID                                       int32   `json:"merchant_order_reject_reason_id"`
	MerchantOrderUserID                                               int64   `json:"merchant_order_user_id"`
	MerchantOrderDeliveryMode                                         string  `json:"merchant_order_delivery_mode"`
	MerchantOrderDeliveryTime                                         int32   `json:"merchant_order_delivery_time"`
	MerchantOrderResID                                                int64   `json:"merchant_order_res_id"`
	MerchantOrderCreatedAt                                            int64   `json:"merchant_order_created_at"`
	MerchantOrderForMarkedStatus                                      string  `json:"merchant_order_for_marked_status"`
	MerchantOrderKptDelaySecs                                         int32   `json:"merchant_order_kpt_delay_secs"`
	MerchantOrderTotalCost                                            float64 `json:"merchant_order_total_cost"`
	MerchantOrderRejectReason                                         string  `json:"merchant_order_reject_reason"`
	MerchantOrderCreatedISTHour                                       int     `json:"merchant_order_created_ist_hour"`
	MerchantOrderCreatedISTDay                                        int     `json:"merchant_order_created_ist_day"`
	MerchantOrderCreatedISTISOWeek                                    int     `json:"merchant_order_created_ist_isoweek"`
	MerchantOrderCreatedISTMonth                                      int     `json:"merchant_order_created_ist_month"`
	MerchantOrderCreatedISTYear                                       int     `json:"merchant_order_created_ist_year"`
	MerchantOrderCreatedISTDt                                         string  `json:"merchant_order_created_ist_dt"`
	MerchantOrderMealtime                                             string  `json:"merchant_order_mealtime"`
	MerchantOrderStateHistoryReadyTime                                int64   `json:"merchant_order_state_history_ready_time"`
	MerchantOrderStateHistoryAcceptedTime                             int64   `json:"merchant_order_state_history_accepted_time"`
	LogisticsOrderID                                                  int64   `json:"logistics_order_id"`
	LogisticsPartnerID                                                int32   `json:"logistics_partner_id"`
	LogisticsOrderState                                               string  `json:"logistics_order_state"`
	LogisticsOrderCreatedAt                                           int64   `json:"logistics_order_created_at"`
	LogisticsOrderUpdatedAt                                           int64   `json:"logistics_order_updated_at"`
	LogisticsOrderStateHistoryArrivedTime                             int64   `json:"logistics_order_state_history_arrived_time"`
	LogisticsOrderStateHistoryPickedTime                              int64   `json:"logistics_order_state_history_picked_time"`
	UpdatedAt                                                         string  `json:"updated_at"`
	IngestionTime                                                     int64   `json:"ingestion_time"`
	Revision                                                          int64   `json:"revision"`
}

type ProtoConvertorHelper struct {
	ProtoMessage           *protoreflect.ProtoMessage
	ProtoMessageDescriptor *protoreflect.MessageDescriptor
	Options                *annotations.JumboMessageOptions
}

func NewProtoConvertorHelper(msgType protoreflect.MessageType) *ProtoConvertorHelper {
	descriptor := msgType.Descriptor()
	msg := msgType.New().Interface()
	helper := ProtoConvertorHelper{ProtoMessage: &msg, ProtoMessageDescriptor: &descriptor}
	if extensions, ok := proto.GetExtension(descriptor.Options(), annotations.E_MsgOpts).(*annotations.JumboMessageOptions); ok && extensions != nil {
		helper.Options = extensions
	}
	return &helper
}

func (p *ProtoConvertorHelper) GetTenantTable() string {
	if p.Options == nil {
		return ""
	}
	var tenant string
	if p.Options.GetTenant() != "" {
		tenant = p.Options.GetTenant()
	} else if len(p.Options.GetTenants()) > 0 {
		tenant = p.Options.GetTenants()[0]
	}
	tableName := p.Options.Table
	return tenant + "." + tableName
}

func (p *ProtoConvertorHelper) HasOptions() bool {
	if p.Options == nil {
		return false
	}
	return p.Options.Table != "" && (p.Options.Tenant != "" || len(p.Options.GetTenants()) > 0)
}

type TimeFields struct {
	DT      string
	Hour    int
	Day     int
	ISOWeek int
	Month   int
	Year    int
}

type LocationInfo struct {
	UserDefinedLatitude  *float64 `json:"user_defined_latitude"`
	UserDefinedLongitude *float64 `json:"user_defined_longitude"`
	CurrentLongitude     *float64 `json:"current_longitude"`
	CurrentLatitude      *float64 `json:"current_latitude"`
}

type JumboV2TransformedEvent struct {
	Payload []byte
	Key     []byte
	Table   string
	Topic   string
	Broker  string
}
type appPayload struct {
	Header  map[string]interface{} `json:"header"`
	Payload map[string]interface{} `json:"payload"`
}

type AppPayloadWrapper struct {
	AppPayload []appPayload `json:"app_payload"`
}

type DDTSalienceTrackingEvents struct {
	Source              string  `json:"source"`
	SessionID           string  `json:"session_id"`
	UserID              string  `json:"user_id"`
	UserAgent           string  `json:"user_agent"`
	Timestamp           int64   `json:"timestamp"`
	IngestionTime       int64   `json:"ingestion_time"`
	Location            int32   `json:"location"`
	SearchID            string  `json:"search_id"`
	TraceID             string  `json:"trace_id"`
	ResID               uint32  `json:"res_id"`
	ArrivalAfterKPT     float64 `json:"arrival_after_kpt"`
	Buffer              float64 `json:"buffer"`
	BaseDDT             float64 `json:"base_ddt"`
	Rank                int32   `json:"rank"`
	SAStatusCode        int32   `json:"sa_status_code"`
	EFOSAStatusCode     int32   `json:"efo_sa_status_code"`
	ApproxSAETA         float64 `json:"approx_sa_eta"`
	RestaurantLongitude float64 `json:"restaurant__longitude"`
	RestaurantLatitude  float64 `json:"restaurant__latitude"`
	RestaurantCellID    string  `json:"restaurant__cell_id"`
	UserLongitude       float64 `json:"user__longitude"`
	UserLatitude        float64 `json:"user__latitude"`
	UserCellID          string  `json:"user__cell_id"`
}

type EventsTicket struct {
	Source            string `json:"source"`
	DeviceID          string `json:"device_id"`
	SessionID         string `json:"session_id"`
	UserID            string `json:"user_id"`
	Timestamp         int64  `json:"timestamp"`
	IngestionTime     int64  `json:"ingestion_time"`
	Location          int32  `json:"location"`
	DistrictEventID   string `json:"district_event_id"`
	ShowID            string `json:"show_id"`
	VenueID           string `json:"venue_id"`
	GroupID           string `json:"group_id"`
	PhaseGroupID      string `json:"phase_group_id"`
	ItemID            string `json:"item_id"`
	TicketType        string `json:"ticket_type"`
	TicketID          string `json:"ticket_id"`
	OrderID           string `json:"order_id"`
	Category          string `json:"category"`
	TicketBasePrice   int32  `json:"ticket_base_price"`
	Discount          int32  `json:"discount"`
	Action            string `json:"action"`
	TicketBasePriceV2 int32  `json:"ticket_base_price_v2"`
	UtmSource         string `json:"utm_source"`
	UtmMedium         string `json:"utm_medium"`
	UtmCampaign       string `json:"utm_campaign"`
}

type EventsBusinessTrackingEvents struct {
	Source            string `json:"source"`
	DeviceID          string `json:"device_id"`
	SessionID         string `json:"session_id"`
	UserID            string `json:"user_id"`
	UserAgent         string `json:"user_agent"`
	Timestamp         int64  `json:"timestamp"`
	IngestionTime     int64  `json:"ingestion_time"`
	Location          int32  `json:"location"`
	EventName         string `json:"event_name"`
	MainEventID       string `json:"main_event_id"`
	MainEventCategory string `json:"main_event_category"`
	Page              string `json:"page"`
	UtmSource         string `json:"utm_source"`
	UtmMedium         string `json:"utm_medium"`
}

type DistrictHomepageTrackingEvents struct {
	Source        string `json:"source"`
	DeviceID      string `json:"device_id"`
	SessionID     string `json:"session_id"`
	UserID        string `json:"user_id"`
	Timestamp     int64  `json:"timestamp"`
	IngestionTime int64  `json:"ingestion_time"`
	Location      int32  `json:"location"`
	PageType      string `json:"page_type"`
	Section       string `json:"section"`
	Action        string `json:"action"`
	EntityId      string `json:"entity_id"`
	EntityType    string `json:"entity_type"`
}

type EventsResStatusUpdateEvents struct {
	ResId                      int64  `json:"res_id"`
	EventType                  string `json:"event_type"`
	DeliveryStatus             int32  `json:"delivery_status"`
	IsDeliveryStatusUpdated    bool   `json:"is_delivery_status_updated"`
	AdminActiveStatus          int32  `json:"admin_active_status"`
	IsAdminActiveStatusUpdated bool   `json:"is_admin_active_status_updated"`
}
