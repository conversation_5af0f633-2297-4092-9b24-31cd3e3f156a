{"job_groups": {"default": {"tables": {"jevent": {"event_name_field": "ename", "allowed_events": [{"producer": "default", "topic_events_map": [{"topic": "online_ordering.jumbo_app_events", "events": ["O2CartPageLoaded", "O2CartPromoTapped", "O2CartPromoApplied", "OrderPlaced", "ResFeaturesTapped", "opened_app", "BACKEND_O2MenuFetched", "BACKEND_O2CartPageLoaded", "O2MenuViewed", "Backend_OrderPlaced", "Backend_JumboEnameO2PhoneVerificationStart", "Backend_JumboEnameO2PhoneVerificationEnd", "Backend_O2CartPromoTapped", "Backend_opened_app", "livestream_deeplink_triggered", "livestream_snippet_tapped", "livestream_fullscreen_view_dismissed", "livestream_restaurant_didStartStreaming", "livestream_restaurant_didEndStreaming", "livestream_snippet_served", "livestream_snippet_viewed", "Backend_O2CartPromoApplied", "rider_cod_blocked", "O2CartCheckoutButtonTapped", "go_offline_tap", "item_stock_toggle", "festive_banner", "PromoPagePromoImpression", "merchant_rider_rbt_data", "ResScannedMenusTapped", "TabSelected", "ResOverviewPageLoaded", "Fetched_GST_clicked", "Pan_Gst_Api_Called", "Pan_Gst_Api_Returned_Gst", "page_rendered", "register_your_restaurant_click", "claim_now_click", "create_your_res_page_click", "Redirection_from_Profile_page", "next_click", "User_Moved_to_O2_Form_Create_Your_Res", "send_contract_click", "User_Accepted_O2_Contract", "Owner_Email_Otp_Sent_Successfully", "Owner_Email_Otp_Sent_Unsuccessfully", "Owner_Email_Otp_Verified_Successfully", "CART_ERROR_DIALOG_SHOWN", "NO_CONTENT_VIEW_ERROR_SHOWN", "NO_CONTENT_VIEW_RETRY_CLICKED", "Dev_no_deeplink_fired", "AppAPIFailure", "ZPLTabLoaded", "ZPLTeamLogoImpression", "ZPLTeamLogoSelected", "ZPLTeamConfirmed", "ZPLSubTabVisible", "ZPLSubTabLoaded", "ZPLInviteButtonImpression", "ZPLInviteButtonClicked", "ZPLUpdateMyAppClicked", "ZPLNotifyMeClicked", "ZPLBannerImpression", "ZPLMatchCardImpression", "ZPLWinningTeamSelected", "ZPLWinningTeamConfirmed", "ZPLRulesFAQsClicked", "ZPLSeeHistoryClicked", "ZPLBragButtonClicked", "ZPLUnlockCouponClicked", "ZPLSeeallCouponsClicked", "ZPLCouponPageLoaded", "ZPLCouponPageCouponImpression", "ZPLCouponTapped", "ZPLScratchCardImpression", "ZPLScratchCardScratched", "ZPLCouponDetailsViewed", "ProPlanPageLoadSuccess", "ProPlanPageBuyProTap", "GoldCartPaySuccess", "pro_crystal_v1_section_viewed", "pro_crystal_v1_section_tapped", "O2MenuProAddToCartDirect", "O2MenuProAddToCart", "GoldCartLoaded", "GoldCartPageOpen", "AerobarImpression", "AerobarTapped", "api_timeout_error", "mx_req_fail_meta", "HomeLocationLoaded", "HomeLocationRequest", "InstantDishImpression", "InstantMenuCardSingleTap", "InstantMenuCardDoubleTap", "InstantItemQuantityModify", "InstantAddonsLoaded", "InstantAddonsSkipped", "InstantAddonsDismissed", "InstantViewMenuButtonTapped", "InstantBannerImpression", "InstantBannerTap", "gold_special_cart_pay_success", "HPPayButtonClicked", "gold_special_cart_load_success", "BottomButtonClicked", "gold_special_cart_pay_tap", "gold_special_cart_continue_tapped", "zpay_payment_status", "dos_reported", "zcredits_issued", "instant_tracking_edt", "O2CartDcPackSnippetViewed", "DcPackPurchaseSuccess", "O2DcPackAddToCart", "O2DcPackInterstitialAddToCart", "O2DcPackInterstitialViewed", "O2DcPackInterstitialDismiss", "O2DcPackInterstitialPlaceOrder", "HomePageLoaded", "TicketTransactionStatusSuccess", "TicketTransactionStatusFailure", "GoldIntroPageSuccessImpression", "GoldIntroCardButtonImpression", "GoldIntroCardButtonTap", "O2CrystalGoldCartCheckout", "GoldIntroSectionImpression", "GoldIntroSectionTapped", "GoldIntroNotifyButtonImpression", "GoldIntroNotifyButtonTap", "GoldO2CartSnippetImpression", "GoldO2CartSnippetTap", "GoldBottomSheetImpression", "GoldBottomSheetAddGoldTapped", "GoldWelcomeConfettiImpression", "GoldCartEligibleUserNoBenefit", "GoldUpgradedLetterPageLoadSuccess", "CheckoutYourMembershipTap", "GoldMembershipRefundSectionImpression", "GoldMembershipRefundSectionTap", "GoldRefundPageLoadSuccess", "GoldRefundClaimButtonImpression", "GoldRefundClaimButtonTap", "AddPhoneNumberSnippetImpression", "AddPhoneNumberSnippetTap", "edit_phone_number_tapped", "verify_email_with_otp", "send_otp_to_new_phone", "active_order_error_view_seen", "GoldIntroBenefitsImpression", "O2CrystalMembershipSnippetViewed", "O2CrystalMembershipSnippetTapped", "deals_ticket_purchase", "TRSlotBookingSuccess", "GDCPaymentSuccess", "dotd_ticket_purchase", "TRSlotCartPageLoaded", "PackagesSuccessPageLoaded", "DiningMenuViewed", "TRSlotBookingPageReservationDetailsSelected", "O2CartChangePaymentMethodTapped", "LocationAddAddressAPISuccess", "LocationAddressScreenImpression", "LocationPageViewed", "O2DishAdded", "ResClosingFloatingViewImpression", "MakeOrderCallStarted", "MakeOrderCallSuccess", "MakeOrderCallFailed", "SdkMakePaymentCallStarted", "SdkMakePaymentCallSuccess", "SdkMakePaymentCallFailure", "SdkMakePaymentCallPending", "CartPollingStarted", "CartPollingSuccess", "CartPollingFailed", "CompletePaymentCallFailed", "DV_TOKEN_INIT_FAILED", "ResPageGetDirectionsTapped", "ResPageCallButtonTapped"], "format": "string"}, {"topic": "online_ordering.jumbo_app_events", "events": ["PARSING_EXCEPTION"], "format": "string", "filters": {"ename": ["PARSING_EXCEPTION"], "var3": ["NETWORK"]}}, {"topic": "merchant.jumbo_app_events", "events": ["accept_order_click", "mx-newOrder-rendered", "new-order-socket-event", "dashboard-load-failed", "sidebar_item_impression", "dashboard-load-status", "order-update-socket-event", "disconnected-socket-event", "connected-socket-event", "res_status_socket_v2", "remote_intent_received"], "format": "string"}, {"topic": "online_ordering.jumbo_promo_app_events", "events": ["listing_offers_impression", "O2MenuSuperOfferViewed", "O2MenuViewed"], "format": "string"}, {"topic": "online_ordering.payment_sdk_retry_screen_failure_messages", "events": ["SDKRetryPaymentScreenFailureMessage"], "format": "string", "transformations": {"track_id": "var1", "failure_message": "var2", "service_type": "var5", "version_number": "var6", "ename": "ename"}, "filters": {"ename": ["SDKRetryPaymentScreenFailureMessage"]}}]}, {"producer": "offlineMsk", "topic_events_map": [{"topic": "zomato.subscription-service.device-banner-tracking-events", "events": ["DeviceLimitingBottomSheetImpression", "DeviceLimitingBottomSheetButtonTap"], "format": "proto"}]}, {"producer": "offlineMsk", "topic_events_map": [{"topic": "zomato.subscription-service.tracking-events", "events": ["MembershipExpiredBottomSheetImpression", "GoldIntermediateMilestonePopupImpression", "GoldDefaultMilestonePopupImpression", "GoldUfrExtraFreeDaysPopupImpression", "GoldExpiryExtraFreeDaysPopupImpression", "AutoAddMembershipBottomSheetImpression", "CitySaleBottomSheetImpression", "HomePillLottieImpression", "NewUserBottomSheetImpression", "FlashSaleBottomSheetImpression", "HomePageTopAnimationImpression", "DCPackPurchaseNudgeTabbedHomeImpression", "GoldMembershipPageSuccessImpression", "LapsedUserFreeTrialBottomSheetImpression", "GoldUfrBannerImpression", "O2DCPackPageLoaderViewed", "O2GoldPageLoaderViewed", "O2GoldPageLoaderLostSavingsViewed", "POISaleBottomSheetImpression"], "format": "proto"}]}]}, "zsearch_events_log": {"event_name_field": "action", "allowed_events": [{"producer": "default", "topic_events_map": [{"topic": "online_ordering.jumbo_app_events", "events": ["impression", "tap"], "format": "string", "transformations": {"search_id": "var1", "entity_type": "var2", "section": "var3", "entity_id": "var4", "page_type": "var5", "rank": "var6", "action": "ename"}, "filters": {"action": ["impression", "tap"], "entity_id": ["Gourmet", "gourmet_banner_v3", "gourmet_slim_banner", "163189", "festive_banner", "blitz_campaign_banner", "dosaituesdaystestzomans", "healthy<PERSON><PERSON><PERSON><PERSON><PERSON>", "orientalwednesdaystestzomans", "pizzafridaystestzomans", "biryanisaturdaystestzomans", "italianthursdaystestzomans", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "New_users_Promo_banner", "default_promo_banner", "home_style_meals"], "page_type": ["o2_search"]}}]}]}, "jadtracking": {"event_name_field": "action", "allowed_events": [{"producer": "default", "topic_events_map": [{"topic": "adtech.campaign_adimpressions", "events": ["impression"], "format": "string"}, {"topic": "adtech.campaign_adclicks", "events": ["click"], "format": "string"}]}]}, "subscription_tracking_events": {"event_name_field": "event_type", "allowed_events": [{"producer": "offlineMsk", "topic_events_map": [{"topic": "zomato.subscription-service.tracking-events", "events": ["EVENT_TYPE_GOLD_LITE_PURCHASE_NUDGE_BOTTOMSHEET_IMPRESSION", "EVENT_TYPE_GOLD_MEMBERSHIP_EXPIRED_V2_BOTTOMSHEET_IMPRESSION", "EVENT_TYPE_DC_PACKS_UPGRADED_BOTTOMSHEET_IMPRESSION", "EVENT_TYPE_DISTANCE_EXTENSION_BOTTOMSHEET_IMPRESSION", "EVENT_TYPE_NOTIFY_PROMO_CODE_BUTTON_TAP", "EVENT_TYPE_HOME_PILL_LOTTIE_IMPRESSION", "EVENT_TYPE_EXPIRY_RENEWAL_LOST_SAVINGS_BANNER_IMPRESSION", "EVENT_TYPE_EXPIRY_RENEWAL_SAVINGS_BANNER_IMPRESSION", "EVENT_TYPE_EXPIRY_RENEWAL_NEUTRAL_BANNER_IMPRESSION"], "format": "proto"}]}]}, "live_order_service_events": {"event_name_field": "event_name", "allowed_events": [{"producer": "offlineMsk", "topic_events_map": [{"topic": "zomato.subscription-service.tracking-events", "events": ["O2CrystalCarouselImpression"], "format": "proto"}]}]}, "raw_driver_events": {"event_name_field": "event_name", "allowed_events": [{"producer": "default", "topic_events_map": [{"topic": "flash.raw_driver_events", "events": ["shift_booking_success_shown", "shift_cancellation_success"], "format": "string"}]}]}, "eta_tracking": {"event_name_field": "category", "allowed_events": [{"producer": "default", "topic_events_map": [{"topic": "flash.eta_tracking", "events": ["ddtv3", "pingwise", "edt", "ix_ddt", "ix_edt"], "format": "string"}]}]}}, "v2_format_tables": ["promo_events", "app_request_metrics", "restaurant_status_active_log", "logistics_order_audit_events", "driver_pings", "driver_slot_events", "reward_credit_events", "pro_membership_tracking", "app_error_metrics", "unified_chat_events", "app_ssl_pinning_metrics", "weather_station_events", "chat_refund_metrics", "driver_cash_limits", "subscription_events", "hermes_cell_speed_stats", "app_auth_metrics", "payments_enriched_events", "menu_tool_update_tracking", "zomato_polls_tracking", "merchant_relay_events", "driver_wallets", "cod_realtime_actions", "hermes_driver_match_stats", "lifeline_events", "video_message_events", "rider_trip_earnings", "auth_sdk_logs"], "jumbo_streaming_tables_package_prefix": "jumbo.eventregistry"}, "blinkit": {"tables": {"jevent": {"event_name_field": "ename", "allowed_events": [{"producer": "blinkit", "topic_events_map": [{"topic": "blinkit.app_events", "events": ["NO_CONTENT_VIEW_ERROR_SHOWN", "NO_CONTENT_VIEW_RETRY_CLICKED"], "format": "string"}, {"topic": "blinkit.app_events", "events": ["PARSING_EXCEPTION"], "format": "string", "filters": {"ename": ["PARSING_EXCEPTION"], "var3": ["NETWORK"]}}]}]}}, "v2_format_tables": ["app_request_metrics", "app_error_metrics", "app_ssl_pinning_metrics", "monet_events", "blinkit_identify_events", "serviceability_info_events", "serviceability_checkouts_block_info_events", "deeplink_events", "storeops_app_events"], "jumbo_v2_consumer_topics": {"default": "blinkit_warpstream"}}, "district": {"tables": {"jadtracking": {"event_name_field": "action", "allowed_events": [{"producer": "default", "topic_events_map": [{"topic": "adtech.campaign_adimpressions", "events": ["impression"], "format": "string"}, {"topic": "adtech.campaign_adclicks", "events": ["click"], "format": "string"}]}]}}}, "nugget": {"v2_format_tables": ["app_request_metrics", "app_error_metrics", "app_ssl_pinning_metrics", "unified_chat_events", "one_support_events"], "tables": {}}}}