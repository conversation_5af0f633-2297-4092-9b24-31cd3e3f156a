{"enrichments": [{"input_topic": "online_ordering.jumbo_app_events", "output_topic": "staging.enriched.online_ordering.jumbo_app_events", "event_time_column": "timestamp", "event_time_format": "2006-01-02T15:04:05Z07:00", "enrichment": [{"enriched_field": "city_name", "lookup_key": "zomato4:city:", "lookup_field": "name", "base_field": "location", "field_type": "cache"}, {"enriched_field": "country_id", "lookup_key": "zomato4:city:", "lookup_field": "country_id", "base_field": "location", "field_type": "cache"}, {"enriched_field": "country_name", "lookup_key": "zomato4:city:", "lookup_field": "country_name", "base_field": "location", "field_type": "cache"}, {"enriched_field": "app_version", "lookup_key": "", "lookup_field": "", "base_field": "user_agent", "field_type": "derived"}, {"enriched_field": "last_order_date", "lookup_key": "user:uid_dt:", "lookup_field": "last_dt", "base_field": "user_id", "field_type": "cache"}, {"enriched_field": "ps_segment", "lookup_key": "user:uid_dt:", "lookup_field": "ps_seg", "base_field": "user_id", "field_type": "cache"}]}]}